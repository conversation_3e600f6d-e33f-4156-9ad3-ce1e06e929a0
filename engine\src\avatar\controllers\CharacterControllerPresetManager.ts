/**
 * 角色控制器预设管理器
 * 用于管理角色控制器的预设和模板
 */
import { EventEmitter, type EventCallback } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import { AdvancedCharacterControllerConfig } from './AdvancedCharacterController';
import { ActionData } from './ActionControlSystem';

/**
 * 控制器预设类型
 */
export enum ControllerPresetType {
  /** 基础预设 */
  BASIC = 'basic',
  /** 高级预设 */
  ADVANCED = 'advanced',
  /** 特殊预设 */
  SPECIAL = 'special',
  /** 自定义预设 */
  CUSTOM = 'custom',
  /** 第一人称预设 */
  FIRST_PERSON = 'first_person',
  /** 第三人称预设 */
  THIRD_PERSON = 'third_person',
  /** 飞行预设 */
  FLYING = 'flying',
  /** 游泳预设 */
  SWIMMING = 'swimming',
  /** 驾驶预设 */
  DRIVING = 'driving',
  /** 战斗预设 */
  COMBAT = 'combat',
  /** 潜行预设 */
  STEALTH = 'stealth',
  /** 攀爬预设 */
  CLIMBING = 'climbing',
  /** 跑酷预设 */
  PARKOUR = 'parkour',
  /** 舞蹈预设 */
  DANCING = 'dancing',
  /** 物理交互预设 */
  PHYSICS_INTERACTION = 'physics_interaction',
  /** 环境感知预设 */
  ENVIRONMENT_AWARE = 'environment_aware'
}

/**
 * 控制器预设数据
 */
export interface ControllerPresetData {
  /** 预设ID */
  id: string;
  /** 预设名称 */
  name: string;
  /** 预设描述 */
  description: string;
  /** 预设类型 */
  type: ControllerPresetType;
  /** 预设标签 */
  tags: string[];
  /** 控制器配置 */
  config: Partial<AdvancedCharacterControllerConfig>;
  /** 默认动作 */
  defaultActions?: ActionData[];
  /** 预设缩略图 */
  thumbnail?: string;
  /** 作者 */
  author?: string;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
}

/**
 * 控制器模板参数
 */
export interface ControllerTemplateParameter {
  /** 参数ID */
  id: string;
  /** 参数名称 */
  name: string;
  /** 参数描述 */
  description: string;
  /** 参数类型 */
  type: 'number' | 'boolean' | 'string' | 'vector' | 'color' | 'enum';
  /** 默认值 */
  defaultValue: any;
  /** 最小值（数值类型） */
  min?: number;
  /** 最大值（数值类型） */
  max?: number;
  /** 步长（数值类型） */
  step?: number;
  /** 枚举选项（枚举类型） */
  options?: { value: any; label: string }[];
  /** 是否必需 */
  required?: boolean;
  /** 分组 */
  group?: string;
}

/**
 * 控制器模板数据
 */
export interface ControllerTemplateData {
  /** 模板ID */
  id: string;
  /** 模板名称 */
  name: string;
  /** 模板描述 */
  description: string;
  /** 模板类型 */
  type: ControllerPresetType;
  /** 模板标签 */
  tags: string[];
  /** 基础控制器配置 */
  baseConfig: Partial<AdvancedCharacterControllerConfig>;
  /** 模板参数 */
  parameters: ControllerTemplateParameter[];
  /** 默认动作 */
  defaultActions?: ActionData[];
  /** 模板缩略图 */
  thumbnail?: string;
  /** 作者 */
  author?: string;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
}

/**
 * 角色控制器预设管理器配置
 */
export interface CharacterControllerPresetManagerConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 预设路径 */
  presetsPath?: string;
  /** 是否自动加载预设 */
  autoLoadPresets?: boolean;
  /** 是否使用本地存储 */
  useLocalStorage?: boolean;
}

/**
 * 角色控制器预设管理器
 */
export class CharacterControllerPresetManager {
  /** 单例实例 */
  private static instance: CharacterControllerPresetManager;

  /** 预设映射 */
  private presets: Map<string, ControllerPresetData> = new Map();

  /** 模板映射 */
  private templates: Map<string, ControllerTemplateData> = new Map();

  /** 配置 */
  private config: CharacterControllerPresetManagerConfig;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: CharacterControllerPresetManagerConfig = {
    debug: false,
    presetsPath: 'presets/controllers',
    autoLoadPresets: true,
    useLocalStorage: true
  };

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 构造函数
   * @param config 配置
   */
  private constructor(config: Partial<CharacterControllerPresetManagerConfig> = {}) {
    this.config = { ...CharacterControllerPresetManager.DEFAULT_CONFIG, ...config };

    // 初始化
    this.initialize();
  }

  /**
   * 获取单例实例
   * @param config 配置
   * @returns 预设管理器实例
   */
  public static getInstance(config?: Partial<CharacterControllerPresetManagerConfig>): CharacterControllerPresetManager {
    if (!CharacterControllerPresetManager.instance) {
      CharacterControllerPresetManager.instance = new CharacterControllerPresetManager(config);
    }
    return CharacterControllerPresetManager.instance;
  }

  /**
   * 初始化
   */
  private initialize(): void {
    if (this.initialized) return;

    if (this.config.debug) {
      Debug.log('角色控制器预设管理器初始化');
    }

    // 加载预设
    if (this.config.autoLoadPresets) {
      this.loadPresets();
    }

    this.initialized = true;
  }

  /**
   * 加载预设
   */
  private loadPresets(): void {
    // 加载内置预设
    this.loadBuiltInPresets();

    // 从本地存储加载
    if (this.config.useLocalStorage) {
      this.loadFromLocalStorage();
    }

    // 从服务器加载
    this.loadFromServer();

    if (this.config.debug) {
      Debug.log(`已加载 ${this.presets.size} 个预设和 ${this.templates.size} 个模板`);
    }
  }

  /**
   * 加载内置预设
   */
  private loadBuiltInPresets(): void {
    // 添加基础角色预设
    this.addPreset({
      id: 'basic_character',
      name: '基础角色控制器',
      description: '标准的角色控制器预设，适用于大多数角色',
      type: ControllerPresetType.BASIC,
      tags: ['基础', '标准', '通用'],
      config: {
        walkSpeed: 2.0,
        runSpeed: 5.0,
        jumpForce: 5.0,
        gravity: 9.8,
        useStateMachine: true,
        useBlendSpace: true
      },
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // 添加高级角色预设
    this.addPreset({
      id: 'advanced_character',
      name: '高级角色控制器',
      description: '高级角色控制器预设，包含更多功能和更好的物理交互',
      type: ControllerPresetType.ADVANCED,
      tags: ['高级', '物理', '交互'],
      config: {
        walkSpeed: 2.0,
        runSpeed: 5.0,
        crouchSpeed: 1.0,
        jumpForce: 5.0,
        gravity: 9.8,
        turnSpeed: 2.0,
        airControl: 0.5,
        usePhysics: true,
        useStateMachine: true,
        useBlendSpace: true,
        useIK: true,
        useEnvironmentAwareness: true
      },
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // 添加第一人称角色预设
    this.addPreset({
      id: 'first_person_character',
      name: '第一人称角色控制器',
      description: '第一人称角色控制器预设，适用于FPS游戏',
      type: ControllerPresetType.FIRST_PERSON,
      tags: ['第一人称', 'FPS', '射击'],
      config: {
        walkSpeed: 2.0,
        runSpeed: 5.0,
        jumpForce: 5.0,
        gravity: 9.8,
        usePhysics: true,
        useStateMachine: true,
        useBlendSpace: false,
        useIK: false,
        useEnvironmentAwareness: false
      },
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // 添加第三人称角色预设
    this.addPreset({
      id: 'third_person_character',
      name: '第三人称角色控制器',
      description: '第三人称角色控制器预设，适用于动作冒险游戏',
      type: ControllerPresetType.THIRD_PERSON,
      tags: ['第三人称', '动作', '冒险'],
      config: {
        walkSpeed: 2.0,
        runSpeed: 5.0,
        jumpForce: 5.0,
        gravity: 9.8,
        usePhysics: true,
        useStateMachine: true,
        useBlendSpace: true,
        useIK: true,
        useEnvironmentAwareness: false
      },
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // 添加飞行角色预设
    this.addPreset({
      id: 'flying_character',
      name: '飞行角色控制器',
      description: '飞行角色控制器预设，适用于飞行场景',
      type: ControllerPresetType.FLYING,
      tags: ['飞行', '空中', '翱翔'],
      config: {
        flySpeed: 10.0,
        turnSpeed: 2.0,
        usePhysics: true,
        useStateMachine: true,
        useBlendSpace: true,
        useIK: true,
        useEnvironmentAwareness: true,
        gravity: 0
      },
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // 添加物理交互角色预设
    this.addPreset({
      id: 'physics_interaction_character',
      name: '物理交互角色控制器',
      description: '物理交互角色控制器预设，适用于需要与物理对象交互的场景',
      type: ControllerPresetType.PHYSICS_INTERACTION,
      tags: ['物理', '交互', '抓取'],
      config: {
        walkSpeed: 2.0,
        runSpeed: 5.0,
        jumpForce: 5.0,
        gravity: 9.8,
        usePhysics: true,
        useStateMachine: true,
        useBlendSpace: true,
        useIK: true,
        useEnvironmentAwareness: true
      },
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // 添加环境感知角色预设
    this.addPreset({
      id: 'environment_aware_character',
      name: '环境感知角色控制器',
      description: '环境感知角色控制器预设，适用于需要对环境做出响应的场景',
      type: ControllerPresetType.ENVIRONMENT_AWARE,
      tags: ['环境', '感知', '响应'],
      config: {
        walkSpeed: 2.0,
        runSpeed: 5.0,
        jumpForce: 5.0,
        gravity: 9.8,
        usePhysics: true,
        useStateMachine: true,
        useBlendSpace: true,
        useIK: true,
        useEnvironmentAwareness: true
      },
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // 添加基础模板
    this.addTemplate({
      id: 'basic_template',
      name: '基础角色模板',
      description: '可自定义的基础角色控制器模板',
      type: ControllerPresetType.BASIC,
      tags: ['基础', '模板', '通用'],
      baseConfig: {
        walkSpeed: 2.0,
        runSpeed: 5.0,
        jumpForce: 5.0,
        gravity: 9.8,
        useStateMachine: true,
        useBlendSpace: true
      },
      parameters: [
        {
          id: 'walkSpeed',
          name: '行走速度',
          description: '角色行走的速度',
          type: 'number',
          defaultValue: 2.0,
          min: 0.5,
          max: 5.0,
          step: 0.1,
          required: true,
          group: '移动'
        },
        {
          id: 'runSpeed',
          name: '跑步速度',
          description: '角色跑步的速度',
          type: 'number',
          defaultValue: 5.0,
          min: 1.0,
          max: 10.0,
          step: 0.1,
          required: true,
          group: '移动'
        },
        {
          id: 'jumpForce',
          name: '跳跃力量',
          description: '角色跳跃的力量',
          type: 'number',
          defaultValue: 5.0,
          min: 1.0,
          max: 10.0,
          step: 0.1,
          required: true,
          group: '移动'
        }
      ],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // 添加物理交互模板
    this.addTemplate({
      id: 'physics_interaction_template',
      name: '物理交互角色模板',
      description: '可自定义的物理交互角色控制器模板',
      type: ControllerPresetType.PHYSICS_INTERACTION,
      tags: ['物理', '交互', '模板'],
      baseConfig: {
        walkSpeed: 2.0,
        runSpeed: 5.0,
        jumpForce: 5.0,
        gravity: 9.8,
        usePhysics: true,
        useStateMachine: true,
        useBlendSpace: true,
        useIK: true,
        useEnvironmentAwareness: true
      },
      parameters: [
        {
          id: 'interactionRange',
          name: '交互范围',
          description: '角色可以与物体交互的最大距离',
          type: 'number',
          defaultValue: 2.0,
          min: 0.5,
          max: 5.0,
          step: 0.1,
          required: true,
          group: '交互'
        },
        {
          id: 'maxForce',
          name: '最大力量',
          description: '角色可以施加的最大力量',
          type: 'number',
          defaultValue: 1000,
          min: 100,
          max: 5000,
          step: 100,
          required: true,
          group: '交互'
        },
        {
          id: 'maxTorque',
          name: '最大扭矩',
          description: '角色可以施加的最大扭矩',
          type: 'number',
          defaultValue: 500,
          min: 50,
          max: 2000,
          step: 50,
          required: true,
          group: '交互'
        },
        {
          id: 'useRagdoll',
          name: '使用布娃娃物理',
          description: '是否启用布娃娃物理效果',
          type: 'boolean',
          defaultValue: true,
          required: false,
          group: '物理'
        },
        {
          id: 'ragdollTransitionTime',
          name: '布娃娃过渡时间',
          description: '从动画到布娃娃物理的过渡时间',
          type: 'number',
          defaultValue: 0.5,
          min: 0.1,
          max: 2.0,
          step: 0.1,
          required: false,
          group: '物理'
        }
      ],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // 添加环境感知模板
    this.addTemplate({
      id: 'environment_aware_template',
      name: '环境感知角色模板',
      description: '可自定义的环境感知角色控制器模板',
      type: ControllerPresetType.ENVIRONMENT_AWARE,
      tags: ['环境', '感知', '模板'],
      baseConfig: {
        walkSpeed: 2.0,
        runSpeed: 5.0,
        jumpForce: 5.0,
        gravity: 9.8,
        usePhysics: true,
        useStateMachine: true,
        useBlendSpace: true,
        useIK: true,
        useEnvironmentAwareness: true
      },
      parameters: [
        {
          id: 'awarenessRange',
          name: '感知范围',
          description: '角色可以感知环境的最大距离',
          type: 'number',
          defaultValue: 50,
          min: 10,
          max: 200,
          step: 10,
          required: true,
          group: '感知'
        },
        {
          id: 'updateFrequency',
          name: '更新频率',
          description: '环境感知系统更新的频率（毫秒）',
          type: 'number',
          defaultValue: 1000,
          min: 100,
          max: 5000,
          step: 100,
          required: true,
          group: '感知'
        },
        {
          id: 'autoDetect',
          name: '自动检测',
          description: '是否自动检测环境',
          type: 'boolean',
          defaultValue: true,
          required: false,
          group: '感知'
        },
        {
          id: 'autoRespond',
          name: '自动响应',
          description: '是否自动响应环境变化',
          type: 'boolean',
          defaultValue: true,
          required: false,
          group: '响应'
        }
      ],
      createdAt: new Date(),
      updatedAt: new Date()
    });
  }

  /**
   * 从本地存储加载
   */
  private loadFromLocalStorage(): void {
    // 实现从本地存储加载预设和模板的逻辑
  }

  /**
   * 从服务器加载
   */
  private loadFromServer(): void {
    // 实现从服务器加载预设和模板的逻辑
  }

  /**
   * 添加预设
   * @param preset 预设数据
   */
  public addPreset(preset: ControllerPresetData): void {
    this.presets.set(preset.id, preset);

    // 发出预设添加事件
    this.eventEmitter.emit('presetAdded', preset);

    if (this.config.debug) {
      Debug.log(`添加预设: ${preset.id}`, preset);
    }
  }

  /**
   * 添加模板
   * @param template 模板数据
   */
  public addTemplate(template: ControllerTemplateData): void {
    this.templates.set(template.id, template);

    // 发出模板添加事件
    this.eventEmitter.emit('templateAdded', template);

    if (this.config.debug) {
      Debug.log(`添加模板: ${template.id}`, template);
    }
  }

  /**
   * 获取预设
   * @param id 预设ID
   * @returns 预设数据
   */
  public getPreset(id: string): ControllerPresetData | undefined {
    return this.presets.get(id);
  }

  /**
   * 获取模板
   * @param id 模板ID
   * @returns 模板数据
   */
  public getTemplate(id: string): ControllerTemplateData | undefined {
    return this.templates.get(id);
  }

  /**
   * 获取所有预设
   * @returns 预设数据数组
   */
  public getAllPresets(): ControllerPresetData[] {
    return Array.from(this.presets.values());
  }

  /**
   * 获取所有模板
   * @returns 模板数据数组
   */
  public getAllTemplates(): ControllerTemplateData[] {
    return Array.from(this.templates.values());
  }

  /**
   * 应用模板
   * @param templateId 模板ID
   * @param parameters 参数值
   * @returns 控制器配置
   */
  public applyTemplate(templateId: string, parameters: Record<string, any> = {}): Partial<AdvancedCharacterControllerConfig> | null {
    // 获取模板
    const template = this.templates.get(templateId);
    if (!template) {
      if (this.config.debug) {
        Debug.warn(`模板不存在: ${templateId}`);
      }
      return null;
    }

    // 创建配置副本
    const config = { ...template.baseConfig };

    // 应用参数
    for (const param of template.parameters) {
      const value = parameters[param.id];
      if (value !== undefined) {
        // 验证参数值
        if (param.type === 'number' && typeof value === 'number') {
          if (param.min !== undefined && value < param.min) continue;
          if (param.max !== undefined && value > param.max) continue;
        }

        // 设置参数值
        (config as any)[param.id] = value;
      }
    }

    return config;
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: EventCallback): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: EventCallback): void {
    this.eventEmitter.off(event, callback);
  }
}

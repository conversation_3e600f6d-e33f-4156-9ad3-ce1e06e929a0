"use strict";
/**
 * 变换组件
 * 管理实体的位置、旋转和缩放
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransformComponent = void 0;
var THREE = require("three");
var Component_1 = require("./Component");
var TransformComponent = exports.TransformComponent = /** @class */ (function (_super) {
    __extends(TransformComponent, _super);
    function TransformComponent() {
        var _this = _super.call(this, TransformComponent.type) || this;
        // 创建 Three.js 对象
        _this.object3D = new THREE.Object3D();
        // 获取引用
        _this.position = _this.object3D.position;
        _this.rotation = _this.object3D.rotation;
        _this.quaternion = _this.object3D.quaternion;
        _this.scale = _this.object3D.scale;
        _this.matrix = _this.object3D.matrix;
        _this.matrixWorld = _this.object3D.matrixWorld;
        return _this;
    }
    /**
     * 设置位置
     */
    TransformComponent.prototype.setPosition = function (x, y, z) {
        this.setPosition(x, y, z);
    };
    /**
     * 设置旋转（欧拉角）
     */
    TransformComponent.prototype.setRotation = function (x, y, z) {
        this.setRotationQuaternion(x, y, z);
    };
    /**
     * 设置旋转（四元数）
     */
    TransformComponent.prototype.setQuaternion = function (x, y, z, w) {
        this.setRotationQuaternion(x, y, z, w);
    };
    /**
     * 设置缩放
     */
    TransformComponent.prototype.setScale = function (x, y, z) {
        this.setScale(x, y, z);
    };
    /**
     * 获取位置
     */
    TransformComponent.prototype.getPosition = function () {
        return this.position.clone();
    };
    /**
     * 获取旋转
     */
    TransformComponent.prototype.getRotation = function () {
        return this.rotation.clone();
    };
    /**
     * 获取四元数
     */
    TransformComponent.prototype.getQuaternion = function () {
        return this.quaternion.clone();
    };
    /**
     * 获取缩放
     */
    TransformComponent.prototype.getScale = function () {
        return this.scale.clone();
    };
    /**
     * 获取世界位置
     */
    TransformComponent.prototype.getWorldPosition = function () {
        var worldPosition = new THREE.Vector3();
        this.object3D.getWorldPosition(worldPosition);
        return worldPosition;
    };
    /**
     * 获取世界旋转
     */
    TransformComponent.prototype.getWorldQuaternion = function () {
        var worldQuaternion = new THREE.Quaternion();
        this.object3D.getWorldQuaternion(worldQuaternion);
        return worldQuaternion;
    };
    /**
     * 获取世界缩放
     */
    TransformComponent.prototype.getWorldScale = function () {
        var worldScale = new THREE.Vector3();
        this.object3D.getWorldScale(worldScale);
        return worldScale;
    };
    /**
     * 向前移动
     */
    TransformComponent.prototype.translateZ = function (distance) {
        this.object3D.translateZ(distance);
    };
    /**
     * 向右移动
     */
    TransformComponent.prototype.translateX = function (distance) {
        this.object3D.translateX(distance);
    };
    /**
     * 向上移动
     */
    TransformComponent.prototype.translateY = function (distance) {
        this.object3D.translateY(distance);
    };
    /**
     * 绕X轴旋转
     */
    TransformComponent.prototype.rotateX = function (angle) {
        this.object3D.rotateX(angle);
    };
    /**
     * 绕Y轴旋转
     */
    TransformComponent.prototype.rotateY = function (angle) {
        this.object3D.rotateY(angle);
    };
    /**
     * 绕Z轴旋转
     */
    TransformComponent.prototype.rotateZ = function (angle) {
        this.object3D.rotateZ(angle);
    };
    /**
     * 看向目标
     */
    TransformComponent.prototype.lookAt = function (target) {
        this.object3D.lookAt(target);
    };
    /**
     * 添加子对象
     */
    TransformComponent.prototype.add = function (object) {
        this.object3D.add(object);
    };
    /**
     * 移除子对象
     */
    TransformComponent.prototype.remove = function (object) {
        this.object3D.remove(object);
    };
    /**
     * 更新变换矩阵
     */
    TransformComponent.prototype.updateMatrix = function () {
        this.object3D.updateMatrix();
    };
    /**
     * 更新世界变换矩阵
     */
    TransformComponent.prototype.updateMatrixWorld = function (force) {
        this.object3D.updateMatrixWorld(force);
    };
    /**
     * 更新组件
     */
    TransformComponent.prototype.onUpdate = function (deltaTime) {
        // 自动更新矩阵
        this.object3D.updateMatrixWorld();
    };
    /**
     * 销毁组件
     */
    TransformComponent.prototype.onDispose = function () {
        // 清理 Three.js 对象
        this.object3D.clear();
        this.object3D.removeFromParent();
    };
    TransformComponent.type = 'Transform';
    return TransformComponent;
}(Component_1.Component));

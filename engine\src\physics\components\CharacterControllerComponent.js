"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CharacterControllerComponent = void 0;
/**
 * 角色控制器组件
 * 为实体提供角色控制器功能
 */
var THREE = require("three");
var Component_1 = require("../../core/Component");
var CharacterController_1 = require("../character/CharacterController");
/**
 * 角色控制器组件
 */
var CharacterControllerComponent = exports.CharacterControllerComponent = /** @class */ (function (_super) {
    __extends(CharacterControllerComponent, _super);
    /**
     * 创建角色控制器组件
     * @param options 控制器选项
     */
    function CharacterControllerComponent(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, CharacterControllerComponent.type) || this;
        /** 角色控制器 */
        _this.controller = null;
        /** 物理世界 */
        _this.world = null;
        /** 是否已初始化 */
        _this.initialized = false;
        /** 是否已销毁 */
        _this.destroyed = false;
        /** 计算出的移动向量 */
        _this.computedMovement = new THREE.Vector3();
        _this.options = options;
        return _this;
    }
    /**
     * 初始化角色控制器
     * @param world 物理世界
     */
    CharacterControllerComponent.prototype.initialize = function (world) {
        if (this.initialized || !this.entity || this.destroyed)
            return;
        this.world = world;
        // 创建角色控制器
        this.controller = new CharacterController_1.CharacterController(this.entity, world, this.options);
        this.initialized = true;
    };
    /**
     * 计算碰撞器移动
     * @param desiredTranslation 期望的移动向量
     * @param filterGroups 碰撞组过滤
     * @param filterPredicate 碰撞过滤谓词函数
     */
    CharacterControllerComponent.prototype.computeColliderMovement = function (desiredTranslation, filterGroups, filterPredicate) {
        if (!this.controller)
            return;
        this.controller.computeColliderMovement(desiredTranslation, filterGroups, filterPredicate);
    };
    /**
     * 获取计算出的移动向量
     * @returns 计算出的移动向量
     */
    CharacterControllerComponent.prototype.getComputedMovement = function () {
        if (!this.controller)
            return new THREE.Vector3();
        return this.controller.getComputedMovement();
    };
    /**
     * 获取偏移量
     * @returns 偏移量
     */
    CharacterControllerComponent.prototype.getOffset = function () {
        if (!this.controller)
            return 0;
        return this.controller.getOffset();
    };
    /**
     * 设置最大爬坡角度
     * @param angle 角度（弧度）
     */
    CharacterControllerComponent.prototype.setMaxSlopeClimbAngle = function (angle) {
        if (!this.controller)
            return;
        this.controller.setMaxSlopeClimbAngle(angle);
    };
    /**
     * 获取最大爬坡角度
     * @returns 角度（弧度）
     */
    CharacterControllerComponent.prototype.getMaxSlopeClimbAngle = function () {
        if (!this.controller)
            return 0;
        return this.controller.getMaxSlopeClimbAngle();
    };
    /**
     * 设置最小滑坡角度
     * @param angle 角度（弧度）
     */
    CharacterControllerComponent.prototype.setMinSlopeSlideAngle = function (angle) {
        if (!this.controller)
            return;
        this.controller.setMinSlopeSlideAngle(angle);
    };
    /**
     * 获取最小滑坡角度
     * @returns 角度（弧度）
     */
    CharacterControllerComponent.prototype.getMinSlopeSlideAngle = function () {
        if (!this.controller)
            return 0;
        return this.controller.getMinSlopeSlideAngle();
    };
    /**
     * 启用自动台阶
     * @param maxHeight 最大台阶高度
     * @param minWidth 最小台阶宽度
     * @param stepOverDynamic 是否可以踏上动态物体
     */
    CharacterControllerComponent.prototype.enableAutoStep = function (maxHeight, minWidth, stepOverDynamic) {
        if (!this.controller)
            return;
        this.controller.enableAutoStep(maxHeight, minWidth, stepOverDynamic);
    };
    /**
     * 禁用自动台阶
     */
    CharacterControllerComponent.prototype.disableAutoStep = function () {
        if (!this.controller)
            return;
        this.controller.disableAutoStep();
    };
    /**
     * 启用地面吸附
     * @param distance 吸附距离
     */
    CharacterControllerComponent.prototype.enableSnapToGround = function (distance) {
        if (!this.controller)
            return;
        this.controller.enableSnapToGround(distance);
    };
    /**
     * 禁用地面吸附
     */
    CharacterControllerComponent.prototype.disableSnapToGround = function () {
        if (!this.controller)
            return;
        this.controller.disableSnapToGround();
    };
    /**
     * 是否启用自动台阶
     * @returns 是否启用
     */
    CharacterControllerComponent.prototype.isAutoStepEnabled = function () {
        if (!this.controller)
            return false;
        return this.controller.isAutoStepEnabled();
    };
    /**
     * 是否启用地面吸附
     * @returns 是否启用
     */
    CharacterControllerComponent.prototype.isSnapToGroundEnabled = function () {
        if (!this.controller)
            return false;
        return this.controller.isSnapToGroundEnabled();
    };
    /**
     * 获取自动台阶最大高度
     * @returns 最大高度
     */
    CharacterControllerComponent.prototype.getAutoStepMaxHeight = function () {
        if (!this.controller)
            return 0;
        return this.controller.getAutoStepMaxHeight();
    };
    /**
     * 获取自动台阶最小宽度
     * @returns 最小宽度
     */
    CharacterControllerComponent.prototype.getAutoStepMinWidth = function () {
        if (!this.controller)
            return 0;
        return this.controller.getAutoStepMinWidth();
    };
    /**
     * 是否可以踏上动态物体
     * @returns 是否可以
     */
    CharacterControllerComponent.prototype.canStepOverDynamic = function () {
        if (!this.controller)
            return false;
        return this.controller.canStepOverDynamic();
    };
    /**
     * 是否在地面上
     * @returns 是否在地面上
     */
    CharacterControllerComponent.prototype.isOnGround = function () {
        if (!this.controller)
            return false;
        return this.controller.isOnGround();
    };
    /**
     * 获取地面法线
     * @returns 地面法线
     */
    CharacterControllerComponent.prototype.getGroundNormal = function () {
        if (!this.controller)
            return new THREE.Vector3(0, 1, 0);
        return this.controller.getGroundNormal();
    };
    /**
     * 销毁组件
     */
    CharacterControllerComponent.prototype.dispose = function () {
        if (this.destroyed)
            return;
        this.controller = null;
        this.world = null;
        this.initialized = false;
        this.destroyed = true;
        _super.prototype.dispose.call(this);
    };
    /** 组件类型 */
    CharacterControllerComponent.type = 'CharacterControllerComponent';
    return CharacterControllerComponent;
}(Component_1.Component));

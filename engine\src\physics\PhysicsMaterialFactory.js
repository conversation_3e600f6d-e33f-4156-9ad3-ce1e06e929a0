"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhysicsMaterialFactory = void 0;
/**
 * 物理材质工厂
 * 用于创建和管理物理材质
 */
var CANNON = require("cannon-es");
/**
 * 物理材质工厂
 */
var PhysicsMaterialFactory = exports.PhysicsMaterialFactory = /** @class */ (function () {
    function PhysicsMaterialFactory() {
    }
    /**
     * 初始化物理材质工厂
     */
    PhysicsMaterialFactory.initialize = function () {
        // 创建默认材质
        this.defaultMaterial = new CANNON.Material('default');
        this.materials.set('default', this.defaultMaterial);
        // 创建常用材质
        this.createMaterial('metal', 0.3, 0.3);
        this.createMaterial('wood', 0.4, 0.2);
        this.createMaterial('plastic', 0.5, 0.4);
        this.createMaterial('rubber', 0.7, 0.1);
        this.createMaterial('ice', 0.05, 0.9);
        this.createMaterial('glass', 0.2, 0.8);
        this.createMaterial('concrete', 0.8, 0.2);
        this.createMaterial('bouncy', 0.5, 1.5);
    };
    /**
     * 创建材质
     * @param name 材质名称
     * @param friction 摩擦力
     * @param restitution 恢复系数
     * @returns 材质
     */
    PhysicsMaterialFactory.createMaterial = function (name, friction, restitution) {
        if (friction === void 0) { friction = 0.3; }
        if (restitution === void 0) { restitution = 0.3; }
        // 如果已存在，则返回现有材质
        if (this.materials.has(name)) {
            return this.materials.get(name);
        }
        // 创建新材质
        var material = new CANNON.Material(name);
        this.materials.set(name, material);
        // 创建与默认材质的接触材质
        this.createContactMaterial(material, this.defaultMaterial, friction, restitution);
        return material;
    };
    /**
     * 获取材质
     * @param name 材质名称
     * @returns 材质
     */
    PhysicsMaterialFactory.getMaterial = function (name) {
        // 如果不存在，则返回默认材质
        if (!this.materials.has(name)) {
            console.warn("\u6750\u8D28 \"".concat(name, "\" \u4E0D\u5B58\u5728\uFF0C\u4F7F\u7528\u9ED8\u8BA4\u6750\u8D28"));
            return this.defaultMaterial;
        }
        return this.materials.get(name);
    };
    /**
     * 创建接触材质
     * @param materialA 材质A
     * @param materialB 材质B
     * @param friction 摩擦力
     * @param restitution 恢复系数
     * @param options 其他选项
     * @returns 接触材质
     */
    PhysicsMaterialFactory.createContactMaterial = function (materialA, materialB, friction, restitution, options) {
        if (friction === void 0) { friction = 0.3; }
        if (restitution === void 0) { restitution = 0.3; }
        if (options === void 0) { options = {}; }
        // 创建唯一键
        var key = "".concat(materialA.name, "_").concat(materialB.name);
        // 如果已存在，则返回现有接触材质
        if (this.contactMaterials.has(key)) {
            return this.contactMaterials.get(key);
        }
        // 创建新接触材质
        var contactMaterial = new CANNON.ContactMaterial(materialA, materialB, __assign({ friction: friction, restitution: restitution }, options));
        this.contactMaterials.set(key, contactMaterial);
        return contactMaterial;
    };
    /**
     * 获取接触材质
     * @param materialA 材质A
     * @param materialB 材质B
     * @returns 接触材质
     */
    PhysicsMaterialFactory.getContactMaterial = function (materialA, materialB) {
        // 创建唯一键
        var key = "".concat(materialA.name, "_").concat(materialB.name);
        // 如果不存在，则尝试反向键
        if (!this.contactMaterials.has(key)) {
            var reverseKey = "".concat(materialB.name, "_").concat(materialA.name);
            if (this.contactMaterials.has(reverseKey)) {
                return this.contactMaterials.get(reverseKey);
            }
            return null;
        }
        return this.contactMaterials.get(key);
    };
    /**
     * 添加接触材质到物理世界
     * @param world 物理世界
     * @param contactMaterial 接触材质
     */
    PhysicsMaterialFactory.addContactMaterialToWorld = function (world, contactMaterial) {
        world.addContactMaterial(contactMaterial);
    };
    /**
     * 添加所有接触材质到物理世界
     * @param world 物理世界
     */
    PhysicsMaterialFactory.addAllContactMaterialsToWorld = function (world) {
        for (var _i = 0, _a = this.contactMaterials.values(); _i < _a.length; _i++) {
            var contactMaterial = _a[_i];
            world.addContactMaterial(contactMaterial);
        }
    };
    /**
     * 获取默认材质
     * @returns 默认材质
     */
    PhysicsMaterialFactory.getDefaultMaterial = function () {
        return this.defaultMaterial;
    };
    /**
     * 清除所有材质
     */
    PhysicsMaterialFactory.clear = function () {
        this.materials.clear();
        this.contactMaterials.clear();
        // 重新创建默认材质
        this.defaultMaterial = new CANNON.Material('default');
        this.materials.set('default', this.defaultMaterial);
    };
    /** 材质映射 */
    PhysicsMaterialFactory.materials = new Map();
    /** 接触材质映射 */
    PhysicsMaterialFactory.contactMaterials = new Map();
    return PhysicsMaterialFactory;
}());

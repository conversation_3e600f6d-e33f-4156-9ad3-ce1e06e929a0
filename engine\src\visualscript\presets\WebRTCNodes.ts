/**
 * 视觉脚本WebRTC节点
 * 提供WebRTC相关功能，如媒体流控制、数据通道管理等
 */
import type { Entity } from '../../core/Entity';
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { EventNode } from '../nodes/EventNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';
import { NetworkSystem } from '../../network/NetworkSystem';
import { WebRTCConnection } from '../../network/WebRTCConnection';
import { WebRTCDataChannel } from '../../network/WebRTCDataChannel';
import { MediaStreamType } from '../../network/MediaStreamType';
import { MediaStreamConstraints } from '../../network/MediaStreamConstraints';

/**
 * 创建WebRTC连接节点
 * 创建与远程对等方的WebRTC连接
 */
export class CreateWebRTCConnectionNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'peerId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '对等方ID'
    });

    this.addInput({
      name: 'config',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebRTC配置',
      defaultValue: {},
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'WebRTC连接'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const peerId = this.getInputValue('peerId') as string;
    const config = this.getInputValue('config') as object;

    // 检查输入值是否有效
    if (!peerId) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取网络系统
    const networkSystem = this.graph.getWorld().getSystem(NetworkSystem);
    if (!networkSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 创建WebRTC连接
      const connection = await networkSystem.createWebRTCConnection(peerId, config);
      
      if (connection) {
        // 设置输出值
        this.setOutputValue('connection', connection);
        
        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('创建WebRTC连接失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 创建数据通道节点
 * 在WebRTC连接上创建数据通道
 */
export class CreateDataChannelNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebRTC连接'
    });

    this.addInput({
      name: 'label',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '通道标签',
      defaultValue: 'data'
    });

    this.addInput({
      name: 'options',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '通道选项',
      defaultValue: {},
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'dataChannel',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '数据通道'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const connection = this.getInputValue('connection') as WebRTCConnection;
    const label = this.getInputValue('label') as string;
    const options = this.getInputValue('options') as object;

    // 检查输入值是否有效
    if (!connection) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 创建数据通道
      const dataChannel = await connection.createDataChannel(label, options);
      
      if (dataChannel) {
        // 设置输出值
        this.setOutputValue('dataChannel', dataChannel);
        
        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('创建数据通道失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 数据通道消息事件节点
 * 监听数据通道消息事件
 */
export class DataChannelMessageEventNode extends EventNode {
  /** 数据通道 */
  private dataChannel: WebRTCDataChannel | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入数据插槽
    this.addInput({
      name: 'dataChannel',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '数据通道'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '收到消息'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '消息数据'
    });
  }

  /**
   * 初始化事件
   */
  public initialize(): void {
    // 获取数据通道
    this.dataChannel = this.getInputValue('dataChannel') as WebRTCDataChannel;
    
    if (this.dataChannel) {
      // 监听消息事件
      this.dataChannel.on('message', this.onMessage.bind(this));
    }
  }

  /**
   * 消息事件处理
   * @param message 消息数据
   */
  private onMessage(message: any): void {
    // 设置输出值
    this.setOutputValue('message', message);
    
    // 触发输出流程
    this.triggerFlow('flow');
  }

  /**
   * 清理事件
   */
  public cleanup(): void {
    if (this.dataChannel) {
      // 移除事件监听
      this.dataChannel.off('message', this.onMessage.bind(this));
      this.dataChannel = null;
    }
  }
}

/**
 * 注册WebRTC节点
 * @param registry 节点注册表
 */
export function registerWebRTCNodes(registry: NodeRegistry): void {
  // 注册创建WebRTC连接节点
  registry.registerNodeType({
    type: 'network/webrtc/createConnection',
    category: NodeCategory.NETWORK,
    constructor: CreateWebRTCConnectionNode,
    label: '创建WebRTC连接',
    description: '创建与远程对等方的WebRTC连接',
    icon: 'webrtc',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'connection']
  });

  // 注册创建数据通道节点
  registry.registerNodeType({
    type: 'network/webrtc/createDataChannel',
    category: NodeCategory.NETWORK,
    constructor: CreateDataChannelNode,
    label: '创建数据通道',
    description: '在WebRTC连接上创建数据通道',
    icon: 'datachannel',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'datachannel']
  });

  // 注册数据通道消息事件节点
  registry.registerNodeType({
    type: 'network/webrtc/onDataChannelMessage',
    category: NodeCategory.NETWORK,
    constructor: DataChannelMessageEventNode,
    label: '数据通道消息事件',
    description: '监听数据通道消息事件',
    icon: 'message',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'datachannel', 'message', 'event']
  });
}

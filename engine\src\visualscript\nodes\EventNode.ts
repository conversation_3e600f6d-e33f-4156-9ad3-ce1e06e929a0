/**
 * 视觉脚本事件节点
 * 事件节点是视觉脚本的入口点，用于响应各种事件
 */
import { Node, NodeCategory, NodeOptions, NodeType, SocketType } from './Node';

/**
 * 事件节点选项
 */
export interface EventNodeOptions extends NodeOptions {
  /** 事件名称 */
  eventName?: string;
}

/**
 * 事件节点基类
 */
export class EventNode extends Node {
  /** 节点类型 */
  public readonly nodeType: NodeType = NodeType.EVENT;
  
  /** 节点类别 */
  public readonly category: NodeCategory = NodeCategory.EVENT;
  
  /** 事件名称 */
  protected eventName: string;
  
  /**
   * 创建事件节点
   * @param options 节点选项
   */
  constructor(options: EventNodeOptions) {
    super(options);
    
    this.eventName = options.eventName || '';
  }
  
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 事件节点只有输出流程插槽，没有输入流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '当事件触发时执行'
    });
  }
  
  /**
   * 初始化节点
   * 在视觉脚本引擎启动时调用
   */
  public initialize(): void {
    // 子类实现
  }
  
  /**
   * 当视觉脚本开始执行时调用
   */
  public onStart(): void {
    // 子类实现
  }
  
  /**
   * 当视觉脚本停止执行时调用
   */
  public onStop(): void {
    // 子类实现
  }
  
  /**
   * 当视觉脚本更新时调用
   * @param deltaTime 帧间隔时间（秒）
   */
  public onUpdate(deltaTime: number): void {
    // 子类实现
  }
  
  /**
   * 触发事件
   * @param args 事件参数
   */
  protected trigger(...args: any[]): void {
    // 设置输出参数
    if (args.length > 0 && this.outputs.size > 1) {
      const outputNames = Array.from(this.outputs.keys()).filter(name => name !== 'flow');
      
      for (let i = 0; i < Math.min(args.length, outputNames.length); i++) {
        this.setOutputValue(outputNames[i], args[i]);
      }
    }
    
    // 触发流程
    this.triggerFlow('flow');
  }
}

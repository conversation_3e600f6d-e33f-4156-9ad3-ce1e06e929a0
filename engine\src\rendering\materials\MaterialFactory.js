"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MaterialFactory = exports.MaterialType = void 0;
/**
 * 材质工厂
 * 用于创建各种类型的材质
 */
var THREE = require("three");
/**
 * 材质类型枚举
 */
var MaterialType;
(function (MaterialType) {
    /** 基础材质 */
    MaterialType["BASIC"] = "basic";
    /** Lambert材质 */
    MaterialType["LAMBERT"] = "lambert";
    /** Phong材质 */
    MaterialType["PHONG"] = "phong";
    /** 标准材质 */
    MaterialType["STANDARD"] = "standard";
    /** 物理材质 */
    MaterialType["PHYSICAL"] = "physical";
    /** 卡通材质 */
    MaterialType["TOON"] = "toon";
    /** 深度材质 */
    MaterialType["DEPTH"] = "depth";
    /** 法线材质 */
    MaterialType["NORMAL"] = "normal";
    /** 线条材质 */
    MaterialType["LINE_BASIC"] = "lineBasic";
    /** 虚线材质 */
    MaterialType["LINE_DASHED"] = "lineDashed";
    /** 点材质 */
    MaterialType["POINTS"] = "points";
    /** 精灵材质 */
    MaterialType["SPRITE"] = "sprite";
    /** 着色器材质 */
    MaterialType["SHADER"] = "shader";
    /** 原始着色器材质 */
    MaterialType["RAW_SHADER"] = "rawShader";
    /** 阴影材质 */
    MaterialType["SHADOW"] = "shadow";
    /** Matcap材质 */
    MaterialType["MATCAP"] = "matcap";
})(MaterialType || (exports.MaterialType = MaterialType = {}));
/**
 * 材质工厂类
 */
var MaterialFactory = /** @class */ (function () {
    /**
     * 创建材质工厂
     */
    function MaterialFactory() {
        /** 材质创建函数映射 */
        this.materialCreators = new Map();
        this.registerDefaultMaterials();
    }
    /**
     * 注册默认材质
     */
    MaterialFactory.prototype.registerDefaultMaterials = function () {
        // 注册基础材质
        this.registerMaterial(MaterialType.BASIC, function (params) { return new THREE.MeshBasicMaterial(params); });
        // 注册Lambert材质
        this.registerMaterial(MaterialType.LAMBERT, function (params) { return new THREE.MeshLambertMaterial(params); });
        // 注册Phong材质
        this.registerMaterial(MaterialType.PHONG, function (params) { return new THREE.MeshPhongMaterial(params); });
        // 注册标准材质
        this.registerMaterial(MaterialType.STANDARD, function (params) { return new THREE.MeshStandardMaterial(params); });
        // 注册物理材质
        this.registerMaterial(MaterialType.PHYSICAL, function (params) { return new THREE.MeshPhysicalMaterial(params); });
        // 注册卡通材质
        this.registerMaterial(MaterialType.TOON, function (params) { return new THREE.MeshToonMaterial(params); });
        // 注册深度材质
        this.registerMaterial(MaterialType.DEPTH, function (params) { return new THREE.MeshDepthMaterial(params); });
        // 注册法线材质
        this.registerMaterial(MaterialType.NORMAL, function (params) { return new THREE.MeshNormalMaterial(params); });
        // 注册线条材质
        this.registerMaterial(MaterialType.LINE_BASIC, function (params) { return new THREE.LineBasicMaterial(params); });
        // 注册虚线材质
        this.registerMaterial(MaterialType.LINE_DASHED, function (params) { return new THREE.LineDashedMaterial(params); });
        // 注册点材质
        this.registerMaterial(MaterialType.POINTS, function (params) { return new THREE.PointsMaterial(params); });
        // 注册精灵材质
        this.registerMaterial(MaterialType.SPRITE, function (params) { return new THREE.SpriteMaterial(params); });
        // 注册着色器材质
        this.registerMaterial(MaterialType.SHADER, function (params) { return new THREE.ShaderMaterial(params); });
        // 注册原始着色器材质
        this.registerMaterial(MaterialType.RAW_SHADER, function (params) { return new THREE.RawShaderMaterial(params); });
        // 注册阴影材质
        this.registerMaterial(MaterialType.SHADOW, function (params) { return new THREE.ShadowMaterial(params); });
        // 注册Matcap材质
        this.registerMaterial(MaterialType.MATCAP, function (params) { return new THREE.MeshMatcapMaterial(params); });
    };
    /**
     * 注册材质
     * @param type 材质类型
     * @param creator 创建函数
     */
    MaterialFactory.prototype.registerMaterial = function (type, creator) {
        this.materialCreators.set(type, creator);
    };
    /**
     * 创建材质
     * @param type 材质类型
     * @param params 材质参数
     * @returns 材质
     */
    MaterialFactory.prototype.createMaterial = function (type, params) {
        if (params === void 0) { params = {}; }
        // 获取创建函数
        var creator = this.materialCreators.get(type);
        // 如果找不到创建函数，则使用默认材质
        if (!creator) {
            console.warn("\u672A\u77E5\u7684\u6750\u8D28\u7C7B\u578B: ".concat(type, "\uFF0C\u4F7F\u7528\u57FA\u7840\u6750\u8D28\u4EE3\u66FF"));
            return new THREE.MeshBasicMaterial(params);
        }
        // 创建材质
        return creator(params);
    };
    /**
     * 创建基础材质
     * @param params 材质参数
     * @returns 基础材质
     */
    MaterialFactory.prototype.createBasicMaterial = function (params) {
        if (params === void 0) { params = {}; }
        return new THREE.MeshBasicMaterial(params);
    };
    /**
     * 创建Lambert材质
     * @param params 材质参数
     * @returns Lambert材质
     */
    MaterialFactory.prototype.createLambertMaterial = function (params) {
        if (params === void 0) { params = {}; }
        return new THREE.MeshLambertMaterial(params);
    };
    /**
     * 创建Phong材质
     * @param params 材质参数
     * @returns Phong材质
     */
    MaterialFactory.prototype.createPhongMaterial = function (params) {
        if (params === void 0) { params = {}; }
        return new THREE.MeshPhongMaterial(params);
    };
    /**
     * 创建标准材质
     * @param params 材质参数
     * @returns 标准材质
     */
    MaterialFactory.prototype.createStandardMaterial = function (params) {
        if (params === void 0) { params = {}; }
        return new THREE.MeshStandardMaterial(params);
    };
    /**
     * 创建物理材质
     * @param params 材质参数
     * @returns 物理材质
     */
    MaterialFactory.prototype.createPhysicalMaterial = function (params) {
        if (params === void 0) { params = {}; }
        return new THREE.MeshPhysicalMaterial(params);
    };
    /**
     * 创建卡通材质
     * @param params 材质参数
     * @returns 卡通材质
     */
    MaterialFactory.prototype.createToonMaterial = function (params) {
        if (params === void 0) { params = {}; }
        return new THREE.MeshToonMaterial(params);
    };
    /**
     * 创建深度材质
     * @param params 材质参数
     * @returns 深度材质
     */
    MaterialFactory.prototype.createDepthMaterial = function (params) {
        if (params === void 0) { params = {}; }
        return new THREE.MeshDepthMaterial(params);
    };
    /**
     * 创建法线材质
     * @param params 材质参数
     * @returns 法线材质
     */
    MaterialFactory.prototype.createNormalMaterial = function (params) {
        if (params === void 0) { params = {}; }
        return new THREE.MeshNormalMaterial(params);
    };
    /**
     * 创建线条材质
     * @param params 材质参数
     * @returns 线条材质
     */
    MaterialFactory.prototype.createLineBasicMaterial = function (params) {
        if (params === void 0) { params = {}; }
        return new THREE.LineBasicMaterial(params);
    };
    /**
     * 创建虚线材质
     * @param params 材质参数
     * @returns 虚线材质
     */
    MaterialFactory.prototype.createLineDashedMaterial = function (params) {
        if (params === void 0) { params = {}; }
        return new THREE.LineDashedMaterial(params);
    };
    /**
     * 创建点材质
     * @param params 材质参数
     * @returns 点材质
     */
    MaterialFactory.prototype.createPointsMaterial = function (params) {
        if (params === void 0) { params = {}; }
        return new THREE.PointsMaterial(params);
    };
    /**
     * 创建精灵材质
     * @param params 材质参数
     * @returns 精灵材质
     */
    MaterialFactory.prototype.createSpriteMaterial = function (params) {
        if (params === void 0) { params = {}; }
        return new THREE.SpriteMaterial(params);
    };
    /**
     * 创建着色器材质
     * @param params 材质参数
     * @returns 着色器材质
     */
    MaterialFactory.prototype.createShaderMaterial = function (params) {
        if (params === void 0) { params = {}; }
        return new THREE.ShaderMaterial(params);
    };
    /**
     * 创建原始着色器材质
     * @param params 材质参数
     * @returns 原始着色器材质
     */
    MaterialFactory.prototype.createRawShaderMaterial = function (params) {
        if (params === void 0) { params = {}; }
        return new THREE.RawShaderMaterial(params);
    };
    /**
     * 创建阴影材质
     * @param params 材质参数
     * @returns 阴影材质
     */
    MaterialFactory.prototype.createShadowMaterial = function (params) {
        if (params === void 0) { params = {}; }
        return new THREE.ShadowMaterial(params);
    };
    /**
     * 创建Matcap材质
     * @param params 材质参数
     * @returns Matcap材质
     */
    MaterialFactory.prototype.createMatcapMaterial = function (params) {
        if (params === void 0) { params = {}; }
        return new THREE.MeshMatcapMaterial(params);
    };
    return MaterialFactory;
}());
exports.MaterialFactory = MaterialFactory;

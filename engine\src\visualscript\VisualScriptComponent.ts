/**
 * 视觉脚本组件
 * 用于将视觉脚本附加到实体上
 */
import { Component } from '../core/Component';
import type { Entity } from '../core/Entity';
import { GraphJSON } from './graph/GraphJSON';

/**
 * 视觉脚本组件配置
 */
export interface VisualScriptComponentOptions {
  /** 视觉脚本JSON数据 */
  script?: GraphJSON;
  /** 是否自动运行 */
  autoRun?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 脚本域 */
  domain?: string;
}

/**
 * 视觉脚本组件
 * 用于将视觉脚本附加到实体上
 */
export class VisualScriptComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE = 'VisualScript';
  
  /** 视觉脚本JSON数据 */
  private _script: GraphJSON | null = null;
  
  /** 是否正在运行 */
  private _running: boolean = false;
  
  /** 是否禁用 */
  private _disabled: boolean = false;
  
  /** 脚本域 */
  private _domain: string = 'default';
  
  /**
   * 创建视觉脚本组件
   * @param entity 所属实体
   * @param options 组件选项
   */
  constructor(entity: Entity, options: VisualScriptComponentOptions = {}) {
    super(entity, VisualScriptComponent.TYPE);
    
    if (options.script) {
      this._script = options.script;
    }
    
    this._running = options.autoRun || false;
    this._disabled = options.disabled || false;
    this._domain = options.domain || 'default';
  }
  
  /**
   * 获取视觉脚本JSON数据
   */
  public get script(): GraphJSON | null {
    return this._script;
  }
  
  /**
   * 设置视觉脚本JSON数据
   */
  public set script(value: GraphJSON | null) {
    this._script = value;
    this.emit('scriptChanged', value);
  }
  
  /**
   * 获取是否正在运行
   */
  public get running(): boolean {
    return this._running && !this._disabled;
  }
  
  /**
   * 设置是否运行
   */
  public set running(value: boolean) {
    if (this._running !== value) {
      this._running = value;
      this.emit('runningChanged', value);
    }
  }
  
  /**
   * 获取是否禁用
   */
  public get disabled(): boolean {
    return this._disabled;
  }
  
  /**
   * 设置是否禁用
   */
  public set disabled(value: boolean) {
    if (this._disabled !== value) {
      this._disabled = value;
      
      // 如果禁用，则停止运行
      if (value && this._running) {
        this._running = false;
        this.emit('runningChanged', false);
      }
      
      this.emit('disabledChanged', value);
    }
  }
  
  /**
   * 获取脚本域
   */
  public get domain(): string {
    return this._domain;
  }
  
  /**
   * 设置脚本域
   */
  public set domain(value: string) {
    if (this._domain !== value) {
      this._domain = value;
      this.emit('domainChanged', value);
    }
  }
  
  /**
   * 开始运行视觉脚本
   */
  public play(): void {
    if (!this._disabled && !this._running) {
      this.running = true;
    }
  }
  
  /**
   * 暂停运行视觉脚本
   */
  public pause(): void {
    if (this._running) {
      this.running = false;
    }
  }
  
  /**
   * 切换运行状态
   */
  public toggle(): void {
    if (!this._disabled) {
      this.running = !this._running;
    }
  }
  
  /**
   * 序列化组件
   */
  public serialize(): any {
    return {
      type: VisualScriptComponent.TYPE,
      script: this._script,
      running: this._running,
      disabled: this._disabled,
      domain: this._domain
    };
  }
  
  /**
   * 反序列化组件
   * @param data 序列化数据
   */
  public deserialize(data: any): void {
    if (data.script) {
      this._script = data.script;
    }
    
    if (data.running !== undefined) {
      this._running = data.running;
    }
    
    if (data.disabled !== undefined) {
      this._disabled = data.disabled;
    }
    
    if (data.domain !== undefined) {
      this._domain = data.domain;
    }
  }
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhysicsDebugger = void 0;
/**
 * 物理调试器
 * 用于可视化物理世界
 */
var THREE = require("three");
var CANNON = require("cannon-es");
/**
 * 物理调试器
 */
var PhysicsDebugger = /** @class */ (function () {
    /**
     * 创建物理调试器
     * @param physicsSystem 物理系统
     * @param options 调试器选项
     */
    function PhysicsDebugger(physicsSystem, options) {
        if (options === void 0) { options = {}; }
        /** 物理体网格映射 */
        this.bodyMeshes = new Map();
        /** 约束网格映射 */
        this.constraintMeshes = new Map();
        /** 碰撞点网格 */
        this.contactPointMeshes = [];
        /** AABB网格映射 */
        this.aabbMeshes = new Map();
        /** 是否已初始化 */
        this.initialized = false;
        this.physicsSystem = physicsSystem;
        this.scene = new THREE.Scene();
        // 设置选项
        this.showBodies = options.showBodies !== undefined ? options.showBodies : true;
        this.showConstraints = options.showConstraints !== undefined ? options.showConstraints : true;
        this.showContactPoints = options.showContactPoints !== undefined ? options.showContactPoints : false;
        this.showAABBs = options.showAABBs !== undefined ? options.showAABBs : false;
        // 设置颜色
        this.bodyColor = options.bodyColor instanceof THREE.Color
            ? options.bodyColor
            : new THREE.Color(options.bodyColor !== undefined ? options.bodyColor : 0x00ff00);
        this.staticBodyColor = options.staticBodyColor instanceof THREE.Color
            ? options.staticBodyColor
            : new THREE.Color(options.staticBodyColor !== undefined ? options.staticBodyColor : 0xff0000);
        this.kinematicBodyColor = options.kinematicBodyColor instanceof THREE.Color
            ? options.kinematicBodyColor
            : new THREE.Color(options.kinematicBodyColor !== undefined ? options.kinematicBodyColor : 0x0000ff);
        this.constraintColor = options.constraintColor instanceof THREE.Color
            ? options.constraintColor
            : new THREE.Color(options.constraintColor !== undefined ? options.constraintColor : 0xffff00);
        this.contactPointColor = options.contactPointColor instanceof THREE.Color
            ? options.contactPointColor
            : new THREE.Color(options.contactPointColor !== undefined ? options.contactPointColor : 0xff00ff);
        this.aabbColor = options.aabbColor instanceof THREE.Color
            ? options.aabbColor
            : new THREE.Color(options.aabbColor !== undefined ? options.aabbColor : 0xffffff);
        // 设置其他属性
        this.lineWidth = options.lineWidth !== undefined ? options.lineWidth : 1;
        this.opacity = options.opacity !== undefined ? options.opacity : 0.5;
    }
    /**
     * 初始化调试器
     */
    PhysicsDebugger.prototype.initialize = function () {
        if (this.initialized)
            return;
        // 清空场景
        this.clear();
        // 获取物理世界
        var world = this.physicsSystem.getPhysicsWorld();
        // 创建物理体网格
        if (this.showBodies) {
            for (var i = 0; i < world.bodies.length; i++) {
                var body = world.bodies[i];
                var mesh = this.createBodyMesh(body);
                this.bodyMeshes.set(body, mesh);
                this.scene.add(mesh);
            }
        }
        // 创建约束网格
        if (this.showConstraints) {
            for (var i = 0; i < world.constraints.length; i++) {
                var constraint = world.constraints[i];
                var mesh = this.createConstraintMesh(constraint);
                if (mesh) {
                    this.constraintMeshes.set(constraint, mesh);
                    this.scene.add(mesh);
                }
            }
        }
        // 创建AABB网格
        if (this.showAABBs) {
            for (var i = 0; i < world.bodies.length; i++) {
                var body = world.bodies[i];
                var mesh = this.createAABBMesh(body);
                this.aabbMeshes.set(body, mesh);
                this.scene.add(mesh);
            }
        }
        this.initialized = true;
    };
    /**
     * 更新调试器
     */
    PhysicsDebugger.prototype.update = function () {
        if (!this.initialized)
            return;
        // 获取物理世界
        var world = this.physicsSystem.getPhysicsWorld();
        // 更新物理体网格
        if (this.showBodies) {
            for (var i = 0; i < world.bodies.length; i++) {
                var body = world.bodies[i];
                // 如果物理体没有网格，创建一个
                if (!this.bodyMeshes.has(body)) {
                    var mesh_1 = this.createBodyMesh(body);
                    this.bodyMeshes.set(body, mesh_1);
                    this.scene.add(mesh_1);
                }
                // 更新网格位置和旋转
                var mesh = this.bodyMeshes.get(body);
                mesh.position.copy(new THREE.Vector3(body.getPosition().x, body.getPosition().y, body.getPosition().z));
                mesh.quaternion.copy(new THREE.Quaternion(body.quaternion.x, body.quaternion.y, body.quaternion.z, body.quaternion.w));
            }
            // 移除不存在的物理体网格
            for (var _i = 0, _a = this.bodyMeshes.entries(); _i < _a.length; _i++) {
                var _b = _a[_i], body = _b[0], mesh = _b[1];
                if (!world.bodies.includes(body)) {
                    this.scene.remove(mesh);
                    this.bodyMeshes.delete(body);
                }
            }
        }
        // 更新约束网格
        if (this.showConstraints) {
            for (var i = 0; i < world.constraints.length; i++) {
                var constraint = world.constraints[i];
                // 如果约束没有网格，创建一个
                if (!this.constraintMeshes.has(constraint)) {
                    var mesh_2 = this.createConstraintMesh(constraint);
                    if (mesh_2) {
                        this.constraintMeshes.set(constraint, mesh_2);
                        this.scene.add(mesh_2);
                    }
                }
                // 更新约束网格
                var mesh = this.constraintMeshes.get(constraint);
                if (mesh) {
                    this.updateConstraintMesh(constraint, mesh);
                }
            }
            // 移除不存在的约束网格
            for (var _c = 0, _d = this.constraintMeshes.entries(); _c < _d.length; _c++) {
                var _e = _d[_c], constraint = _e[0], mesh = _e[1];
                if (!world.constraints.includes(constraint)) {
                    this.scene.remove(mesh);
                    this.constraintMeshes.delete(constraint);
                }
            }
        }
        // 更新碰撞点网格
        if (this.showContactPoints) {
            // 移除所有碰撞点网格
            for (var _f = 0, _g = this.contactPointMeshes; _f < _g.length; _f++) {
                var mesh = _g[_f];
                this.scene.remove(mesh);
            }
            this.contactPointMeshes = [];
            // 创建新的碰撞点网格
            for (var i = 0; i < world.contacts.length; i++) {
                var contact = world.contacts[i];
                for (var j = 0; j < contact.contactEquations.length; j++) {
                    var equation = contact.contactEquations[j];
                    var mesh = this.createContactPointMesh(equation);
                    this.contactPointMeshes.push(mesh);
                    this.scene.add(mesh);
                }
            }
        }
        // 更新AABB网格
        if (this.showAABBs) {
            for (var i = 0; i < world.bodies.length; i++) {
                var body = world.bodies[i];
                // 如果物理体没有AABB网格，创建一个
                if (!this.aabbMeshes.has(body)) {
                    var mesh_3 = this.createAABBMesh(body);
                    this.aabbMeshes.set(body, mesh_3);
                    this.scene.add(mesh_3);
                }
                // 更新AABB网格
                var mesh = this.aabbMeshes.get(body);
                this.updateAABBMesh(body, mesh);
            }
            // 移除不存在的AABB网格
            for (var _h = 0, _j = this.aabbMeshes.entries(); _h < _j.length; _h++) {
                var _k = _j[_h], body = _k[0], mesh = _k[1];
                if (!world.bodies.includes(body)) {
                    this.scene.remove(mesh);
                    this.aabbMeshes.delete(body);
                }
            }
        }
    };
    /**
     * 创建物理体网格
     * @param body 物理体
     * @returns 物理体网格
     */
    PhysicsDebugger.prototype.createBodyMesh = function (body) {
        var group = new THREE.Group();
        // 根据物理体类型选择颜色
        var color;
        if (body.type === CANNON.BODY_TYPES.STATIC) {
            color = this.staticBodyColor;
        }
        else if (body.type === CANNON.BODY_TYPES.KINEMATIC) {
            color = this.kinematicBodyColor;
        }
        else {
            color = this.bodyColor;
        }
        // 创建材质
        var material = new THREE.MeshBasicMaterial({
            color: color,
            wireframe: true,
            transparent: true,
            opacity: this.opacity,
            depthTest: true,
        });
        // 为每个形状创建网格
        for (var i = 0; i < body.shapes.length; i++) {
            var shape = body.shapes[i];
            var geometry = void 0;
            var mesh = void 0;
            // 根据形状类型创建几何体
            switch (shape.type) {
                case CANNON.SHAPE_TYPES.BOX:
                    var boxShape = shape;
                    geometry = new THREE.BoxGeometry(boxShape.halfExtents.x * 2, boxShape.halfExtents.y * 2, boxShape.halfExtents.z * 2);
                    mesh = new THREE.Mesh(geometry, material);
                    break;
                case CANNON.SHAPE_TYPES.SPHERE:
                    var sphereShape = shape;
                    geometry = new THREE.SphereGeometry(sphereShape.radius, 16, 16);
                    mesh = new THREE.Mesh(geometry, material);
                    break;
                case CANNON.SHAPE_TYPES.CYLINDER:
                    var cylinderShape = shape;
                    geometry = new THREE.CylinderGeometry(cylinderShape.radiusTop, cylinderShape.radiusBottom, cylinderShape.height, 16);
                    mesh = new THREE.Mesh(geometry, material);
                    // 旋转几何体以匹配CANNON.js的坐标系
                    mesh.rotation.x = Math.PI / 2;
                    break;
                case CANNON.SHAPE_TYPES.PLANE:
                    geometry = new THREE.PlaneGeometry(10, 10, 10, 10);
                    mesh = new THREE.Mesh(geometry, material);
                    break;
                case CANNON.SHAPE_TYPES.CONVEXPOLYHEDRON:
                    var convexShape = shape;
                    var vertices = [];
                    for (var j = 0; j < convexShape.vertices.length; j++) {
                        var v = convexShape.vertices[j];
                        vertices.push(new THREE.Vector3(v.x, v.y, v.z));
                    }
                    var faces = [];
                    for (var j = 0; j < convexShape.faces.length; j++) {
                        var face = convexShape.faces[j];
                        // 三角化面
                        for (var k = 1; k < face.length - 1; k++) {
                            faces.push(face[0], face[k], face[k + 1]);
                        }
                    }
                    geometry = new THREE.BufferGeometry();
                    var positionArray = new Float32Array(faces.length * 3 * 3);
                    for (var j = 0; j < faces.length; j += 3) {
                        var v1 = vertices[faces[j]];
                        var v2 = vertices[faces[j + 1]];
                        var v3 = vertices[faces[j + 2]];
                        positionArray[j * 3] = v1.x;
                        positionArray[j * 3 + 1] = v1.y;
                        positionArray[j * 3 + 2] = v1.z;
                        positionArray[j * 3 + 3] = v2.x;
                        positionArray[j * 3 + 4] = v2.y;
                        positionArray[j * 3 + 5] = v2.z;
                        positionArray[j * 3 + 6] = v3.x;
                        positionArray[j * 3 + 7] = v3.y;
                        positionArray[j * 3 + 8] = v3.z;
                    }
                    geometry.setAttribute('position', new THREE.BufferAttribute(positionArray, 3));
                    geometry.computeVertexNormals();
                    mesh = new THREE.Mesh(geometry, material);
                    break;
                default:
                    // 对于不支持的形状，创建一个小球作为占位符
                    geometry = new THREE.SphereGeometry(0.1, 8, 8);
                    mesh = new THREE.Mesh(geometry, material);
                    console.warn("\u4E0D\u652F\u6301\u7684\u7269\u7406\u5F62\u72B6\u7C7B\u578B: ".concat(shape.type));
                    break;
            }
            // 应用形状的位置和旋转
            if (body.shapeOffsets[i]) {
                mesh.position.copy(new THREE.Vector3(body.shapeOffsets[i].x, body.shapeOffsets[i].y, body.shapeOffsets[i].z));
            }
            if (body.shapeOrientations[i]) {
                mesh.quaternion.copy(new THREE.Quaternion(body.shapeOrientations[i].x, body.shapeOrientations[i].y, body.shapeOrientations[i].z, body.shapeOrientations[i].w));
            }
            group.add(mesh);
        }
        return group;
    };
    /**
     * 创建约束网格
     * @param constraint 约束
     * @returns 约束网格
     */
    PhysicsDebugger.prototype.createConstraintMesh = function (constraint) {
        // 创建约束可视化的基本实现
        var material = new THREE.LineBasicMaterial({ color: this.constraintColor });
        var geometry = new THREE.BufferGeometry();
        // 获取约束的两个物理体
        var bodyA = constraint.bodyA;
        var bodyB = constraint.bodyB;
        if (!bodyA || !bodyB)
            return null;
        // 创建连接线
        var points = [
            new THREE.Vector3(bodyA.getPosition().x, bodyA.getPosition().y, bodyA.getPosition().z),
            new THREE.Vector3(bodyB.getPosition().x, bodyB.getPosition().y, bodyB.getPosition().z)
        ];
        geometry.setFromPoints(points);
        return new THREE.Line(geometry, material);
    };
    /**
     * 更新约束网格
     * @param constraint 约束
     * @param mesh 网格
     */
    PhysicsDebugger.prototype.updateConstraintMesh = function (constraint, mesh) {
        var line = mesh;
        var geometry = line.geometry;
        var bodyA = constraint.bodyA;
        var bodyB = constraint.bodyB;
        if (!bodyA || !bodyB)
            return;
        var points = [
            new THREE.Vector3(bodyA.getPosition().x, bodyA.getPosition().y, bodyA.getPosition().z),
            new THREE.Vector3(bodyB.getPosition().x, bodyB.getPosition().y, bodyB.getPosition().z)
        ];
        geometry.setFromPoints(points);
    };
    /**
     * 创建碰撞点网格
     * @param equation 碰撞方程
     * @returns 碰撞点网格
     */
    PhysicsDebugger.prototype.createContactPointMesh = function (equation) {
        var geometry = new THREE.SphereGeometry(0.05, 8, 8);
        var material = new THREE.MeshBasicMaterial({ color: this.contactPointColor });
        var mesh = new THREE.Mesh(geometry, material);
        // 设置位置
        mesh.setPosition(equation.contactPointA.x + equation.bodyA.getPosition().x, equation.contactPointA.y + equation.bodyA.getPosition().y, equation.contactPointA.z + equation.bodyA.getPosition().z);
        return mesh;
    };
    /**
     * 创建AABB网格
     * @param body 物理体
     * @returns AABB网格
     */
    PhysicsDebugger.prototype.createAABBMesh = function (body) {
        var geometry = new THREE.EdgesGeometry(new THREE.BoxGeometry(1, 1, 1));
        var material = new THREE.LineBasicMaterial({ color: this.aabbColor });
        var mesh = new THREE.LineSegments(geometry, material);
        this.updateAABBMesh(body, mesh);
        return mesh;
    };
    /**
     * 更新AABB网格
     * @param body 物理体
     * @param mesh 网格
     */
    PhysicsDebugger.prototype.updateAABBMesh = function (body, mesh) {
        // 计算AABB
        body.computeAABB();
        var aabb = body.aabb;
        if (!aabb)
            return;
        // 更新网格大小和位置
        var size = new THREE.Vector3(aabb.upperBound.x - aabb.lowerBound.x, aabb.upperBound.y - aabb.lowerBound.y, aabb.upperBound.z - aabb.lowerBound.z);
        var center = new THREE.Vector3((aabb.upperBound.x + aabb.lowerBound.x) / 2, (aabb.upperBound.y + aabb.lowerBound.y) / 2, (aabb.upperBound.z + aabb.lowerBound.z) / 2);
        mesh.scale.copy(size);
        mesh.position.copy(center);
    };
    /**
     * 清空调试器
     */
    PhysicsDebugger.prototype.clear = function () {
        // 清空所有网格
        this.scene.clear();
        this.bodyMeshes.clear();
        this.constraintMeshes.clear();
        this.contactPointMeshes = [];
        this.aabbMeshes.clear();
    };
    /**
     * 销毁调试器
     */
    PhysicsDebugger.prototype.dispose = function () {
        this.clear();
        this.initialized = false;
    };
    return PhysicsDebugger;
}());
exports.PhysicsDebugger = PhysicsDebugger;

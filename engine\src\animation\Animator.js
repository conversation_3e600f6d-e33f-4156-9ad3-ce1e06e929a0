"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Animator = exports.AnimationEventType = exports.AnimationState = void 0;
/**
 * 动画控制器
 * 用于控制和混合多个动画片段的播放
 */
var THREE = require("three");
var Component_1 = require("../core/Component");
var EventEmitter_1 = require("../utils/EventEmitter");
var AnimationClip_1 = require("./AnimationClip");
/**
 * 动画状态
 */
var AnimationState;
(function (AnimationState) {
    /** 停止 */
    AnimationState["STOPPED"] = "stopped";
    /** 播放中 */
    AnimationState["PLAYING"] = "playing";
    /** 暂停 */
    AnimationState["PAUSED"] = "paused";
    /** 混合中 */
    AnimationState["BLENDING"] = "blending";
})(AnimationState || (exports.AnimationState = AnimationState = {}));
/**
 * 动画事件类型
 */
var AnimationEventType;
(function (AnimationEventType) {
    /** 开始 */
    AnimationEventType["START"] = "start";
    /** 停止 */
    AnimationEventType["STOP"] = "stop";
    /** 暂停 */
    AnimationEventType["PAUSE"] = "pause";
    /** 恢复 */
    AnimationEventType["RESUME"] = "resume";
    /** 循环 */
    AnimationEventType["LOOP"] = "loop";
    /** 完成 */
    AnimationEventType["COMPLETE"] = "complete";
    /** 混合开始 */
    AnimationEventType["BLEND_START"] = "blendStart";
    /** 混合完成 */
    AnimationEventType["BLEND_COMPLETE"] = "blendComplete";
})(AnimationEventType || (exports.AnimationEventType = AnimationEventType = {}));
/**
 * 动画控制器组件
 */
var Animator = exports.Animator = /** @class */ (function (_super) {
    __extends(Animator, _super);
    /**
     * 创建动画控制器
     * @param options 动画控制器选项
     */
    function Animator(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, Animator.type) || this;
        /** 目标实体引用 */
        _this._targetEntity = null;
        /** 动画片段映射 */
        _this.clips = new Map();
        /** 当前播放的动画片段 */
        _this.currentClip = null;
        /** 下一个要播放的动画片段（用于混合） */
        _this.nextClip = null;
        /** 当前动画状态 */
        _this.state = AnimationState.STOPPED;
        /** 当前播放时间（秒） */
        _this.time = 0;
        /** 混合开始时间（秒） */
        _this.blendStartTime = 0;
        /** 混合持续时间（秒） */
        _this.blendTime = 0;
        /** 混合因子（0-1） */
        _this.blendFactor = 0;
        /** 默认混合时间（秒） */
        _this.defaultBlendTime = 0.3;
        /** 时间缩放 */
        _this.timeScale = 1.0;
        /** 是否循环 */
        _this.loop = true;
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** Three.js动画混合器（用于兼容） */
        _this.mixer = null;
        /** Three.js动画动作映射 */
        _this.actions = new Map();
        /** 当前动画状态 */
        _this.animationState = new Map();
        /** 缓存的动画状态 */
        _this._cachedAnimationState = null;
        /** 缓存的时间 */
        _this._cachedTime = -1;
        /** 缓存的混合因子 */
        _this._cachedBlendFactor = -1;
        /** 是否需要更新 */
        _this._needsUpdate = true;
        /** 是否启用缓存 */
        _this._cacheEnabled = true;
        /** 动画参数映射 */
        _this.parameters = new Map();
        if (options.entity) {
            _this.setEntity(options.entity);
        }
        if (options.clips) {
            for (var _i = 0, _a = options.clips; _i < _a.length; _i++) {
                var clip = _a[_i];
                _this.addClip(clip);
            }
        }
        _this.defaultBlendTime = options.defaultBlendTime || 0.3;
        _this.timeScale = options.timeScale || 1.0;
        if (options.autoPlay && _this.clips.size > 0) {
            var firstClipResult = _this.clips.values().next();
            if (!firstClipResult.done && firstClipResult.value) {
                var firstClip = firstClipResult.value;
                _this.play(firstClip.name);
            }
        }
        return _this;
    }
    /**
     * 设置目标实体
     * @param entity 实体
     */
    Animator.prototype.setEntity = function (entity) {
        var _this = this;
        this._targetEntity = entity;
        _super.prototype.setEntity.call(this, entity);
        // 如果实体有Three.js对象，则创建混合器
        var object = entity.getComponent('Transform');
        if (object) {
            // 使用类型断言访问getObject3D方法
            var transformWithObject3D = object;
            if (typeof transformWithObject3D.getObject3D === 'function') {
                var object3D = transformWithObject3D.getObject3D();
                if (object3D) {
                    this.mixer = new THREE.AnimationMixer(object3D);
                    // 重新创建所有动作
                    this.actions.clear();
                    this.clips.forEach(function (clip, name) {
                        var threeClip = clip.toThreeAnimationClip();
                        var action = _this.mixer.clipAction(threeClip);
                        _this.actions.set(name, action);
                    });
                }
            }
        }
    };
    /**
     * 获取目标实体
     * @returns 实体
     */
    Animator.prototype.getTargetEntity = function () {
        return this._targetEntity;
    };
    /**
     * 添加动画片段
     * @param clip 动画片段
     */
    Animator.prototype.addClip = function (clip) {
        this.clips.set(clip.name, clip);
        // 如果有混合器，则创建动作
        if (this.mixer) {
            var threeClip = clip.toThreeAnimationClip();
            var action = this.mixer.clipAction(threeClip);
            this.actions.set(clip.name, action);
        }
    };
    /**
     * 移除动画片段
     * @param name 动画片段名称
     * @returns 是否成功移除
     */
    Animator.prototype.removeClip = function (name) {
        // 如果正在播放该片段，则停止播放
        if (this.currentClip && this.currentClip.name === name) {
            this.stop();
        }
        // 如果有混合器，则移除动作
        if (this.mixer) {
            var action = this.actions.get(name);
            if (action) {
                action.stop();
                this.actions.delete(name);
            }
        }
        return this.clips.delete(name);
    };
    /**
     * 获取动画片段
     * @param name 动画片段名称
     * @returns 动画片段，如果不存在则返回null
     */
    Animator.prototype.getClip = function (name) {
        return this.clips.get(name) || null;
    };
    /**
     * 获取所有动画片段
     * @returns 动画片段数组
     */
    Animator.prototype.getClips = function () {
        return Array.from(this.clips.values());
    };
    /**
     * 播放动画
     * @param name 动画片段名称
     * @param blendTime 混合时间（秒），如果为0则立即切换
     * @returns 是否成功开始播放
     */
    Animator.prototype.play = function (name, blendTime) {
        var clip = this.clips.get(name);
        if (!clip) {
            console.warn("\u52A8\u753B\u7247\u6BB5 \"".concat(name, "\" \u4E0D\u5B58\u5728"));
            return false;
        }
        // 如果已经在播放该片段，则不做任何操作
        if (this.currentClip && this.currentClip.name === name && this.state === AnimationState.PLAYING) {
            return true;
        }
        // 设置混合时间
        var actualBlendTime = blendTime !== undefined ? blendTime : this.defaultBlendTime;
        // 如果当前没有播放任何片段，或者混合时间为0，则直接播放
        if (!this.currentClip || actualBlendTime <= 0) {
            this.currentClip = clip;
            this.time = 0;
            this.state = AnimationState.PLAYING;
            this.loop = this.currentClip.loopMode !== AnimationClip_1.LoopMode.NONE;
            // 如果有混合器，则播放对应的动作
            if (this.mixer) {
                var action = this.actions.get(name);
                if (action) {
                    action.reset();
                    action.play();
                    // 设置循环模式
                    action.loop = this.currentClip.loopMode === AnimationClip_1.LoopMode.PING_PONG ? THREE.LoopPingPong :
                        this.currentClip.loopMode === AnimationClip_1.LoopMode.REPEAT ? THREE.LoopRepeat :
                            THREE.LoopOnce;
                    // 设置权重和时间缩放
                    action.weight = 1;
                    action.timeScale = this.timeScale * this.currentClip.speed;
                    // 停止其他所有动作
                    this.actions.forEach(function (otherAction, otherName) {
                        if (otherName !== name) {
                            otherAction.stop();
                        }
                    });
                }
            }
            this.eventEmitter.emit(AnimationEventType.START, { name: name });
            return true;
        }
        // 否则，设置为混合状态
        this.nextClip = clip;
        this.blendStartTime = this.time;
        this.blendTime = actualBlendTime;
        this.blendFactor = 0;
        this.state = AnimationState.BLENDING;
        // 如果有混合器，则混合到下一个动作
        if (this.mixer) {
            var currentAction = this.actions.get(this.currentClip.name);
            var nextAction = this.actions.get(name);
            if (currentAction && nextAction) {
                nextAction.reset();
                nextAction.play();
                // 设置循环模式
                nextAction.loop = this.nextClip.loopMode === AnimationClip_1.LoopMode.PING_PONG ? THREE.LoopPingPong :
                    this.nextClip.loopMode === AnimationClip_1.LoopMode.REPEAT ? THREE.LoopRepeat :
                        THREE.LoopOnce;
                // 设置权重和时间缩放
                nextAction.weight = 0;
                nextAction.timeScale = this.timeScale * this.nextClip.speed;
                // 交叉淡入淡出
                currentAction.crossFadeTo(nextAction, actualBlendTime, true);
            }
        }
        this.eventEmitter.emit(AnimationEventType.BLEND_START, { from: this.currentClip.name, to: name });
        return true;
    };
    /**
     * 停止播放
     */
    Animator.prototype.stop = function () {
        if (this.state === AnimationState.STOPPED) {
            return;
        }
        var previousClip = this.currentClip;
        this.state = AnimationState.STOPPED;
        this.time = 0;
        this.blendFactor = 0;
        this.nextClip = null;
        // 如果有混合器，则停止所有动作
        if (this.mixer) {
            this.actions.forEach(function (action) {
                action.stop();
            });
        }
        if (previousClip) {
            this.eventEmitter.emit(AnimationEventType.STOP, { name: previousClip.name });
        }
    };
    /**
     * 暂停播放
     */
    Animator.prototype.pause = function () {
        if (this.state !== AnimationState.PLAYING && this.state !== AnimationState.BLENDING) {
            return;
        }
        this.state = AnimationState.PAUSED;
        // 如果有混合器，则暂停所有动作
        if (this.mixer) {
            this.actions.forEach(function (action) {
                if (action.isRunning()) {
                    action.paused = true;
                }
            });
        }
        if (this.currentClip) {
            this.eventEmitter.emit(AnimationEventType.PAUSE, { name: this.currentClip.name });
        }
    };
    /**
     * 恢复播放
     */
    Animator.prototype.resume = function () {
        if (this.state !== AnimationState.PAUSED) {
            return;
        }
        this.state = this.nextClip ? AnimationState.BLENDING : AnimationState.PLAYING;
        // 如果有混合器，则恢复所有动作
        if (this.mixer) {
            this.actions.forEach(function (action) {
                if (action.paused) {
                    action.paused = false;
                }
            });
        }
        if (this.currentClip) {
            this.eventEmitter.emit(AnimationEventType.RESUME, { name: this.currentClip.name });
        }
    };
    /**
     * 设置播放时间
     * @param time 时间（秒）
     */
    Animator.prototype.setTime = function (time) {
        this.time = time;
        // 如果有混合器，则设置所有动作的时间
        if (this.mixer) {
            this.mixer.setTime(time);
        }
        // 更新动画状态
        this.updateAnimationState();
    };
    /**
     * 获取播放时间
     * @returns 时间（秒）
     */
    Animator.prototype.getTime = function () {
        return this.time;
    };
    /**
     * 设置时间缩放
     * @param timeScale 时间缩放
     */
    Animator.prototype.setTimeScale = function (timeScale) {
        var _this = this;
        this.timeScale = timeScale;
        // 如果有混合器，则设置所有动作的时间缩放
        if (this.mixer) {
            this.actions.forEach(function (action, name) {
                var clip = _this.clips.get(name);
                if (clip) {
                    action.timeScale = timeScale * clip.speed;
                }
            });
        }
    };
    /**
     * 获取时间缩放
     * @returns 时间缩放
     */
    Animator.prototype.getTimeScale = function () {
        return this.timeScale;
    };
    /**
     * 设置循环模式
     * @param loop 是否循环
     */
    Animator.prototype.setLoop = function (loop) {
        this.loop = loop;
        // 如果有当前片段，则更新其循环模式
        if (this.currentClip) {
            this.currentClip.loopMode = loop ? AnimationClip_1.LoopMode.REPEAT : AnimationClip_1.LoopMode.NONE;
            // 如果有混合器，则更新当前动作的循环模式
            if (this.mixer) {
                var action = this.actions.get(this.currentClip.name);
                if (action) {
                    action.loop = loop ? THREE.LoopRepeat : THREE.LoopOnce;
                }
            }
        }
    };
    /**
     * 获取循环模式
     * @returns 是否循环
     */
    Animator.prototype.getLoop = function () {
        return this.loop;
    };
    /**
     * 获取当前动画状态
     * @returns 动画状态
     */
    Animator.prototype.getState = function () {
        return this.state;
    };
    /**
     * 获取当前播放的动画片段
     * @returns 动画片段，如果没有则返回null
     */
    Animator.prototype.getCurrentClip = function () {
        return this.currentClip;
    };
    /**
     * 获取下一个要播放的动画片段
     * @returns 动画片段，如果没有则返回null
     */
    Animator.prototype.getNextClip = function () {
        return this.nextClip;
    };
    /**
     * 获取混合因子
     * @returns 混合因子（0-1）
     */
    Animator.prototype.getBlendFactor = function () {
        return this.blendFactor;
    };
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器函数
     */
    Animator.prototype.addListener = function (type, listener) {
        this.eventEmitter.on(type, listener);
    };
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器函数
     */
    Animator.prototype.removeListener = function (type, listener) {
        this.eventEmitter.off(type, listener);
    };
    /**
     * 更新动画
     * @param deltaTime 帧间隔时间（秒）
     */
    Animator.prototype.update = function (deltaTime) {
        if (this.state === AnimationState.STOPPED || this.state === AnimationState.PAUSED) {
            return;
        }
        // 更新时间
        var scaledDeltaTime = deltaTime * this.timeScale;
        var previousTime = this.time;
        this.time += scaledDeltaTime;
        // 如果有混合器，则更新混合器
        if (this.mixer) {
            this.mixer.update(scaledDeltaTime);
        }
        else {
            // 否则，手动更新动画状态
            // 标记需要更新
            this._needsUpdate = true;
            this.updateAnimationState();
        }
        // 处理混合状态
        var blendStateChanged = false;
        if (this.state === AnimationState.BLENDING && this.nextClip) {
            // 计算混合因子
            var previousBlendFactor = this.blendFactor;
            var blendProgress = (this.time - this.blendStartTime) / this.blendTime;
            this.blendFactor = Math.min(blendProgress, 1.0);
            // 检查混合因子是否发生显著变化
            if (Math.abs(this.blendFactor - previousBlendFactor) > 0.01) {
                this._needsUpdate = true;
                blendStateChanged = true;
            }
            // 如果混合完成，则切换到下一个片段
            if (this.blendFactor >= 1.0) {
                var previousClip = this.currentClip;
                this.currentClip = this.nextClip;
                this.nextClip = null;
                this.state = AnimationState.PLAYING;
                this.loop = this.currentClip.loopMode !== AnimationClip_1.LoopMode.NONE;
                this._needsUpdate = true;
                if (previousClip) {
                    this.eventEmitter.emit(AnimationEventType.BLEND_COMPLETE, {
                        from: previousClip.name,
                        to: this.currentClip.name
                    });
                }
            }
        }
        // 处理非循环动画的结束
        if (this.state === AnimationState.PLAYING && this.currentClip && !this.loop) {
            if (this.time >= this.currentClip.duration) {
                var clipName = this.currentClip.name;
                this.stop();
                this.eventEmitter.emit(AnimationEventType.COMPLETE, { name: clipName });
                return; // 已停止，不需要继续处理
            }
        }
        // 处理循环动画的循环事件
        if (this.state === AnimationState.PLAYING && this.currentClip && this.loop) {
            var duration = this.currentClip.duration;
            if (duration > 0) {
                var previousCycle = Math.floor(previousTime / duration);
                var currentCycle = Math.floor(this.time / duration);
                if (currentCycle > previousCycle) {
                    this.eventEmitter.emit(AnimationEventType.LOOP, {
                        name: this.currentClip.name,
                        cycle: currentCycle
                    });
                    this._needsUpdate = true; // 循环时需要更新
                }
            }
        }
        // 如果时间变化显著且不在混合状态，则标记需要更新
        if (!blendStateChanged && this.state === AnimationState.PLAYING) {
            var timeDiff = this.time - previousTime;
            if (Math.abs(timeDiff) > 0.016) { // 大于一帧的时间
                this._needsUpdate = true;
            }
        }
    };
    /**
     * 设置是否启用缓存
     * @param enabled 是否启用
     */
    Animator.prototype.setCacheEnabled = function (enabled) {
        this._cacheEnabled = enabled;
        if (!enabled) {
            this._cachedTime = -1;
            this._cachedBlendFactor = -1;
            this._cachedAnimationState = null;
        }
    };
    /**
     * 清除缓存
     */
    Animator.prototype.clearCache = function () {
        this._cachedTime = -1;
        this._cachedBlendFactor = -1;
        this._cachedAnimationState = null;
        this._needsUpdate = true;
    };
    /**
     * 获取Three.js动画混合器
     * @returns 动画混合器，如果不存在则返回null
     */
    Animator.prototype.getMixer = function () {
        return this.mixer;
    };
    /**
     * 获取指定名称的动画动作
     * @param name 动画片段名称
     * @returns 动画动作，如果不存在则返回null
     */
    Animator.prototype.getAction = function (name) {
        return this.actions.get(name) || null;
    };
    /**
     * 设置动画参数
     * @param name 参数名称
     * @param value 参数值
     */
    Animator.prototype.setParameter = function (name, value) {
        this.parameters.set(name, value);
    };
    /**
     * 获取动画参数
     * @param name 参数名称
     * @returns 参数值
     */
    Animator.prototype.getParameter = function (name) {
        return this.parameters.get(name);
    };
    /**
     * 获取所有动画参数
     * @returns 参数映射
     */
    Animator.prototype.getParameters = function () {
        return new Map(this.parameters);
    };
    /**
     * 获取骨骼
     * @returns 骨骼，如果不存在则返回null
     */
    Animator.prototype.getSkeleton = function () {
        if (!this._targetEntity)
            return null;
        var transform = this._targetEntity.getComponent('Transform');
        if (!transform)
            return null;
        var transformWithObject3D = transform;
        if (typeof transformWithObject3D.getObject3D === 'function') {
            var object3D = transformWithObject3D.getObject3D();
            if (object3D && object3D.type === 'SkinnedMesh') {
                return object3D.skeleton;
            }
        }
        return null;
    };
    /**
     * 更新动画片段
     * @param name 动画片段名称
     * @param clip 新的动画片段
     * @returns 是否成功更新
     */
    Animator.prototype.updateClip = function (name, clip) {
        // 检查片段是否存在
        if (!this.clips.has(name)) {
            console.warn("\u52A8\u753B\u7247\u6BB5 \"".concat(name, "\" \u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u66F4\u65B0"));
            return false;
        }
        // 更新片段
        this.clips.set(name, clip);
        // 如果有混合器，则更新对应的动作
        if (this.mixer) {
            // 移除旧动作
            var oldAction = this.actions.get(name);
            if (oldAction) {
                oldAction.stop();
                this.actions.delete(name);
            }
            // 创建新动作
            var threeClip = clip.toThreeAnimationClip();
            var newAction = this.mixer.clipAction(threeClip);
            this.actions.set(name, newAction);
            // 如果当前正在播放该片段，则重新开始播放
            if (this.currentClip && this.currentClip.name === name) {
                this.currentClip = clip;
                newAction.reset();
                newAction.play();
                newAction.weight = 1;
                newAction.timeScale = this.timeScale * clip.speed;
            }
        }
        return true;
    };
    /**
     * 更新动作映射
     * @param clipName 动画片段名称
     * @param action 新的动作
     */
    Animator.prototype.updateAction = function (clipName, action) {
        this.actions.set(clipName, action);
    };
    /**
     * 更新动画状态
     */
    Animator.prototype.updateAnimationState = function () {
        var _this = this;
        // 如果不需要更新且缓存有效，则直接使用缓存
        if (!this._needsUpdate && this._cacheEnabled &&
            this._cachedTime === this.time &&
            this._cachedBlendFactor === this.blendFactor &&
            this._cachedAnimationState) {
            // 复制缓存的状态
            this.animationState.clear();
            this._cachedAnimationState.forEach(function (value, key) {
                _this.animationState.set(key, _this._cloneValue(value));
            });
            // 应用动画状态到实体
            this.applyAnimationState();
            return;
        }
        // 清除之前的状态
        this.animationState.clear();
        // 如果没有当前片段，则不更新
        if (!this.currentClip) {
            return;
        }
        // 计算当前片段的时间
        var currentTime = this.time;
        var currentDuration = this.currentClip.duration;
        // 处理循环
        if (currentDuration > 0) {
            if (this.loop) {
                if (this.currentClip.loopMode === AnimationClip_1.LoopMode.REPEAT) {
                    currentTime = currentTime % currentDuration;
                }
                else if (this.currentClip.loopMode === AnimationClip_1.LoopMode.PING_PONG) {
                    var cycle = Math.floor(currentTime / currentDuration);
                    currentTime = currentTime % currentDuration;
                    if (cycle % 2 === 1) {
                        currentTime = currentDuration - currentTime;
                    }
                }
                else {
                    currentTime = Math.min(currentTime, currentDuration);
                }
            }
            else {
                currentTime = Math.min(currentTime, currentDuration);
            }
        }
        // 获取当前片段在当前时间的状态
        this.currentClip.getStateAtTime(currentTime, this.animationState);
        // 如果在混合状态，则混合下一个片段的状态
        if (this.state === AnimationState.BLENDING && this.nextClip && this.blendFactor > 0) {
            // 计算下一个片段的时间
            var nextTime = this.time - this.blendStartTime;
            var nextDuration = this.nextClip.duration;
            // 处理循环
            if (nextDuration > 0) {
                if (this.nextClip.loopMode === AnimationClip_1.LoopMode.REPEAT) {
                    nextTime = nextTime % nextDuration;
                }
                else if (this.nextClip.loopMode === AnimationClip_1.LoopMode.PING_PONG) {
                    var cycle = Math.floor(nextTime / nextDuration);
                    nextTime = nextTime % nextDuration;
                    if (cycle % 2 === 1) {
                        nextTime = nextDuration - nextTime;
                    }
                }
                else {
                    nextTime = Math.min(nextTime, nextDuration);
                }
            }
            // 获取下一个片段在当前时间的状态
            var nextState = new Map();
            this.nextClip.getStateAtTime(nextTime, nextState);
            // 混合两个状态
            nextState.forEach(function (nextValue, path) {
                var currentValue = _this.animationState.get(path);
                if (currentValue !== undefined) {
                    // 如果当前状态中有该路径，则混合值
                    var blendedValue = _this.blendValues(currentValue, nextValue, _this.blendFactor);
                    _this.animationState.set(path, blendedValue);
                }
                else {
                    // 如果当前状态中没有该路径，则直接使用下一个值
                    _this.animationState.set(path, nextValue);
                }
            });
        }
        // 更新缓存
        if (this._cacheEnabled) {
            this._cachedTime = this.time;
            this._cachedBlendFactor = this.blendFactor;
            this._cachedAnimationState = new Map();
            this.animationState.forEach(function (value, key) {
                _this._cachedAnimationState.set(key, _this._cloneValue(value));
            });
        }
        // 重置更新标志
        this._needsUpdate = false;
        // 应用动画状态到实体
        this.applyAnimationState();
    };
    /**
     * 克隆值
     * @param value 要克隆的值
     * @returns 克隆的值
     */
    Animator.prototype._cloneValue = function (value) {
        if (value instanceof THREE.Vector2) {
            return value.clone();
        }
        else if (value instanceof THREE.Vector3) {
            return value.clone();
        }
        else if (value instanceof THREE.Vector4) {
            return value.clone();
        }
        else if (value instanceof THREE.Quaternion) {
            return value.clone();
        }
        else if (value instanceof THREE.Color) {
            return value.clone();
        }
        else if (value instanceof THREE.Matrix4) {
            return value.clone();
        }
        else {
            return value;
        }
    };
    /**
     * 混合两个值
     * @param a 值A
     * @param b 值B
     * @param t 混合因子（0-1）
     * @returns 混合结果
     */
    Animator.prototype.blendValues = function (a, b, t) {
        // 根据值的类型选择不同的混合方法
        if (a instanceof THREE.Vector3 && b instanceof THREE.Vector3) {
            return new THREE.Vector3().copy(a).lerp(b, t);
        }
        else if (a instanceof THREE.Quaternion && b instanceof THREE.Quaternion) {
            return new THREE.Quaternion().copy(a).slerp(b, t);
        }
        else if (a instanceof THREE.Color && b instanceof THREE.Color) {
            return new THREE.Color().copy(a).lerp(b, t);
        }
        else if (typeof a === 'number' && typeof b === 'number') {
            return a + (b - a) * t;
        }
        else if (typeof a === 'boolean' && typeof b === 'boolean') {
            return t < 0.5 ? a : b;
        }
        else if (a && b && typeof a.lerp === 'function') {
            return a.clone().lerp(b, t);
        }
        else {
            return t < 0.5 ? a : b;
        }
    };
    /**
     * 应用动画状态到实体
     */
    Animator.prototype.applyAnimationState = function () {
        if (!this._targetEntity) {
            return;
        }
        // 获取实体的对象
        var transform = this._targetEntity.getComponent('Transform');
        if (!transform) {
            return;
        }
        // 使用类型断言访问getObject3D方法
        var transformWithObject3D = transform;
        if (typeof transformWithObject3D.getObject3D !== 'function') {
            return;
        }
        var object3D = transformWithObject3D.getObject3D();
        if (!object3D) {
            return;
        }
        // 应用动画状态到对象
        this.animationState.forEach(function (value, path) {
            // 解析路径
            var parts = path.split('.');
            var target = object3D;
            var _loop_1 = function (i) {
                var part = parts[i];
                // 如果是骨骼名称，则在骨骼列表中查找
                if (target.type === 'SkinnedMesh' && target.skeleton && i === 0) {
                    var bone = target.skeleton.bones.find(function (b) { return b.name === part; });
                    if (bone) {
                        target = bone;
                        return "continue";
                    }
                }
                // 否则，在子对象中查找
                if (target.children) {
                    var child = target.children.find(function (c) { return c.name === part; });
                    if (child) {
                        target = child;
                        return "continue";
                    }
                }
                // 如果是属性，则获取属性
                if (target[part] !== undefined) {
                    target = target[part];
                }
                else {
                    // 如果找不到目标，则跳过
                    target = null;
                    return "break";
                }
            };
            // 遍历路径，找到目标对象
            for (var i = 0; i < parts.length - 1; i++) {
                var state_1 = _loop_1(i);
                if (state_1 === "break")
                    break;
            }
            // 如果找到目标，则设置属性
            if (target) {
                var property = parts[parts.length - 1];
                target[property] = value;
            }
        });
    };
    /**
     * 销毁组件
     */
    Animator.prototype.dispose = function () {
        this.stop();
        this.clips.clear();
        this.actions.clear();
        this.animationState.clear();
        this.eventEmitter.removeAllListeners();
        if (this.mixer) {
            this.mixer.stopAllAction();
            this.mixer.uncacheRoot(this.mixer.getRoot());
            this.mixer = null;
        }
        this._targetEntity = null;
        _super.prototype.dispose.call(this);
    };
    /** 组件类型 */
    Animator.type = 'Animator';
    return Animator;
}(Component_1.Component));

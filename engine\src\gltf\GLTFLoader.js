"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GLTFLoader = void 0;
/**
 * GLTF加载器
 * 用于加载和处理GLTF模型
 */
var THREE = require("three");
var Entity_1 = require("../core/Entity");
var Transform_1 = require("../scene/Transform");
var EventEmitter_1 = require("../utils/EventEmitter");
var GLTFAnimationComponent_1 = require("./components/GLTFAnimationComponent");
var GLTFModelComponent_1 = require("./components/GLTFModelComponent");
/**
 * GLTF加载器
 */
var GLTFLoader = /** @class */ (function (_super) {
    __extends(GLTFLoader, _super);
    /**
     * 创建GLTF加载器
     * @param options 加载选项
     */
    function GLTFLoader(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this) || this;
        /** Draco加载器 */
        _this.dracoLoader = null;
        /** KTX2加载器 */
        _this.ktx2Loader = null;
        _this.options = {
            useDraco: options.useDraco !== undefined ? options.useDraco : true,
            dracoDecoderPath: options.dracoDecoderPath || 'https://www.gstatic.com/draco/versioned/decoders/1.5.6/',
            useKTX2: options.useKTX2 !== undefined ? options.useKTX2 : true,
            ktx2DecoderPath: options.ktx2DecoderPath || 'https://unpkg.com/three@0.152.2/examples/jsm/libs/basis/',
            loadAnimations: options.loadAnimations !== undefined ? options.loadAnimations : true,
            loadCameras: options.loadCameras !== undefined ? options.loadCameras : true,
            loadLights: options.loadLights !== undefined ? options.loadLights : true,
            optimizeGeometry: options.optimizeGeometry !== undefined ? options.optimizeGeometry : true,
        };
        // 创建Three.js GLTF加载器
        _this.loader = new THREE.GLTFLoader();
        // 设置Draco加载器
        if (_this.options.useDraco) {
            _this.dracoLoader = new THREE.DRACOLoader();
            _this.dracoLoader.setDecoderPath(_this.options.dracoDecoderPath);
            _this.loader.setDRACOLoader(_this.dracoLoader);
        }
        // 设置KTX2加载器
        if (_this.options.useKTX2) {
            _this.ktx2Loader = new THREE.KTX2Loader();
            _this.ktx2Loader.setTranscoderPath(_this.options.ktx2DecoderPath);
            _this.loader.setKTX2Loader(_this.ktx2Loader);
        }
        return _this;
    }
    /**
     * 加载GLTF模型
     * @param url 模型URL
     * @returns Promise，解析为加载的GLTF模型
     */
    GLTFLoader.prototype.load = function (url) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve, reject) {
                        _this.loader.load(url, function (gltf) {
                            // 优化几何体
                            if (_this.options.optimizeGeometry) {
                                _this.optimizeGeometry(gltf);
                            }
                            resolve(gltf);
                        }, function (progress) {
                            _this.emit('progress', {
                                url: url,
                                loaded: progress.loaded,
                                total: progress.total,
                                progress: progress.total > 0 ? progress.loaded / progress.total : 0
                            });
                        }, function (error) {
                            reject(new Error("\u52A0\u8F7DGLTF\u6A21\u578B\u5931\u8D25: ".concat(error.message)));
                        });
                    })];
            });
        });
    };
    /**
     * 优化几何体
     * @param gltf GLTF模型
     */
    GLTFLoader.prototype.optimizeGeometry = function (gltf) {
        // 遍历所有网格
        gltf.scene.traverse(function (node) {
            if (node instanceof THREE.Mesh) {
                var geometry = node.geometry;
                // 确保几何体有法线
                if (!geometry.attributes.normal) {
                    geometry.computeVertexNormals();
                }
                // 确保几何体有切线
                if (!geometry.attributes.tangent && node.material instanceof THREE.MeshStandardMaterial) {
                    geometry.computeTangents();
                }
            }
        });
    };
    /**
     * 创建实体
     * @param gltf GLTF模型
     * @param entity 父实体
     * @returns 创建的根实体
     */
    GLTFLoader.prototype.createEntity = function (gltf, entity) {
        // 添加GLTF模型组件
        var modelComponent = new GLTFModelComponent_1.GLTFModelComponent(gltf);
        entity.addComponent(modelComponent);
        // 添加变换组件
        if (!entity.getTransform()) {
            entity.addComponent(new Transform_1.Transform());
        }
        // 处理场景节点
        this.processNode(gltf.scene, entity, gltf);
        // 处理动画
        if (this.options.loadAnimations && gltf.animations && gltf.animations.length > 0) {
            var animationComponent = new GLTFAnimationComponent_1.GLTFAnimationComponent(gltf.animations);
            entity.addComponent(animationComponent);
        }
        return entity;
    };
    /**
     * 处理节点
     * @param node Three.js对象
     * @param parentEntity 父实体
     * @param gltf GLTF模型
     */
    GLTFLoader.prototype.processNode = function (node, parentEntity, gltf) {
        // 获取父实体的Transform组件
        var parentTransform = parentEntity.getTransform();
        if (parentTransform) {
            // 将Three.js对象添加到父Transform中
            parentTransform.getObject3D().add(node);
        }
        // 处理子节点
        for (var _i = 0, _a = node.children; _i < _a.length; _i++) {
            var child = _a[_i];
            // 创建子实体
            var childEntity = new Entity_1.Entity(child.name || 'GLTFNode');
            // 添加变换组件
            var transform = new Transform_1.Transform();
            // 从Three.js对象设置变换
            transform.getObject3D().position.copy(child.position);
            transform.getObject3D().rotation.copy(child.rotation);
            transform.getObject3D().scale.copy(child.scale);
            childEntity.addComponent(transform);
            // 处理子节点
            this.processNode(child, childEntity, gltf);
            // 添加到父实体
            parentEntity.addChild(childEntity);
        }
    };
    /**
     * 设置基础路径
     * @param path 基础路径
     */
    GLTFLoader.prototype.setPath = function (path) {
        this.loader.setPath(path);
    };
    /**
     * 设置资源路径
     * @param path 资源路径
     */
    GLTFLoader.prototype.setResourcePath = function (path) {
        this.loader.setResourcePath(path);
    };
    /**
     * 设置跨域
     * @param crossOrigin 跨域设置
     */
    GLTFLoader.prototype.setCrossOrigin = function (crossOrigin) {
        this.loader.setCrossOrigin(crossOrigin);
    };
    /**
     * 销毁加载器
     */
    GLTFLoader.prototype.dispose = function () {
        if (this.dracoLoader) {
            this.dracoLoader.dispose();
        }
        if (this.ktx2Loader) {
            this.ktx2Loader.dispose();
        }
    };
    return GLTFLoader;
}(EventEmitter_1.EventEmitter));
exports.GLTFLoader = GLTFLoader;

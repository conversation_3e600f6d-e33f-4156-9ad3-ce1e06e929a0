"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MaterialConverter = void 0;
/**
 * 材质转换器
 * 用于在不同类型的材质之间进行转换
 */
var THREE = require("three");
var DeviceCapabilities_1 = require("../../utils/DeviceCapabilities");
var MaterialFactory_1 = require("./MaterialFactory");
/**
 * 材质转换器类
 */
var MaterialConverter = /** @class */ (function () {
    /**
     * 创建材质转换器
     * @param options 材质转换器配置
     */
    function MaterialConverter(options) {
        if (options === void 0) { options = {}; }
        /** 材质转换映射 */
        this.conversionMap = new Map();
        /** 材质降级映射 */
        this.downgradeMap = new Map();
        /** 材质升级映射 */
        this.upgradeMap = new Map();
        this.deviceCapabilities = options.deviceCapabilities || new DeviceCapabilities_1.DeviceCapabilities();
        this.preserveOriginal = options.preserveOriginal !== undefined ? options.preserveOriginal : false;
        this.registerDefaultConversions();
        this.registerDefaultDowngrades();
        this.registerDefaultUpgrades();
    }
    /**
     * 注册默认转换
     */
    MaterialConverter.prototype.registerDefaultConversions = function () {
        var _this = this;
        // 注册标准材质到基础材质的转换
        this.registerConversion(MaterialFactory_1.MaterialType.STANDARD, MaterialFactory_1.MaterialType.BASIC, function (material) {
            var standardMaterial = material;
            var basicMaterial = new THREE.MeshBasicMaterial();
            // 复制基本属性
            basicMaterial.color.copy(standardMaterial.color);
            basicMaterial.opacity = standardMaterial.opacity;
            basicMaterial.transparent = standardMaterial.transparent;
            basicMaterial.side = standardMaterial.side;
            basicMaterial.alphaTest = standardMaterial.alphaTest;
            basicMaterial.blending = standardMaterial.blending;
            basicMaterial.visible = standardMaterial.visible;
            // 复制纹理
            basicMaterial.map = standardMaterial.map;
            basicMaterial.aoMap = standardMaterial.aoMap;
            basicMaterial.aoMapIntensity = standardMaterial.aoMapIntensity;
            basicMaterial.alphaMap = standardMaterial.alphaMap;
            basicMaterial.envMap = standardMaterial.envMap;
            // 复制自发光
            if (standardMaterial.emissive) {
                basicMaterial.emissive = new THREE.Color().copy(standardMaterial.emissive);
                basicMaterial.emissiveMap = standardMaterial.emissiveMap;
                basicMaterial.emissiveIntensity = standardMaterial.emissiveIntensity;
            }
            return basicMaterial;
        });
        // 注册物理材质到标准材质的转换
        this.registerConversion(MaterialFactory_1.MaterialType.PHYSICAL, MaterialFactory_1.MaterialType.STANDARD, function (material) {
            var physicalMaterial = material;
            var standardMaterial = new THREE.MeshStandardMaterial();
            // 复制基本属性
            standardMaterial.color.copy(physicalMaterial.color);
            standardMaterial.opacity = physicalMaterial.opacity;
            standardMaterial.transparent = physicalMaterial.transparent;
            standardMaterial.side = physicalMaterial.side;
            standardMaterial.alphaTest = physicalMaterial.alphaTest;
            standardMaterial.blending = physicalMaterial.blending;
            standardMaterial.visible = physicalMaterial.visible;
            // 复制标准材质特有属性
            standardMaterial.roughness = physicalMaterial.roughness;
            standardMaterial.metalness = physicalMaterial.metalness;
            standardMaterial.emissive.copy(physicalMaterial.emissive);
            standardMaterial.emissiveIntensity = physicalMaterial.emissiveIntensity;
            standardMaterial.wireframe = physicalMaterial.wireframe;
            // 复制纹理
            standardMaterial.map = physicalMaterial.map;
            standardMaterial.aoMap = physicalMaterial.aoMap;
            standardMaterial.aoMapIntensity = physicalMaterial.aoMapIntensity;
            standardMaterial.normalMap = physicalMaterial.normalMap;
            standardMaterial.normalMapType = physicalMaterial.normalMapType;
            standardMaterial.normalScale.copy(physicalMaterial.normalScale);
            standardMaterial.displacementMap = physicalMaterial.displacementMap;
            standardMaterial.displacementScale = physicalMaterial.displacementScale;
            standardMaterial.displacementBias = physicalMaterial.displacementBias;
            standardMaterial.roughnessMap = physicalMaterial.roughnessMap;
            standardMaterial.metalnessMap = physicalMaterial.metalnessMap;
            standardMaterial.alphaMap = physicalMaterial.alphaMap;
            standardMaterial.envMap = physicalMaterial.envMap;
            standardMaterial.envMapIntensity = physicalMaterial.envMapIntensity;
            standardMaterial.emissiveMap = physicalMaterial.emissiveMap;
            return standardMaterial;
        });
        // 注册标准材质到Lambert材质的转换
        this.registerConversion(MaterialFactory_1.MaterialType.STANDARD, MaterialFactory_1.MaterialType.LAMBERT, function (material) {
            var standardMaterial = material;
            var lambertMaterial = new THREE.MeshLambertMaterial();
            // 复制基本属性
            lambertMaterial.color.copy(standardMaterial.color);
            lambertMaterial.opacity = standardMaterial.opacity;
            lambertMaterial.transparent = standardMaterial.transparent;
            lambertMaterial.side = standardMaterial.side;
            lambertMaterial.alphaTest = standardMaterial.alphaTest;
            lambertMaterial.blending = standardMaterial.blending;
            lambertMaterial.visible = standardMaterial.visible;
            // 复制纹理
            lambertMaterial.map = standardMaterial.map;
            lambertMaterial.aoMap = standardMaterial.aoMap;
            lambertMaterial.aoMapIntensity = standardMaterial.aoMapIntensity;
            lambertMaterial.alphaMap = standardMaterial.alphaMap;
            lambertMaterial.envMap = standardMaterial.envMap;
            // 复制自发光
            lambertMaterial.emissive.copy(standardMaterial.emissive);
            lambertMaterial.emissiveMap = standardMaterial.emissiveMap;
            lambertMaterial.emissiveIntensity = standardMaterial.emissiveIntensity;
            // 设置反射率（基于金属度）
            lambertMaterial.reflectivity = standardMaterial.metalness;
            return lambertMaterial;
        });
        // 注册物理材质到Lambert材质的转换
        this.registerConversion(MaterialFactory_1.MaterialType.PHYSICAL, MaterialFactory_1.MaterialType.LAMBERT, function (material) {
            // 先转换为标准材质，再转换为Lambert材质
            var standardMaterial = _this.convert(material, MaterialFactory_1.MaterialType.STANDARD);
            return _this.convert(standardMaterial, MaterialFactory_1.MaterialType.LAMBERT);
        });
        // 注册Phong材质到Lambert材质的转换
        this.registerConversion(MaterialFactory_1.MaterialType.PHONG, MaterialFactory_1.MaterialType.LAMBERT, function (material) {
            var phongMaterial = material;
            var lambertMaterial = new THREE.MeshLambertMaterial();
            // 复制基本属性
            lambertMaterial.color.copy(phongMaterial.color);
            lambertMaterial.opacity = phongMaterial.opacity;
            lambertMaterial.transparent = phongMaterial.transparent;
            lambertMaterial.side = phongMaterial.side;
            lambertMaterial.alphaTest = phongMaterial.alphaTest;
            lambertMaterial.blending = phongMaterial.blending;
            lambertMaterial.visible = phongMaterial.visible;
            // 复制纹理
            lambertMaterial.map = phongMaterial.map;
            lambertMaterial.aoMap = phongMaterial.aoMap;
            lambertMaterial.aoMapIntensity = phongMaterial.aoMapIntensity;
            lambertMaterial.alphaMap = phongMaterial.alphaMap;
            lambertMaterial.envMap = phongMaterial.envMap;
            // 复制自发光
            lambertMaterial.emissive.copy(phongMaterial.emissive);
            lambertMaterial.emissiveMap = phongMaterial.emissiveMap;
            lambertMaterial.emissiveIntensity = phongMaterial.emissiveIntensity;
            // 设置反射率
            lambertMaterial.reflectivity = phongMaterial.shininess / 100;
            return lambertMaterial;
        });
    };
    /**
     * 注册默认降级
     */
    MaterialConverter.prototype.registerDefaultDowngrades = function () {
        var _this = this;
        // 注册物理材质的降级
        this.registerDowngrade(MaterialFactory_1.MaterialType.PHYSICAL, function (material) {
            return _this.convert(material, MaterialFactory_1.MaterialType.STANDARD);
        });
        // 注册标准材质的降级
        this.registerDowngrade(MaterialFactory_1.MaterialType.STANDARD, function (material) {
            return _this.convert(material, MaterialFactory_1.MaterialType.LAMBERT);
        });
        // 注册Phong材质的降级
        this.registerDowngrade(MaterialFactory_1.MaterialType.PHONG, function (material) {
            return _this.convert(material, MaterialFactory_1.MaterialType.LAMBERT);
        });
        // 注册Lambert材质的降级
        this.registerDowngrade(MaterialFactory_1.MaterialType.LAMBERT, function (material) {
            return _this.convert(material, MaterialFactory_1.MaterialType.BASIC);
        });
        // 注册卡通材质的降级
        this.registerDowngrade(MaterialFactory_1.MaterialType.TOON, function (material) {
            return _this.convert(material, MaterialFactory_1.MaterialType.LAMBERT);
        });
    };
    /**
     * 注册默认升级
     */
    MaterialConverter.prototype.registerDefaultUpgrades = function () {
        // 注册基础材质的升级
        this.registerUpgrade(MaterialFactory_1.MaterialType.BASIC, function (material) {
            var basicMaterial = material;
            var lambertMaterial = new THREE.MeshLambertMaterial();
            // 复制基本属性
            lambertMaterial.color.copy(basicMaterial.color);
            lambertMaterial.opacity = basicMaterial.opacity;
            lambertMaterial.transparent = basicMaterial.transparent;
            lambertMaterial.side = basicMaterial.side;
            lambertMaterial.alphaTest = basicMaterial.alphaTest;
            lambertMaterial.blending = basicMaterial.blending;
            lambertMaterial.visible = basicMaterial.visible;
            // 复制纹理
            lambertMaterial.map = basicMaterial.map;
            lambertMaterial.aoMap = basicMaterial.aoMap;
            lambertMaterial.aoMapIntensity = basicMaterial.aoMapIntensity;
            lambertMaterial.alphaMap = basicMaterial.alphaMap;
            lambertMaterial.envMap = basicMaterial.envMap;
            // 复制自发光
            if (basicMaterial.emissive) {
                lambertMaterial.emissive.copy(basicMaterial.emissive);
                lambertMaterial.emissiveMap = basicMaterial.emissiveMap;
                lambertMaterial.emissiveIntensity = basicMaterial.emissiveIntensity;
            }
            return lambertMaterial;
        });
        // 注册Lambert材质的升级
        this.registerUpgrade(MaterialFactory_1.MaterialType.LAMBERT, function (material) {
            var lambertMaterial = material;
            var standardMaterial = new THREE.MeshStandardMaterial();
            // 复制基本属性
            standardMaterial.color.copy(lambertMaterial.color);
            standardMaterial.opacity = lambertMaterial.opacity;
            standardMaterial.transparent = lambertMaterial.transparent;
            standardMaterial.side = lambertMaterial.side;
            standardMaterial.alphaTest = lambertMaterial.alphaTest;
            standardMaterial.blending = lambertMaterial.blending;
            standardMaterial.visible = lambertMaterial.visible;
            // 复制纹理
            standardMaterial.map = lambertMaterial.map;
            standardMaterial.aoMap = lambertMaterial.aoMap;
            standardMaterial.aoMapIntensity = lambertMaterial.aoMapIntensity;
            standardMaterial.alphaMap = lambertMaterial.alphaMap;
            standardMaterial.envMap = lambertMaterial.envMap;
            // 复制自发光
            standardMaterial.emissive.copy(lambertMaterial.emissive);
            standardMaterial.emissiveMap = lambertMaterial.emissiveMap;
            standardMaterial.emissiveIntensity = lambertMaterial.emissiveIntensity;
            // 设置粗糙度和金属度
            standardMaterial.roughness = 0.7;
            standardMaterial.metalness = lambertMaterial.reflectivity || 0.0;
            return standardMaterial;
        });
        // 注册标准材质的升级
        this.registerUpgrade(MaterialFactory_1.MaterialType.STANDARD, function (material) {
            var standardMaterial = material;
            var physicalMaterial = new THREE.MeshPhysicalMaterial();
            // 复制基本属性
            physicalMaterial.color.copy(standardMaterial.color);
            physicalMaterial.opacity = standardMaterial.opacity;
            physicalMaterial.transparent = standardMaterial.transparent;
            physicalMaterial.side = standardMaterial.side;
            physicalMaterial.alphaTest = standardMaterial.alphaTest;
            physicalMaterial.blending = standardMaterial.blending;
            physicalMaterial.visible = standardMaterial.visible;
            // 复制标准材质特有属性
            physicalMaterial.roughness = standardMaterial.roughness;
            physicalMaterial.metalness = standardMaterial.metalness;
            physicalMaterial.emissive.copy(standardMaterial.emissive);
            physicalMaterial.emissiveIntensity = standardMaterial.emissiveIntensity;
            physicalMaterial.wireframe = standardMaterial.wireframe;
            // 复制纹理
            physicalMaterial.map = standardMaterial.map;
            physicalMaterial.aoMap = standardMaterial.aoMap;
            physicalMaterial.aoMapIntensity = standardMaterial.aoMapIntensity;
            physicalMaterial.normalMap = standardMaterial.normalMap;
            physicalMaterial.normalMapType = standardMaterial.normalMapType;
            physicalMaterial.normalScale.copy(standardMaterial.normalScale);
            physicalMaterial.displacementMap = standardMaterial.displacementMap;
            physicalMaterial.displacementScale = standardMaterial.displacementScale;
            physicalMaterial.displacementBias = standardMaterial.displacementBias;
            physicalMaterial.roughnessMap = standardMaterial.roughnessMap;
            physicalMaterial.metalnessMap = standardMaterial.metalnessMap;
            physicalMaterial.alphaMap = standardMaterial.alphaMap;
            physicalMaterial.envMap = standardMaterial.envMap;
            physicalMaterial.envMapIntensity = standardMaterial.envMapIntensity;
            physicalMaterial.emissiveMap = standardMaterial.emissiveMap;
            // 设置物理材质特有属性
            physicalMaterial.clearcoat = 0.0;
            physicalMaterial.clearcoatRoughness = 0.0;
            physicalMaterial.ior = 1.5;
            physicalMaterial.reflectivity = 0.5;
            physicalMaterial.transmission = 0.0;
            physicalMaterial.thickness = 0.0;
            return physicalMaterial;
        });
    };
    /**
     * 注册材质转换
     * @param fromType 源材质类型
     * @param toType 目标材质类型
     * @param converter 转换函数
     */
    MaterialConverter.prototype.registerConversion = function (fromType, toType, converter) {
        if (!this.conversionMap.has(fromType)) {
            this.conversionMap.set(fromType, new Map());
        }
        this.conversionMap.get(fromType).set(toType, converter);
    };
    /**
     * 注册材质降级
     * @param type 材质类型
     * @param downgrader 降级函数
     */
    MaterialConverter.prototype.registerDowngrade = function (type, downgrader) {
        this.downgradeMap.set(type, downgrader);
    };
    /**
     * 注册材质升级
     * @param type 材质类型
     * @param upgrader 升级函数
     */
    MaterialConverter.prototype.registerUpgrade = function (type, upgrader) {
        this.upgradeMap.set(type, upgrader);
    };
    /**
     * 转换材质
     * @param material 材质
     * @param targetType 目标类型
     * @returns 转换后的材质
     */
    MaterialConverter.prototype.convert = function (material, targetType) {
        // 如果材质类型已经是目标类型，则直接返回
        if (this.getMaterialType(material) === targetType) {
            return material;
        }
        // 获取材质类型
        var materialType = this.getMaterialType(material);
        // 查找转换函数
        var converters = this.conversionMap.get(materialType);
        if (!converters) {
            console.warn("\u6CA1\u6709\u627E\u5230\u4ECE ".concat(materialType, " \u7C7B\u578B\u7684\u8F6C\u6362\u51FD\u6570"));
            return material;
        }
        var converter = converters.get(targetType);
        if (!converter) {
            console.warn("\u6CA1\u6709\u627E\u5230\u4ECE ".concat(materialType, " \u5230 ").concat(targetType, " \u7684\u8F6C\u6362\u51FD\u6570"));
            return material;
        }
        // 转换材质
        var convertedMaterial = converter(material);
        // 如果不保留原始材质，则释放原始材质
        if (!this.preserveOriginal) {
            material.dispose();
        }
        return convertedMaterial;
    };
    /**
     * 降级材质
     * @param material 材质
     * @returns 降级后的材质
     */
    MaterialConverter.prototype.downgrade = function (material) {
        // 获取材质类型
        var materialType = this.getMaterialType(material);
        // 查找降级函数
        var downgrader = this.downgradeMap.get(materialType);
        if (!downgrader) {
            // 如果没有找到降级函数，则尝试转换为基础材质
            if (materialType !== MaterialFactory_1.MaterialType.BASIC) {
                return this.convert(material, MaterialFactory_1.MaterialType.BASIC);
            }
            return material;
        }
        // 降级材质
        var downgradedMaterial = downgrader(material);
        // 如果不保留原始材质，则释放原始材质
        if (!this.preserveOriginal) {
            material.dispose();
        }
        return downgradedMaterial;
    };
    /**
     * 升级材质
     * @param material 材质
     * @returns 升级后的材质
     */
    MaterialConverter.prototype.upgrade = function (material) {
        // 获取材质类型
        var materialType = this.getMaterialType(material);
        // 查找升级函数
        var upgrader = this.upgradeMap.get(materialType);
        if (!upgrader) {
            // 如果没有找到升级函数，则尝试转换为物理材质
            if (materialType !== MaterialFactory_1.MaterialType.PHYSICAL) {
                return this.convert(material, MaterialFactory_1.MaterialType.PHYSICAL);
            }
            return material;
        }
        // 升级材质
        var upgradedMaterial = upgrader(material);
        // 如果不保留原始材质，则释放原始材质
        if (!this.preserveOriginal) {
            material.dispose();
        }
        return upgradedMaterial;
    };
    /**
     * 获取材质类型
     * @param material 材质
     * @returns 材质类型
     */
    MaterialConverter.prototype.getMaterialType = function (material) {
        if (material instanceof THREE.MeshBasicMaterial) {
            return MaterialFactory_1.MaterialType.BASIC;
        }
        else if (material instanceof THREE.MeshLambertMaterial) {
            return MaterialFactory_1.MaterialType.LAMBERT;
        }
        else if (material instanceof THREE.MeshPhongMaterial) {
            return MaterialFactory_1.MaterialType.PHONG;
        }
        else if (material instanceof THREE.MeshStandardMaterial) {
            if (material instanceof THREE.MeshPhysicalMaterial) {
                return MaterialFactory_1.MaterialType.PHYSICAL;
            }
            return MaterialFactory_1.MaterialType.STANDARD;
        }
        else if (material instanceof THREE.MeshToonMaterial) {
            return MaterialFactory_1.MaterialType.TOON;
        }
        else if (material instanceof THREE.MeshDepthMaterial) {
            return MaterialFactory_1.MaterialType.DEPTH;
        }
        else if (material instanceof THREE.MeshNormalMaterial) {
            return MaterialFactory_1.MaterialType.NORMAL;
        }
        else if (material instanceof THREE.LineBasicMaterial) {
            return MaterialFactory_1.MaterialType.LINE_BASIC;
        }
        else if (material instanceof THREE.LineDashedMaterial) {
            return MaterialFactory_1.MaterialType.LINE_DASHED;
        }
        else if (material instanceof THREE.PointsMaterial) {
            return MaterialFactory_1.MaterialType.POINTS;
        }
        else if (material instanceof THREE.SpriteMaterial) {
            return MaterialFactory_1.MaterialType.SPRITE;
        }
        else if (material instanceof THREE.ShaderMaterial) {
            if (material instanceof THREE.RawShaderMaterial) {
                return MaterialFactory_1.MaterialType.RAW_SHADER;
            }
            return MaterialFactory_1.MaterialType.SHADER;
        }
        else if (material instanceof THREE.ShadowMaterial) {
            return MaterialFactory_1.MaterialType.SHADOW;
        }
        else if (material instanceof THREE.MeshMatcapMaterial) {
            return MaterialFactory_1.MaterialType.MATCAP;
        }
        return 'unknown';
    };
    /**
     * 设置是否保留原始材质
     * @param preserve 是否保留
     */
    MaterialConverter.prototype.setPreserveOriginal = function (preserve) {
        this.preserveOriginal = preserve;
    };
    return MaterialConverter;
}());
exports.MaterialConverter = MaterialConverter;

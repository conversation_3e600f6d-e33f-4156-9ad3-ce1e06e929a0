"use strict";
/**
 * 网络模块索引文件
 * 导出所有网络相关的类和接口
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkTracer = exports.NetworkSimulator = exports.ServerConnectionTester = exports.BandwidthTester = exports.NetworkSecuritySystem = exports.QuadtreePartitioning = exports.NetworkUserComponent = exports.NetworkTransformComponent = exports.MessageSerializer = exports.WebRTCDataChannel = exports.WebSocketConnection = exports.NetworkConnection = exports.SyncPriorityManager = exports.NetworkPredictor = exports.NetworkAdaptiveController = exports.AdvancedBandwidthController = exports.EnhancedNetworkSystem = exports.MicroserviceClient = exports.ServiceDiscoveryClient = exports.DataCompressor = exports.BandwidthController = exports.NetworkQualityMonitor = exports.MediaStreamManager = exports.UserSessionManager = exports.NetworkEntityComponent = exports.EntitySyncManager = exports.WebRTCConnectionManager = exports.WebRTCConnection = exports.NetworkEventBuffer = exports.NetworkEventDispatcher = exports.NetworkManager = exports.NetworkSystem = void 0;
// 类型和枚举（优先导出，避免冲突）
__exportStar(require("./types"), exports);
// 核心系统
var NetworkSystem_1 = require("./NetworkSystem");
Object.defineProperty(exports, "NetworkSystem", { enumerable: true, get: function () { return NetworkSystem_1.NetworkSystem; } });
var NetworkManager_1 = require("./NetworkManager");
Object.defineProperty(exports, "NetworkManager", { enumerable: true, get: function () { return NetworkManager_1.NetworkManager; } });
var NetworkEventDispatcher_1 = require("./NetworkEventDispatcher");
Object.defineProperty(exports, "NetworkEventDispatcher", { enumerable: true, get: function () { return NetworkEventDispatcher_1.NetworkEventDispatcher; } });
var NetworkEventBuffer_1 = require("./NetworkEventBuffer");
Object.defineProperty(exports, "NetworkEventBuffer", { enumerable: true, get: function () { return NetworkEventBuffer_1.NetworkEventBuffer; } });
// WebRTC相关
var WebRTCConnection_1 = require("./WebRTCConnection");
Object.defineProperty(exports, "WebRTCConnection", { enumerable: true, get: function () { return WebRTCConnection_1.WebRTCConnection; } });
var WebRTCConnectionManager_1 = require("./WebRTCConnectionManager");
Object.defineProperty(exports, "WebRTCConnectionManager", { enumerable: true, get: function () { return WebRTCConnectionManager_1.WebRTCConnectionManager; } });
// 实体同步
var EntitySyncManager_1 = require("./EntitySyncManager");
Object.defineProperty(exports, "EntitySyncManager", { enumerable: true, get: function () { return EntitySyncManager_1.EntitySyncManager; } });
var NetworkEntityComponent_1 = require("./components/NetworkEntityComponent");
Object.defineProperty(exports, "NetworkEntityComponent", { enumerable: true, get: function () { return NetworkEntityComponent_1.NetworkEntityComponent; } });
// 用户会话
var UserSessionManager_1 = require("./UserSessionManager");
Object.defineProperty(exports, "UserSessionManager", { enumerable: true, get: function () { return UserSessionManager_1.UserSessionManager; } });
// 媒体流
var MediaStreamManager_1 = require("./MediaStreamManager");
Object.defineProperty(exports, "MediaStreamManager", { enumerable: true, get: function () { return MediaStreamManager_1.MediaStreamManager; } });
// 网络质量和带宽
var NetworkQualityMonitor_1 = require("./NetworkQualityMonitor");
Object.defineProperty(exports, "NetworkQualityMonitor", { enumerable: true, get: function () { return NetworkQualityMonitor_1.NetworkQualityMonitor; } });
var BandwidthController_1 = require("./BandwidthController");
Object.defineProperty(exports, "BandwidthController", { enumerable: true, get: function () { return BandwidthController_1.BandwidthController; } });
// 数据压缩
var DataCompressor_1 = require("./DataCompressor");
Object.defineProperty(exports, "DataCompressor", { enumerable: true, get: function () { return DataCompressor_1.DataCompressor; } });
// 服务发现和微服务通信
var ServiceDiscoveryClient_1 = require("./ServiceDiscoveryClient");
Object.defineProperty(exports, "ServiceDiscoveryClient", { enumerable: true, get: function () { return ServiceDiscoveryClient_1.ServiceDiscoveryClient; } });
var MicroserviceClient_1 = require("./MicroserviceClient");
Object.defineProperty(exports, "MicroserviceClient", { enumerable: true, get: function () { return MicroserviceClient_1.MicroserviceClient; } });
// 高级网络功能
var EnhancedNetworkSystem_1 = require("./EnhancedNetworkSystem");
Object.defineProperty(exports, "EnhancedNetworkSystem", { enumerable: true, get: function () { return EnhancedNetworkSystem_1.EnhancedNetworkSystem; } });
var AdvancedBandwidthController_1 = require("./AdvancedBandwidthController");
Object.defineProperty(exports, "AdvancedBandwidthController", { enumerable: true, get: function () { return AdvancedBandwidthController_1.AdvancedBandwidthController; } });
var NetworkAdaptiveController_1 = require("./NetworkAdaptiveController");
Object.defineProperty(exports, "NetworkAdaptiveController", { enumerable: true, get: function () { return NetworkAdaptiveController_1.NetworkAdaptiveController; } });
var NetworkPredictor_1 = require("./NetworkPredictor");
Object.defineProperty(exports, "NetworkPredictor", { enumerable: true, get: function () { return NetworkPredictor_1.NetworkPredictor; } });
var SyncPriorityManager_1 = require("./SyncPriorityManager");
Object.defineProperty(exports, "SyncPriorityManager", { enumerable: true, get: function () { return SyncPriorityManager_1.SyncPriorityManager; } });
// 网络连接
var NetworkConnection_1 = require("./NetworkConnection");
Object.defineProperty(exports, "NetworkConnection", { enumerable: true, get: function () { return NetworkConnection_1.NetworkConnection; } });
var WebSocketConnection_1 = require("./WebSocketConnection");
Object.defineProperty(exports, "WebSocketConnection", { enumerable: true, get: function () { return WebSocketConnection_1.WebSocketConnection; } });
var WebRTCDataChannel_1 = require("./WebRTCDataChannel");
Object.defineProperty(exports, "WebRTCDataChannel", { enumerable: true, get: function () { return WebRTCDataChannel_1.WebRTCDataChannel; } });
// 消息和序列化
var MessageSerializer_1 = require("./MessageSerializer");
Object.defineProperty(exports, "MessageSerializer", { enumerable: true, get: function () { return MessageSerializer_1.MessageSerializer; } });
// 组件
var NetworkTransformComponent_1 = require("./components/NetworkTransformComponent");
Object.defineProperty(exports, "NetworkTransformComponent", { enumerable: true, get: function () { return NetworkTransformComponent_1.NetworkTransformComponent; } });
var NetworkUserComponent_1 = require("./components/NetworkUserComponent");
Object.defineProperty(exports, "NetworkUserComponent", { enumerable: true, get: function () { return NetworkUserComponent_1.NetworkUserComponent; } });
// 空间分区
var QuadtreePartitioning_1 = require("./spatial/QuadtreePartitioning");
Object.defineProperty(exports, "QuadtreePartitioning", { enumerable: true, get: function () { return QuadtreePartitioning_1.QuadtreePartitioning; } });
// 网络安全和测试
var NetworkSecuritySystem_1 = require("./NetworkSecuritySystem");
Object.defineProperty(exports, "NetworkSecuritySystem", { enumerable: true, get: function () { return NetworkSecuritySystem_1.NetworkSecuritySystem; } });
var BandwidthTester_1 = require("./BandwidthTester");
Object.defineProperty(exports, "BandwidthTester", { enumerable: true, get: function () { return BandwidthTester_1.BandwidthTester; } });
var ServerConnectionTester_1 = require("./ServerConnectionTester");
Object.defineProperty(exports, "ServerConnectionTester", { enumerable: true, get: function () { return ServerConnectionTester_1.ServerConnectionTester; } });
var NetworkSimulator_1 = require("./NetworkSimulator");
Object.defineProperty(exports, "NetworkSimulator", { enumerable: true, get: function () { return NetworkSimulator_1.NetworkSimulator; } });
var NetworkTracer_1 = require("./NetworkTracer");
Object.defineProperty(exports, "NetworkTracer", { enumerable: true, get: function () { return NetworkTracer_1.NetworkTracer; } });

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BandwidthTester = void 0;
/**
 * 带宽测试器
 * 用于测试网络带宽和延迟
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var Debug_1 = require("../utils/Debug");
/**
 * 带宽测试器
 * 用于测试网络带宽和延迟
 */
var BandwidthTester = /** @class */ (function (_super) {
    __extends(BandwidthTester, _super);
    /**
     * 创建带宽测试器
     * @param config 配置
     */
    function BandwidthTester(config) {
        var _this = _super.call(this) || this;
        /** 当前测试结果 */
        _this.currentResult = {};
        /** 测试是否正在进行 */
        _this.testing = false;
        /** WebSocket连接 */
        _this.ws = null;
        /** 测试超时定时器ID */
        _this.timeoutId = null;
        /** 延迟测试结果 */
        _this.latencyResults = [];
        /** 上传测试开始时间 */
        _this.uploadStartTime = 0;
        /** 下载测试开始时间 */
        _this.downloadStartTime = 0;
        // 默认配置
        _this.config = {
            serverUrl: config.serverUrl,
            uploadSize: config.uploadSize || 1024 * 1024,
            downloadSize: config.downloadSize || 10 * 1024 * 1024,
            timeout: config.timeout || 30000,
            retries: config.retries || 3,
            detailedLogging: config.detailedLogging || false,
            concurrentConnections: config.concurrentConnections || 3,
            useWebSocket: config.useWebSocket !== undefined ? config.useWebSocket : true,
            useHttp: config.useHttp !== undefined ? config.useHttp : false,
            testUpload: config.testUpload !== undefined ? config.testUpload : true,
            testDownload: config.testDownload !== undefined ? config.testDownload : true,
            testLatency: config.testLatency !== undefined ? config.testLatency : true,
            latencyTestCount: config.latencyTestCount || 10,
        };
        if (_this.config.detailedLogging) {
            Debug_1.Debug.log('BandwidthTester', '带宽测试器已创建');
        }
        return _this;
    }
    /**
     * 开始测试
     * @returns 测试结果Promise
     */
    BandwidthTester.prototype.startTest = function () {
        return __awaiter(this, void 0, void 0, function () {
            var error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.testing) {
                            throw new Error('测试已在进行中');
                        }
                        this.testing = true;
                        this.resetTestResult();
                        // 记录测试开始时间
                        this.currentResult.startTime = Date.now();
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 8, , 9]);
                        // 设置超时
                        this.setTestTimeout();
                        // 发送测试开始事件
                        this.emit('testStart', { time: this.currentResult.startTime });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('BandwidthTester', '开始带宽测试');
                        }
                        if (!this.config.testLatency) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.testLatency()];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        if (!this.config.testDownload) return [3 /*break*/, 5];
                        return [4 /*yield*/, this.testDownloadBandwidth()];
                    case 4:
                        _a.sent();
                        _a.label = 5;
                    case 5:
                        if (!this.config.testUpload) return [3 /*break*/, 7];
                        return [4 /*yield*/, this.testUploadBandwidth()];
                    case 6:
                        _a.sent();
                        _a.label = 7;
                    case 7:
                        // 清除超时
                        this.clearTestTimeout();
                        // 记录测试结束时间
                        this.currentResult.endTime = Date.now();
                        this.currentResult.duration = this.currentResult.endTime - this.currentResult.startTime;
                        this.currentResult.success = true;
                        // 发送测试完成事件
                        this.emit('testComplete', this.getTestResult());
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('BandwidthTester', "\u5E26\u5BBD\u6D4B\u8BD5\u5B8C\u6210: \u4E0A\u4F20=".concat(this.currentResult.uploadBandwidth, "B/s, \u4E0B\u8F7D=").concat(this.currentResult.downloadBandwidth, "B/s, \u5EF6\u8FDF=").concat(this.currentResult.latency, "ms"));
                        }
                        this.testing = false;
                        return [2 /*return*/, this.getTestResult()];
                    case 8:
                        error_1 = _a.sent();
                        // 清除超时
                        this.clearTestTimeout();
                        // 记录测试结束时间
                        this.currentResult.endTime = Date.now();
                        this.currentResult.duration = this.currentResult.endTime - this.currentResult.startTime;
                        this.currentResult.success = false;
                        this.currentResult.error = error_1 instanceof Error ? error_1.message : String(error_1);
                        // 发送测试失败事件
                        this.emit('testError', { error: this.currentResult.error, result: this.getTestResult() });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('BandwidthTester', "\u5E26\u5BBD\u6D4B\u8BD5\u5931\u8D25: ".concat(this.currentResult.error));
                        }
                        this.testing = false;
                        return [2 /*return*/, this.getTestResult()];
                    case 9: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 取消测试
     */
    BandwidthTester.prototype.cancelTest = function () {
        if (!this.testing) {
            return;
        }
        // 清除超时
        this.clearTestTimeout();
        // 关闭WebSocket连接
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        // 记录测试结束时间
        this.currentResult.endTime = Date.now();
        this.currentResult.duration = this.currentResult.endTime - this.currentResult.startTime;
        this.currentResult.success = false;
        this.currentResult.error = '测试被取消';
        // 发送测试取消事件
        this.emit('testCancel', this.getTestResult());
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('BandwidthTester', '带宽测试被取消');
        }
        this.testing = false;
    };
    /**
     * 设置测试超时
     */
    BandwidthTester.prototype.setTestTimeout = function () {
        var _this = this;
        this.timeoutId = window.setTimeout(function () {
            if (_this.testing) {
                _this.cancelTest();
                _this.currentResult.error = '测试超时';
                _this.emit('testTimeout', _this.getTestResult());
                if (_this.config.detailedLogging) {
                    Debug_1.Debug.log('BandwidthTester', '带宽测试超时');
                }
            }
        }, this.config.timeout);
    };
    /**
     * 清除测试超时
     */
    BandwidthTester.prototype.clearTestTimeout = function () {
        if (this.timeoutId !== null) {
            clearTimeout(this.timeoutId);
            this.timeoutId = null;
        }
    };
    /**
     * 重置测试结果
     */
    BandwidthTester.prototype.resetTestResult = function () {
        this.currentResult = {
            uploadBandwidth: 0,
            downloadBandwidth: 0,
            latency: 0,
            jitter: 0,
            success: false,
            startTime: 0,
            endTime: 0,
            duration: 0,
        };
        this.latencyResults = [];
        this.uploadStartTime = 0;
        this.downloadStartTime = 0;
    };
    /**
     * 获取测试结果
     * @returns 测试结果
     */
    BandwidthTester.prototype.getTestResult = function () {
        return this.currentResult;
    };
    /**
     * 测试延迟
     */
    BandwidthTester.prototype.testLatency = function () {
        return __awaiter(this, void 0, void 0, function () {
            var i, latency, error_2, sum, avg_1, min, max, squaredDiffs, avgSquaredDiff, stdDev;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('BandwidthTester', '开始延迟测试');
                        }
                        // 发送延迟测试开始事件
                        this.emit('latencyTestStart');
                        this.latencyResults = [];
                        i = 0;
                        _a.label = 1;
                    case 1:
                        if (!(i < this.config.latencyTestCount)) return [3 /*break*/, 6];
                        _a.label = 2;
                    case 2:
                        _a.trys.push([2, 4, , 5]);
                        return [4 /*yield*/, this.measureLatency()];
                    case 3:
                        latency = _a.sent();
                        this.latencyResults.push(latency);
                        // 发送延迟测试进度事件
                        this.emit('latencyTestProgress', {
                            current: i + 1,
                            total: this.config.latencyTestCount,
                            latency: latency,
                        });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('BandwidthTester', "\u5EF6\u8FDF\u6D4B\u8BD5 ".concat(i + 1, "/").concat(this.config.latencyTestCount, ": ").concat(latency, "ms"));
                        }
                        return [3 /*break*/, 5];
                    case 4:
                        error_2 = _a.sent();
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('BandwidthTester', "\u5EF6\u8FDF\u6D4B\u8BD5 ".concat(i + 1, "/").concat(this.config.latencyTestCount, " \u5931\u8D25: ").concat(error_2));
                        }
                        return [3 /*break*/, 5];
                    case 5:
                        i++;
                        return [3 /*break*/, 1];
                    case 6:
                        // 计算延迟统计数据
                        if (this.latencyResults.length > 0) {
                            sum = this.latencyResults.reduce(function (acc, val) { return acc + val; }, 0);
                            avg_1 = sum / this.latencyResults.length;
                            min = Math.min.apply(Math, this.latencyResults);
                            max = Math.max.apply(Math, this.latencyResults);
                            squaredDiffs = this.latencyResults.map(function (val) { return Math.pow(val - avg_1, 2); });
                            avgSquaredDiff = squaredDiffs.reduce(function (acc, val) { return acc + val; }, 0) / squaredDiffs.length;
                            stdDev = Math.sqrt(avgSquaredDiff);
                            // 更新测试结果
                            this.currentResult.latency = avg_1;
                            this.currentResult.jitter = stdDev;
                            this.currentResult.latencyDetails = {
                                min: min,
                                max: max,
                                avg: avg_1,
                                stdDev: stdDev,
                                values: __spreadArray([], this.latencyResults, true),
                            };
                        }
                        // 发送延迟测试完成事件
                        this.emit('latencyTestComplete', {
                            latency: this.currentResult.latency,
                            jitter: this.currentResult.jitter,
                            details: this.currentResult.latencyDetails,
                        });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('BandwidthTester', "\u5EF6\u8FDF\u6D4B\u8BD5\u5B8C\u6210: \u5E73\u5747=".concat(this.currentResult.latency, "ms, \u6296\u52A8=").concat(this.currentResult.jitter, "ms"));
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 测试下载带宽
     */
    BandwidthTester.prototype.testDownloadBandwidth = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // 实现下载带宽测试
                        // 这里需要根据实际情况实现，可以使用HTTP请求或WebSocket
                        // 为简化示例，这里使用模拟数据
                        this.downloadStartTime = Date.now();
                        // 发送下载测试开始事件
                        this.emit('downloadTestStart');
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('BandwidthTester', '开始下载带宽测试');
                        }
                        // 模拟下载测试
                        return [4 /*yield*/, new Promise(function (resolve) {
                                setTimeout(function () {
                                    var downloadTime = Date.now() - _this.downloadStartTime;
                                    var downloadSpeed = _this.config.downloadSize / (downloadTime / 1000);
                                    _this.currentResult.downloadBandwidth = downloadSpeed;
                                    _this.currentResult.downloadDetails = {
                                        bytes: _this.config.downloadSize,
                                        time: downloadTime,
                                        speed: downloadSpeed,
                                    };
                                    // 发送下载测试完成事件
                                    _this.emit('downloadTestComplete', {
                                        bandwidth: downloadSpeed,
                                        details: _this.currentResult.downloadDetails,
                                    });
                                    if (_this.config.detailedLogging) {
                                        Debug_1.Debug.log('BandwidthTester', "\u4E0B\u8F7D\u5E26\u5BBD\u6D4B\u8BD5\u5B8C\u6210: ".concat(downloadSpeed, "B/s"));
                                    }
                                    resolve();
                                }, 2000); // 模拟2秒的下载测试
                            })];
                    case 1:
                        // 模拟下载测试
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 测试上传带宽
     */
    BandwidthTester.prototype.testUploadBandwidth = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // 实现上传带宽测试
                        // 这里需要根据实际情况实现，可以使用HTTP请求或WebSocket
                        // 为简化示例，这里使用模拟数据
                        this.uploadStartTime = Date.now();
                        // 发送上传测试开始事件
                        this.emit('uploadTestStart');
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('BandwidthTester', '开始上传带宽测试');
                        }
                        // 模拟上传测试
                        return [4 /*yield*/, new Promise(function (resolve) {
                                setTimeout(function () {
                                    var uploadTime = Date.now() - _this.uploadStartTime;
                                    var uploadSpeed = _this.config.uploadSize / (uploadTime / 1000);
                                    _this.currentResult.uploadBandwidth = uploadSpeed;
                                    _this.currentResult.uploadDetails = {
                                        bytes: _this.config.uploadSize,
                                        time: uploadTime,
                                        speed: uploadSpeed,
                                    };
                                    // 发送上传测试完成事件
                                    _this.emit('uploadTestComplete', {
                                        bandwidth: uploadSpeed,
                                        details: _this.currentResult.uploadDetails,
                                    });
                                    if (_this.config.detailedLogging) {
                                        Debug_1.Debug.log('BandwidthTester', "\u4E0A\u4F20\u5E26\u5BBD\u6D4B\u8BD5\u5B8C\u6210: ".concat(uploadSpeed, "B/s"));
                                    }
                                    resolve();
                                }, 1500); // 模拟1.5秒的上传测试
                            })];
                    case 1:
                        // 模拟上传测试
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 测量延迟
     * @returns 延迟（毫秒）
     */
    BandwidthTester.prototype.measureLatency = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                // 实现延迟测量
                // 这里需要根据实际情况实现，可以使用HTTP请求或WebSocket
                // 为简化示例，这里使用模拟数据
                return [2 /*return*/, new Promise(function (resolve) {
                        var startTime = Date.now();
                        // 模拟网络请求
                        setTimeout(function () {
                            var latency = Date.now() - startTime;
                            resolve(latency);
                        }, 20 + Math.random() * 100); // 模拟20-120ms的延迟
                    })];
            });
        });
    };
    /**
     * 销毁测试器
     */
    BandwidthTester.prototype.dispose = function () {
        if (this.testing) {
            this.cancelTest();
        }
        this.removeAllListeners();
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('BandwidthTester', '带宽测试器已销毁');
        }
    };
    return BandwidthTester;
}(EventEmitter_1.EventEmitter));
exports.BandwidthTester = BandwidthTester;

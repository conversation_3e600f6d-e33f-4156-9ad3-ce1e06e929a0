/**
 * 视觉脚本AI情感分析节点
 * 提供情感分析和情感驱动动画相关的节点
 */
import type { Entity } from '../../core/Entity';
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';
import { AIEmotionAnalysisSystem } from '../../ai/AIEmotionAnalysisSystem';
import { EmotionAnalysisResult } from '../../ai/EmotionAnalysisResult';
import { EmotionType } from '../../ai/EmotionType';
import { AnimatorComponent } from '../../animation/AnimatorComponent';
import { FacialAnimationComponent } from '../../animation/FacialAnimationComponent';

/**
 * 情感分析节点
 * 分析文本的情感
 */
export class EmotionAnalysisNode extends AsyncNode {
  /** 请求ID */
  private requestId: string | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要分析的文本',
      defaultValue: ''
    });

    this.addInput({
      name: 'detailed',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否返回详细结果',
      defaultValue: false
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'emotion',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '主要情感'
    });

    this.addOutput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '情感强度'
    });

    this.addOutput({
      name: 'detailedResult',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '详细分析结果'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const text = this.getInputValue('text') as string;
    const detailed = this.getInputValue('detailed') as boolean;

    // 检查输入值是否有效
    if (!text) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取情感分析系统
    const emotionSystem = this.graph.getWorld().getSystem(AIEmotionAnalysisSystem);
    if (!emotionSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 分析情感
      const result = await emotionSystem.analyzeEmotion(text, { detailed });
      
      if (result) {
        // 设置输出值
        this.setOutputValue('emotion', result.primaryEmotion);
        this.setOutputValue('intensity', result.intensity);
        
        if (detailed) {
          this.setOutputValue('detailedResult', result.detailedEmotions);
        }
        
        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('情感分析失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 情感驱动动画节点
 * 根据情感分析结果驱动角色动画
 */
export class EmotionDrivenAnimationNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'emotion',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '情感类型'
    });

    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '情感强度',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '持续时间（秒）',
      defaultValue: 3.0
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const emotion = this.getInputValue('emotion') as string;
    const intensity = this.getInputValue('intensity') as number;
    const duration = this.getInputValue('duration') as number;

    // 检查输入值是否有效
    if (!entity || !emotion) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 获取面部动画组件
      const facialComponent = entity.getComponent(FacialAnimationComponent.TYPE) as FacialAnimationComponent;
      
      // 获取动画器组件
      const animatorComponent = entity.getComponent(AnimatorComponent.TYPE) as AnimatorComponent;
      
      let success = false;
      
      // 应用面部表情
      if (facialComponent) {
        facialComponent.setEmotion(emotion as EmotionType, intensity, duration);
        success = true;
      }
      
      // 应用身体动画
      if (animatorComponent) {
        // 根据情感类型选择合适的动画
        const animationName = this.getAnimationForEmotion(emotion, intensity);
        if (animationName) {
          animatorComponent.play(animationName, {
            duration,
            weight: intensity,
            fadeIn: 0.3,
            fadeOut: 0.3
          });
          success = true;
        }
      }
      
      // 设置输出值
      this.setOutputValue('success', success);
      
      // 触发输出流程
      this.triggerFlow('flow');
      
      return success;
    } catch (error) {
      console.error('应用情感动画失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }

  /**
   * 根据情感类型获取动画名称
   * @param emotion 情感类型
   * @param intensity 情感强度
   * @returns 动画名称
   */
  private getAnimationForEmotion(emotion: string, intensity: number): string | null {
    // 根据情感类型和强度选择合适的动画
    switch (emotion) {
      case EmotionType.HAPPY:
        return intensity > 0.7 ? 'happy_high' : 'happy_low';
      case EmotionType.SAD:
        return intensity > 0.7 ? 'sad_high' : 'sad_low';
      case EmotionType.ANGRY:
        return intensity > 0.7 ? 'angry_high' : 'angry_low';
      case EmotionType.SURPRISED:
        return 'surprised';
      case EmotionType.FEARFUL:
        return 'fearful';
      case EmotionType.DISGUSTED:
        return 'disgusted';
      case EmotionType.NEUTRAL:
        return 'idle';
      default:
        return null;
    }
  }
}

/**
 * 注册AI情感节点
 * @param registry 节点注册表
 */
export function registerAIEmotionNodes(registry: NodeRegistry): void {
  // 注册情感分析节点
  registry.registerNodeType({
    type: 'ai/emotion/analyze',
    category: NodeCategory.AI,
    constructor: EmotionAnalysisNode,
    label: '情感分析',
    description: '分析文本的情感',
    icon: 'emotion',
    color: '#673AB7',
    tags: ['ai', 'emotion', 'analysis']
  });

  // 注册情感驱动动画节点
  registry.registerNodeType({
    type: 'ai/emotion/driveAnimation',
    category: NodeCategory.AI,
    constructor: EmotionDrivenAnimationNode,
    label: '情感驱动动画',
    description: '根据情感分析结果驱动角色动画',
    icon: 'animation',
    color: '#673AB7',
    tags: ['ai', 'emotion', 'animation']
  });
}

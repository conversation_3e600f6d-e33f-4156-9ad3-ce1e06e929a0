"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MediaStreamManager = exports.MediaStreamQuality = exports.MediaStreamType = void 0;
/**
 * 媒体流管理器
 * 负责管理WebRTC媒体流的获取、处理和释放
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var Debug_1 = require("../utils/Debug");
/**
 * 媒体流类型
 */
var MediaStreamType;
(function (MediaStreamType) {
    /** 音频 */
    MediaStreamType["AUDIO"] = "audio";
    /** 视频 */
    MediaStreamType["VIDEO"] = "video";
    /** 屏幕共享 */
    MediaStreamType["SCREEN_SHARE"] = "screen_share";
    /** 音频和视频 */
    MediaStreamType["AUDIO_VIDEO"] = "audio_video";
})(MediaStreamType || (exports.MediaStreamType = MediaStreamType = {}));
/**
 * 媒体流质量
 */
var MediaStreamQuality;
(function (MediaStreamQuality) {
    /** 低质量 */
    MediaStreamQuality["LOW"] = "low";
    /** 中等质量 */
    MediaStreamQuality["MEDIUM"] = "medium";
    /** 高质量 */
    MediaStreamQuality["HIGH"] = "high";
    /** 超高质量 */
    MediaStreamQuality["ULTRA"] = "ultra";
})(MediaStreamQuality || (exports.MediaStreamQuality = MediaStreamQuality = {}));
/**
 * 媒体流管理器
 * 负责管理WebRTC媒体流的获取、处理和释放
 */
var MediaStreamManager = /** @class */ (function (_super) {
    __extends(MediaStreamManager, _super);
    /**
     * 创建媒体流管理器
     * @param config 配置
     */
    function MediaStreamManager(config) {
        if (config === void 0) { config = {}; }
        var _this = _super.call(this) || this;
        /** 本地媒体流映射表 */
        _this.localStreams = new Map();
        /** 远程媒体流映射表 */
        _this.remoteStreams = new Map();
        /** 音频输入设备列表 */
        _this.audioInputDevices = [];
        /** 音频输出设备列表 */
        _this.audioOutputDevices = [];
        /** 视频输入设备列表 */
        _this.videoInputDevices = [];
        /** 当前音频输入设备ID */
        _this.currentAudioInputDeviceId = null;
        /** 当前音频输出设备ID */
        _this.currentAudioOutputDeviceId = null;
        /** 当前视频输入设备ID */
        _this.currentVideoInputDeviceId = null;
        /** 音频电平监测定时器ID */
        _this.audioLevelMonitoringTimerId = null;
        /** 音频电平分析器映射表 */
        _this.audioAnalysers = new Map();
        /** 音频上下文 */
        _this.audioContext = null;
        // 默认配置
        _this.config = __assign({ defaultAudioConstraints: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true,
            }, defaultVideoConstraints: {
                width: { ideal: 1280 },
                height: { ideal: 720 },
                frameRate: { ideal: 30 },
            }, defaultScreenShareConstraints: {
                width: { ideal: 1920 },
                height: { ideal: 1080 },
                frameRate: { ideal: 15 },
            }, enableDeviceEnumeration: true, enableDeviceChangeDetection: true, enableAudioProcessing: true, enableVideoProcessing: true, enableAutoPlay: true, enableAudioLevelMonitoring: true, audioLevelMonitoringInterval: 100 }, config);
        // 初始化
        _this.initialize();
        return _this;
    }
    /**
     * 初始化管理器
     */
    MediaStreamManager.prototype.initialize = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // 创建音频上下文
                        if (this.config.enableAudioProcessing || this.config.enableAudioLevelMonitoring) {
                            this.audioContext = new AudioContext();
                        }
                        if (!this.config.enableDeviceEnumeration) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.enumerateDevices()];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        // 监听设备变更事件
                        if (this.config.enableDeviceChangeDetection) {
                            navigator.mediaDevices.addEventListener('devicechange', this.handleDeviceChange.bind(this));
                        }
                        // 启动音频电平监测
                        if (this.config.enableAudioLevelMonitoring) {
                            this.startAudioLevelMonitoring();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 枚举媒体设备
     */
    MediaStreamManager.prototype.enumerateDevices = function () {
        return __awaiter(this, void 0, void 0, function () {
            var devices, _i, devices_1, device, deviceInfo, tempStream, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 5, , 6]);
                        return [4 /*yield*/, navigator.mediaDevices.enumerateDevices()];
                    case 1:
                        devices = _a.sent();
                        // 清空设备列表
                        this.audioInputDevices = [];
                        this.audioOutputDevices = [];
                        this.videoInputDevices = [];
                        // 分类设备
                        for (_i = 0, devices_1 = devices; _i < devices_1.length; _i++) {
                            device = devices_1[_i];
                            deviceInfo = {
                                deviceId: device.deviceId,
                                label: device.label,
                                kind: device.kind,
                                groupId: device.groupId,
                            };
                            switch (device.kind) {
                                case 'audioinput':
                                    this.audioInputDevices.push(deviceInfo);
                                    break;
                                case 'audiooutput':
                                    this.audioOutputDevices.push(deviceInfo);
                                    break;
                                case 'videoinput':
                                    this.videoInputDevices.push(deviceInfo);
                                    break;
                            }
                        }
                        if (!(devices.length > 0 && !devices[0].label)) return [3 /*break*/, 4];
                        return [4 /*yield*/, navigator.mediaDevices.getUserMedia({ audio: true, video: true })];
                    case 2:
                        tempStream = _a.sent();
                        // 停止所有轨道
                        tempStream.getTracks().forEach(function (track) { return track.stop(); });
                        // 重新枚举设备
                        return [4 /*yield*/, this.enumerateDevices()];
                    case 3:
                        // 重新枚举设备
                        _a.sent();
                        return [2 /*return*/];
                    case 4:
                        // 设置默认设备
                        if (this.audioInputDevices.length > 0 && !this.currentAudioInputDeviceId) {
                            this.currentAudioInputDeviceId = this.audioInputDevices[0].deviceId;
                        }
                        if (this.audioOutputDevices.length > 0 && !this.currentAudioOutputDeviceId) {
                            this.currentAudioOutputDeviceId = this.audioOutputDevices[0].deviceId;
                        }
                        if (this.videoInputDevices.length > 0 && !this.currentVideoInputDeviceId) {
                            this.currentVideoInputDeviceId = this.videoInputDevices[0].deviceId;
                        }
                        // 触发设备枚举事件
                        this.emit('devicesEnumerated', {
                            audioInputDevices: this.audioInputDevices,
                            audioOutputDevices: this.audioOutputDevices,
                            videoInputDevices: this.videoInputDevices,
                        });
                        return [3 /*break*/, 6];
                    case 5:
                        error_1 = _a.sent();
                        Debug_1.Debug.error('MediaStreamManager', 'Failed to enumerate devices:', error_1);
                        this.emit('error', 'deviceEnumeration', error_1);
                        return [3 /*break*/, 6];
                    case 6: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 处理设备变更事件
     */
    MediaStreamManager.prototype.handleDeviceChange = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: 
                    // 重新枚举设备
                    return [4 /*yield*/, this.enumerateDevices()];
                    case 1:
                        // 重新枚举设备
                        _a.sent();
                        // 触发设备变更事件
                        this.emit('deviceChange', {
                            audioInputDevices: this.audioInputDevices,
                            audioOutputDevices: this.audioOutputDevices,
                            videoInputDevices: this.videoInputDevices,
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 启动音频电平监测
     */
    MediaStreamManager.prototype.startAudioLevelMonitoring = function () {
        var _this = this;
        if (this.audioLevelMonitoringTimerId !== null) {
            return;
        }
        this.audioLevelMonitoringTimerId = window.setInterval(function () {
            _this.updateAudioLevels();
        }, this.config.audioLevelMonitoringInterval);
    };
    /**
     * 停止音频电平监测
     */
    MediaStreamManager.prototype.stopAudioLevelMonitoring = function () {
        if (this.audioLevelMonitoringTimerId !== null) {
            clearInterval(this.audioLevelMonitoringTimerId);
            this.audioLevelMonitoringTimerId = null;
        }
    };
    /**
     * 更新音频电平
     */
    MediaStreamManager.prototype.updateAudioLevels = function () {
        // 遍历所有音频分析器
        for (var _i = 0, _a = this.audioAnalysers.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], streamId = _b[0], analyser = _b[1];
            // 获取音频数据
            var dataArray = new Uint8Array(analyser.frequencyBinCount);
            analyser.getByteFrequencyData(dataArray);
            // 计算平均音量
            var sum = 0;
            for (var _c = 0, dataArray_1 = dataArray; _c < dataArray_1.length; _c++) {
                var value = dataArray_1[_c];
                sum += value;
            }
            var average = sum / dataArray.length;
            // 归一化到0-1范围
            var level = average / 255;
            // 触发音频电平事件
            this.emit('audioLevel', streamId, level);
        }
    };
    /**
     * 创建音频分析器
     * @param stream 媒体流
     * @returns 音频分析器
     */
    MediaStreamManager.prototype.createAudioAnalyser = function (stream) {
        if (!this.audioContext) {
            return null;
        }
        // 获取音频轨道
        var audioTrack = stream.getAudioTracks()[0];
        if (!audioTrack) {
            return null;
        }
        try {
            // 创建媒体流源
            var source = this.audioContext.createMediaStreamSource(stream);
            // 创建分析器
            var analyser = this.audioContext.createAnalyser();
            analyser.fftSize = 256;
            analyser.smoothingTimeConstant = 0.5;
            // 连接源到分析器
            source.connect(analyser);
            return analyser;
        }
        catch (error) {
            Debug_1.Debug.error('MediaStreamManager', 'Failed to create audio analyser:', error);
            return null;
        }
    };
    /**
     * 获取本地媒体流
     * @param type 媒体流类型
     * @param config 媒体流配置
     * @returns 媒体流信息
     */
    MediaStreamManager.prototype.getLocalStream = function (type, config) {
        if (config === void 0) { config = {}; }
        return __awaiter(this, void 0, void 0, function () {
            var mergedConfig, constraints, stream, streamInfo, analyser, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        mergedConfig = __assign({ audioConstraints: this.config.defaultAudioConstraints, videoConstraints: this.config.defaultVideoConstraints, screenShareConstraints: this.config.defaultScreenShareConstraints, echoCancellation: true, noiseSuppression: true, autoGainControl: true, stereo: false, autoPlay: this.config.enableAutoPlay, muted: false, mirror: true, videoQuality: MediaStreamQuality.HIGH }, config);
                        constraints = {};
                        switch (type) {
                            case MediaStreamType.AUDIO:
                                constraints.audio = this.buildAudioConstraints(mergedConfig);
                                break;
                            case MediaStreamType.VIDEO:
                                constraints.video = this.buildVideoConstraints(mergedConfig);
                                break;
                            case MediaStreamType.AUDIO_VIDEO:
                                constraints.audio = this.buildAudioConstraints(mergedConfig);
                                constraints.video = this.buildVideoConstraints(mergedConfig);
                                break;
                            case MediaStreamType.SCREEN_SHARE:
                                return [2 /*return*/, this.getScreenShareStream(mergedConfig)];
                        }
                        return [4 /*yield*/, navigator.mediaDevices.getUserMedia(constraints)];
                    case 1:
                        stream = _a.sent();
                        streamInfo = {
                            id: stream.id,
                            type: type,
                            quality: mergedConfig.videoQuality || MediaStreamQuality.HIGH,
                            stream: stream,
                            audioTrack: stream.getAudioTracks()[0],
                            videoTrack: stream.getVideoTracks()[0],
                            enabled: true,
                            muted: mergedConfig.muted || false,
                            paused: false,
                            createdAt: Date.now(),
                            config: mergedConfig,
                        };
                        // 添加到本地媒体流映射表
                        this.localStreams.set(stream.id, streamInfo);
                        // 如果启用音频电平监测，则创建音频分析器
                        if (this.config.enableAudioLevelMonitoring && streamInfo.audioTrack) {
                            analyser = this.createAudioAnalyser(stream);
                            if (analyser) {
                                this.audioAnalysers.set(stream.id, analyser);
                            }
                        }
                        // 触发本地流添加事件
                        this.emit('localStreamAdded', streamInfo);
                        return [2 /*return*/, streamInfo];
                    case 2:
                        error_2 = _a.sent();
                        Debug_1.Debug.error('MediaStreamManager', "Failed to get local ".concat(type, " stream:"), error_2);
                        this.emit('error', 'getLocalStream', error_2);
                        throw error_2;
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 获取屏幕共享流
     * @param config 媒体流配置
     * @returns 媒体流信息
     */
    MediaStreamManager.prototype.getScreenShareStream = function (config) {
        return __awaiter(this, void 0, void 0, function () {
            var stream_1, streamInfo, error_3;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, navigator.mediaDevices.getDisplayMedia({
                                video: config.screenShareConstraints || this.config.defaultScreenShareConstraints,
                                audio: config.audioConstraints || false,
                            })];
                    case 1:
                        stream_1 = _a.sent();
                        streamInfo = {
                            id: stream_1.id,
                            type: MediaStreamType.SCREEN_SHARE,
                            quality: config.videoQuality || MediaStreamQuality.HIGH,
                            stream: stream_1,
                            audioTrack: stream_1.getAudioTracks()[0],
                            videoTrack: stream_1.getVideoTracks()[0],
                            enabled: true,
                            muted: config.muted || false,
                            paused: false,
                            createdAt: Date.now(),
                            config: config,
                        };
                        // 添加到本地媒体流映射表
                        this.localStreams.set(stream_1.id, streamInfo);
                        // 监听屏幕共享结束事件
                        if (streamInfo.videoTrack) {
                            streamInfo.videoTrack.addEventListener('ended', function () {
                                _this.stopLocalStream(stream_1.id);
                            });
                        }
                        // 触发本地流添加事件
                        this.emit('localStreamAdded', streamInfo);
                        return [2 /*return*/, streamInfo];
                    case 2:
                        error_3 = _a.sent();
                        Debug_1.Debug.error('MediaStreamManager', 'Failed to get screen share stream:', error_3);
                        this.emit('error', 'getScreenShareStream', error_3);
                        throw error_3;
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 构建音频约束
     * @param config 媒体流配置
     * @returns 音频约束
     */
    MediaStreamManager.prototype.buildAudioConstraints = function (config) {
        var constraints = __assign(__assign({}, config.audioConstraints), { echoCancellation: config.echoCancellation, noiseSuppression: config.noiseSuppression, autoGainControl: config.autoGainControl });
        // 如果指定了设备ID，则使用指定的设备
        if (this.currentAudioInputDeviceId) {
            constraints.deviceId = { exact: this.currentAudioInputDeviceId };
        }
        return constraints;
    };
    /**
     * 构建视频约束
     * @param config 媒体流配置
     * @returns 视频约束
     */
    MediaStreamManager.prototype.buildVideoConstraints = function (config) {
        var constraints = __assign({}, config.videoConstraints);
        // 根据质量设置分辨率和帧率
        switch (config.videoQuality) {
            case MediaStreamQuality.LOW:
                constraints.width = { ideal: config.width || 640 };
                constraints.height = { ideal: config.height || 360 };
                constraints.frameRate = { ideal: config.frameRate || 15 };
                break;
            case MediaStreamQuality.MEDIUM:
                constraints.width = { ideal: config.width || 960 };
                constraints.height = { ideal: config.height || 540 };
                constraints.frameRate = { ideal: config.frameRate || 24 };
                break;
            case MediaStreamQuality.HIGH:
                constraints.width = { ideal: config.width || 1280 };
                constraints.height = { ideal: config.height || 720 };
                constraints.frameRate = { ideal: config.frameRate || 30 };
                break;
            case MediaStreamQuality.ULTRA:
                constraints.width = { ideal: config.width || 1920 };
                constraints.height = { ideal: config.height || 1080 };
                constraints.frameRate = { ideal: config.frameRate || 60 };
                break;
        }
        // 如果指定了设备ID，则使用指定的设备
        if (this.currentVideoInputDeviceId) {
            constraints.deviceId = { exact: this.currentVideoInputDeviceId };
        }
        return constraints;
    };
    /**
     * 停止本地媒体流
     * @param streamId 流ID
     * @returns 是否成功停止
     */
    MediaStreamManager.prototype.stopLocalStream = function (streamId) {
        var streamInfo = this.localStreams.get(streamId);
        if (!streamInfo) {
            return false;
        }
        // 停止所有轨道
        streamInfo.stream.getTracks().forEach(function (track) { return track.stop(); });
        // 从本地媒体流映射表中移除
        this.localStreams.delete(streamId);
        // 移除音频分析器
        this.audioAnalysers.delete(streamId);
        // 触发本地流移除事件
        this.emit('localStreamRemoved', streamInfo);
        return true;
    };
    /**
     * 停止所有本地媒体流
     */
    MediaStreamManager.prototype.stopAllLocalStreams = function () {
        for (var _i = 0, _a = this.localStreams.keys(); _i < _a.length; _i++) {
            var streamId = _a[_i];
            this.stopLocalStream(streamId);
        }
    };
    /**
     * 添加远程媒体流
     * @param stream 媒体流
     * @param userId 用户ID
     * @param type 媒体流类型
     * @param config 媒体流配置
     * @returns 媒体流信息
     */
    MediaStreamManager.prototype.addRemoteStream = function (stream, userId, type, config) {
        if (type === void 0) { type = MediaStreamType.AUDIO_VIDEO; }
        if (config === void 0) { config = {}; }
        // 创建媒体流信息
        var streamInfo = {
            id: stream.id,
            type: type,
            quality: config.videoQuality || MediaStreamQuality.HIGH,
            stream: stream,
            audioTrack: stream.getAudioTracks()[0],
            videoTrack: stream.getVideoTracks()[0],
            enabled: true,
            muted: config.muted || false,
            paused: false,
            createdAt: Date.now(),
            config: config,
            userId: userId,
        };
        // 添加到远程媒体流映射表
        this.remoteStreams.set(stream.id, streamInfo);
        // 如果启用音频电平监测，则创建音频分析器
        if (this.config.enableAudioLevelMonitoring && streamInfo.audioTrack) {
            var analyser = this.createAudioAnalyser(stream);
            if (analyser) {
                this.audioAnalysers.set(stream.id, analyser);
            }
        }
        // 触发远程流添加事件
        this.emit('remoteStreamAdded', streamInfo);
        return streamInfo;
    };
    /**
     * 移除远程媒体流
     * @param streamId 流ID
     * @returns 是否成功移除
     */
    MediaStreamManager.prototype.removeRemoteStream = function (streamId) {
        var streamInfo = this.remoteStreams.get(streamId);
        if (!streamInfo) {
            return false;
        }
        // 从远程媒体流映射表中移除
        this.remoteStreams.delete(streamId);
        // 移除音频分析器
        this.audioAnalysers.delete(streamId);
        // 触发远程流移除事件
        this.emit('remoteStreamRemoved', streamInfo);
        return true;
    };
    /**
     * 获取本地媒体流信息
     * @param streamId 流ID
     * @returns 媒体流信息
     */
    MediaStreamManager.prototype.getLocalStreamInfo = function (streamId) {
        return this.localStreams.get(streamId);
    };
    /**
     * 获取远程媒体流信息
     * @param streamId 流ID
     * @returns 媒体流信息
     */
    MediaStreamManager.prototype.getRemoteStreamInfo = function (streamId) {
        return this.remoteStreams.get(streamId);
    };
    /**
     * 获取所有本地媒体流
     * @returns 媒体流信息列表
     */
    MediaStreamManager.prototype.getAllLocalStreams = function () {
        return Array.from(this.localStreams.values());
    };
    /**
     * 获取所有远程媒体流
     * @returns 媒体流信息列表
     */
    MediaStreamManager.prototype.getAllRemoteStreams = function () {
        return Array.from(this.remoteStreams.values());
    };
    /**
     * 获取用户的远程媒体流
     * @param userId 用户ID
     * @returns 媒体流信息列表
     */
    MediaStreamManager.prototype.getUserRemoteStreams = function (userId) {
        return Array.from(this.remoteStreams.values()).filter(function (stream) { return stream.userId === userId; });
    };
    /**
     * 设置音频输入设备
     * @param deviceId 设备ID
     */
    MediaStreamManager.prototype.setAudioInputDevice = function (deviceId) {
        this.currentAudioInputDeviceId = deviceId;
        this.emit('audioInputDeviceChanged', deviceId);
    };
    /**
     * 设置音频输出设备
     * @param deviceId 设备ID
     */
    MediaStreamManager.prototype.setAudioOutputDevice = function (deviceId) {
        this.currentAudioOutputDeviceId = deviceId;
        this.emit('audioOutputDeviceChanged', deviceId);
    };
    /**
     * 设置视频输入设备
     * @param deviceId 设备ID
     */
    MediaStreamManager.prototype.setVideoInputDevice = function (deviceId) {
        this.currentVideoInputDeviceId = deviceId;
        this.emit('videoInputDeviceChanged', deviceId);
    };
    /**
     * 获取音频输入设备列表
     * @returns 设备列表
     */
    MediaStreamManager.prototype.getAudioInputDevices = function () {
        return this.audioInputDevices;
    };
    /**
     * 获取音频输出设备列表
     * @returns 设备列表
     */
    MediaStreamManager.prototype.getAudioOutputDevices = function () {
        return this.audioOutputDevices;
    };
    /**
     * 获取视频输入设备列表
     * @returns 设备列表
     */
    MediaStreamManager.prototype.getVideoInputDevices = function () {
        return this.videoInputDevices;
    };
    /**
     * 销毁管理器
     */
    MediaStreamManager.prototype.dispose = function () {
        // 停止所有本地媒体流
        this.stopAllLocalStreams();
        // 清空远程媒体流
        this.remoteStreams.clear();
        // 停止音频电平监测
        this.stopAudioLevelMonitoring();
        // 关闭音频上下文
        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }
        // 移除设备变更事件监听器
        if (this.config.enableDeviceChangeDetection) {
            navigator.mediaDevices.removeEventListener('devicechange', this.handleDeviceChange.bind(this));
        }
        // 移除所有监听器
        this.removeAllListeners();
    };
    return MediaStreamManager;
}(EventEmitter_1.EventEmitter));
exports.MediaStreamManager = MediaStreamManager;

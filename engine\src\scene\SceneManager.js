"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SceneManager = exports.SceneTransitionType = void 0;
/**
 * 场景管理器
 * 负责场景的加载、卸载和切换
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var ScenePreloader_1 = require("./ScenePreloader");
/**
 * 场景过渡类型
 */
var SceneTransitionType;
(function (SceneTransitionType) {
    /** 无过渡 */
    SceneTransitionType["NONE"] = "none";
    /** 淡入淡出 */
    SceneTransitionType["FADE"] = "fade";
    /** 交叉淡入淡出 */
    SceneTransitionType["CROSS_FADE"] = "crossFade";
    /** 滑动 */
    SceneTransitionType["SLIDE"] = "slide";
    /** 缩放 */
    SceneTransitionType["ZOOM"] = "zoom";
    /** 自定义 */
    SceneTransitionType["CUSTOM"] = "custom";
})(SceneTransitionType || (exports.SceneTransitionType = SceneTransitionType = {}));
/**
 * 场景管理器
 */
var SceneManager = /** @class */ (function (_super) {
    __extends(SceneManager, _super);
    /**
     * 创建场景管理器实例
     * @param options 场景管理器选项
     */
    function SceneManager(options) {
        var _this = _super.call(this) || this;
        /** 资产管理器 */
        _this.assetManager = null;
        /** 场景预加载器 */
        _this.scenePreloader = null;
        /** 场景缓存 */
        _this.sceneCache = new Map();
        /** 场景访问时间 */
        _this.sceneAccessTimes = new Map();
        /** 当前场景 */
        _this.currentScene = null;
        /** 上一个场景 */
        _this.previousScene = null;
        /** 是否正在切换场景 */
        _this.isTransitioning = false;
        /** 是否正在加载场景 */
        _this.isLoading = false;
        /** 加载界面元素 */
        _this.loadingScreen = null;
        /** 加载进度元素 */
        _this.loadingProgress = null;
        /** 是否已初始化 */
        _this.initialized = false;
        _this.world = options.world;
        _this.assetManager = options.assetManager || null;
        _this.defaultTransition = options.defaultTransition || {
            type: SceneTransitionType.FADE,
            duration: 500
        };
        _this.enableSceneCache = options.enableSceneCache !== undefined ? options.enableSceneCache : true;
        _this.maxSceneCacheCount = options.maxSceneCacheCount || 5;
        // 如果有资产管理器，则创建场景预加载器
        if (_this.assetManager) {
            _this.scenePreloader = new ScenePreloader_1.ScenePreloader({
                assetManager: _this.assetManager,
                autoAnalyzeResources: true,
                autoRegisterResources: true,
                debug: false
            });
        }
        // 创建加载界面
        _this.createLoadingScreen();
        return _this;
    }
    /**
     * 初始化场景管理器
     */
    SceneManager.prototype.initialize = function () {
        var _this = this;
        if (this.initialized) {
            return;
        }
        // 获取当前场景
        this.currentScene = this.world.getActiveScene();
        // 监听世界场景变化事件
        this.world.on('sceneChanged', function (newScene, oldScene) {
            _this.previousScene = oldScene;
            _this.currentScene = newScene;
            // 更新场景访问时间
            if (newScene && newScene.id) {
                _this.sceneAccessTimes.set(newScene.id, Date.now());
            }
            // 发出场景变化事件
            _this.emit('sceneChanged', newScene, oldScene);
        });
        // 初始化场景预加载器
        if (this.scenePreloader) {
            this.scenePreloader.initialize();
            // 监听预加载器事件
            this.scenePreloader.on('loadProgress', function (data) {
                _this.emit('resourceLoadProgress', data);
            });
            this.scenePreloader.on('loadComplete', function (data) {
                _this.emit('resourceLoadComplete', data);
            });
            this.scenePreloader.on('loadError', function (data) {
                _this.emit('resourceLoadError', data);
            });
        }
        this.initialized = true;
        this.emit('initialized');
    };
    /**
     * 创建加载界面
     */
    SceneManager.prototype.createLoadingScreen = function () {
        // 创建加载界面容器
        this.loadingScreen = document.createElement('div');
        this.loadingScreen.style.position = 'fixed';
        this.loadingScreen.style.top = '0';
        this.loadingScreen.style.left = '0';
        this.loadingScreen.style.width = '100%';
        this.loadingScreen.style.height = '100%';
        this.loadingScreen.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        this.loadingScreen.style.display = 'flex';
        this.loadingScreen.style.flexDirection = 'column';
        this.loadingScreen.style.justifyContent = 'center';
        this.loadingScreen.style.alignItems = 'center';
        this.loadingScreen.style.zIndex = '9999';
        this.loadingScreen.style.opacity = '0';
        this.loadingScreen.style.transition = 'opacity 0.3s ease-in-out';
        this.loadingScreen.style.pointerEvents = 'none';
        // 创建加载文本
        var loadingText = document.createElement('div');
        loadingText.textContent = '加载中...';
        loadingText.style.color = 'white';
        loadingText.style.fontSize = '24px';
        loadingText.style.marginBottom = '20px';
        this.loadingScreen.appendChild(loadingText);
        // 创建加载进度条容器
        var progressContainer = document.createElement('div');
        progressContainer.style.width = '300px';
        progressContainer.style.height = '10px';
        progressContainer.style.backgroundColor = '#333';
        progressContainer.style.borderRadius = '5px';
        progressContainer.style.overflow = 'hidden';
        this.loadingScreen.appendChild(progressContainer);
        // 创建加载进度条
        this.loadingProgress = document.createElement('div');
        this.loadingProgress.style.width = '0%';
        this.loadingProgress.style.height = '100%';
        this.loadingProgress.style.backgroundColor = '#0095dd';
        this.loadingProgress.style.transition = 'width 0.3s ease-in-out';
        progressContainer.appendChild(this.loadingProgress);
        // 添加到文档
        document.body.appendChild(this.loadingScreen);
    };
    /**
     * 显示加载界面
     */
    SceneManager.prototype.showLoadingScreen = function () {
        if (!this.loadingScreen)
            return;
        this.loadingScreen.style.opacity = '1';
        this.loadingScreen.style.pointerEvents = 'auto';
        // 重置进度条
        if (this.loadingProgress) {
            this.loadingProgress.style.width = '0%';
        }
    };
    /**
     * 隐藏加载界面
     */
    SceneManager.prototype.hideLoadingScreen = function () {
        if (!this.loadingScreen)
            return;
        this.loadingScreen.style.opacity = '0';
        this.loadingScreen.style.pointerEvents = 'none';
    };
    /**
     * 更新加载进度
     * @param progress 进度（0-1）
     */
    SceneManager.prototype.updateLoadingProgress = function (progress) {
        if (!this.loadingProgress)
            return;
        this.loadingProgress.style.width = "".concat(progress * 100, "%");
    };
    /**
     * 加载场景
     * @param sceneId 场景ID
     * @param options 加载选项
     * @returns Promise，解析为加载的场景
     */
    SceneManager.prototype.loadScene = function (sceneId, options) {
        if (options === void 0) { options = {}; }
        return __awaiter(this, void 0, void 0, function () {
            var mergedOptions, cachedScene, sceneGraph, scene, error_1, sceneGraph, error_2;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // 如果正在加载或切换场景，则抛出错误
                        if (this.isLoading || this.isTransitioning) {
                            throw new Error('已有场景正在加载或切换');
                        }
                        this.isLoading = true;
                        mergedOptions = {
                            setActive: options.setActive !== undefined ? options.setActive : true,
                            showLoadingScreen: options.showLoadingScreen !== undefined ? options.showLoadingScreen : true,
                            preloadAssets: options.preloadAssets !== undefined ? options.preloadAssets : true,
                            initializeSceneGraph: options.initializeSceneGraph !== undefined ? options.initializeSceneGraph : true,
                            transition: options.transition || this.defaultTransition,
                            onLoaded: options.onLoaded,
                            onProgress: options.onProgress,
                            onError: options.onError
                        };
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 11, , 12]);
                        // 显示加载界面
                        if (mergedOptions.showLoadingScreen) {
                            this.showLoadingScreen();
                        }
                        if (!(this.enableSceneCache && this.sceneCache.has(sceneId))) return [3 /*break*/, 4];
                        cachedScene = this.sceneCache.get(sceneId);
                        // 更新场景访问时间
                        this.sceneAccessTimes.set(sceneId, Date.now());
                        // 如果需要初始化场景图，则初始化
                        if (mergedOptions.initializeSceneGraph) {
                            sceneGraph = cachedScene.getSceneGraph();
                            if (sceneGraph) {
                                sceneGraph.initialize();
                            }
                        }
                        if (!mergedOptions.setActive) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.transitionToScene(cachedScene, mergedOptions.transition)];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        // 调用加载完成回调
                        if (mergedOptions.onLoaded) {
                            mergedOptions.onLoaded(cachedScene);
                        }
                        // 隐藏加载界面
                        if (mergedOptions.showLoadingScreen) {
                            this.hideLoadingScreen();
                        }
                        this.isLoading = false;
                        return [2 /*return*/, cachedScene];
                    case 4:
                        scene = this.world.getScene(sceneId);
                        if (!scene) {
                            throw new Error("\u627E\u4E0D\u5230ID\u4E3A ".concat(sceneId, " \u7684\u573A\u666F"));
                        }
                        if (!(mergedOptions.preloadAssets && this.scenePreloader)) return [3 /*break*/, 8];
                        _a.label = 5;
                    case 5:
                        _a.trys.push([5, 7, , 8]);
                        // 使用场景预加载器预加载场景资源
                        return [4 /*yield*/, this.scenePreloader.preloadScene(scene, function (progressInfo) {
                                // 更新加载进度
                                var progress = progressInfo.progress;
                                if (mergedOptions.onProgress) {
                                    mergedOptions.onProgress(progress);
                                }
                                _this.updateLoadingProgress(progress);
                            })];
                    case 6:
                        // 使用场景预加载器预加载场景资源
                        _a.sent();
                        return [3 /*break*/, 8];
                    case 7:
                        error_1 = _a.sent();
                        console.error('预加载场景资源失败:', error_1);
                        return [3 /*break*/, 8];
                    case 8:
                        if (!mergedOptions.setActive) return [3 /*break*/, 10];
                        return [4 /*yield*/, this.transitionToScene(scene, mergedOptions.transition)];
                    case 9:
                        _a.sent();
                        _a.label = 10;
                    case 10:
                        // 如果需要初始化场景图，则初始化
                        if (mergedOptions.initializeSceneGraph) {
                            sceneGraph = scene.getSceneGraph();
                            if (sceneGraph) {
                                sceneGraph.initialize();
                            }
                        }
                        // 如果启用场景缓存，则添加到缓存
                        if (this.enableSceneCache) {
                            this.addToSceneCache(scene);
                        }
                        // 调用加载完成回调
                        if (mergedOptions.onLoaded) {
                            mergedOptions.onLoaded(scene);
                        }
                        // 隐藏加载界面
                        if (mergedOptions.showLoadingScreen) {
                            this.hideLoadingScreen();
                        }
                        this.isLoading = false;
                        return [2 /*return*/, scene];
                    case 11:
                        error_2 = _a.sent();
                        // 调用加载错误回调
                        if (mergedOptions.onError) {
                            mergedOptions.onError(error_2);
                        }
                        // 隐藏加载界面
                        if (mergedOptions.showLoadingScreen) {
                            this.hideLoadingScreen();
                        }
                        this.isLoading = false;
                        throw error_2;
                    case 12: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 切换到场景
     * @param scene 目标场景
     * @param transitionOptions 过渡选项
     * @returns Promise，解析为切换后的场景
     */
    SceneManager.prototype.transitionToScene = function (scene, transitionOptions) {
        if (transitionOptions === void 0) { transitionOptions = this.defaultTransition; }
        return __awaiter(this, void 0, void 0, function () {
            var fromScene, error_3;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // 如果正在切换场景，则抛出错误
                        if (this.isTransitioning) {
                            throw new Error('已有场景正在切换');
                        }
                        this.isTransitioning = true;
                        fromScene = this.currentScene;
                        // 如果目标场景与当前场景相同，则不做任何操作
                        if (fromScene === scene) {
                            this.isTransitioning = false;
                            return [2 /*return*/, scene];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        // 发出场景切换开始事件
                        this.emit('sceneTransitionStart', fromScene, scene);
                        // 执行场景过渡
                        return [4 /*yield*/, this.executeTransition(fromScene, scene, transitionOptions)];
                    case 2:
                        // 执行场景过渡
                        _a.sent();
                        // 设置活跃场景
                        this.world.setActiveScene(scene);
                        // 发出场景切换完成事件
                        this.emit('sceneTransitionComplete', scene, fromScene);
                        this.isTransitioning = false;
                        return [2 /*return*/, scene];
                    case 3:
                        error_3 = _a.sent();
                        this.isTransitioning = false;
                        throw error_3;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 执行场景过渡
     * @param fromScene 源场景
     * @param toScene 目标场景
     * @param options 过渡选项
     * @returns Promise
     */
    SceneManager.prototype.executeTransition = function (fromScene, toScene, options) {
        var _this = this;
        return new Promise(function (resolve) {
            var duration = options.duration || 500;
            // 根据过渡类型执行不同的过渡效果
            switch (options.type) {
                case SceneTransitionType.NONE:
                    // 无过渡，直接完成
                    resolve();
                    break;
                case SceneTransitionType.FADE:
                    // 淡入淡出过渡
                    _this.executeFadeTransition(duration, resolve);
                    break;
                case SceneTransitionType.CROSS_FADE:
                    // 交叉淡入淡出过渡
                    _this.executeCrossFadeTransition(fromScene, toScene, duration, resolve);
                    break;
                case SceneTransitionType.SLIDE:
                    // 滑动过渡
                    _this.executeSlideTransition(options.direction || 'right', duration, resolve);
                    break;
                case SceneTransitionType.ZOOM:
                    // 缩放过渡
                    _this.executeZoomTransition(duration, resolve);
                    break;
                case SceneTransitionType.CUSTOM:
                    // 自定义过渡
                    if (options.customTransition) {
                        var completed_1 = false;
                        var onComplete_1 = function () {
                            if (!completed_1) {
                                completed_1 = true;
                                resolve();
                            }
                        };
                        // 执行自定义过渡
                        options.customTransition(fromScene, toScene, 0, onComplete_1);
                        // 设置超时，确保过渡最终会完成
                        setTimeout(function () {
                            onComplete_1();
                        }, duration + 100);
                    }
                    else {
                        // 如果没有提供自定义过渡函数，则直接完成
                        resolve();
                    }
                    break;
                default:
                    // 默认无过渡
                    resolve();
                    break;
            }
        });
    };
    /**
     * 执行淡入淡出过渡
     * @param duration 持续时间
     * @param onComplete 完成回调
     */
    SceneManager.prototype.executeFadeTransition = function (duration, onComplete) {
        // 创建过渡遮罩
        var mask = document.createElement('div');
        mask.style.position = 'fixed';
        mask.style.top = '0';
        mask.style.left = '0';
        mask.style.width = '100%';
        mask.style.height = '100%';
        mask.style.backgroundColor = 'black';
        mask.style.opacity = '0';
        mask.style.transition = "opacity ".concat(duration / 2, "ms ease-in-out");
        mask.style.zIndex = '9998';
        mask.style.pointerEvents = 'none';
        document.body.appendChild(mask);
        // 淡入
        setTimeout(function () {
            mask.style.opacity = '1';
            // 淡出
            setTimeout(function () {
                mask.style.opacity = '0';
                // 移除遮罩并完成过渡
                setTimeout(function () {
                    document.body.removeChild(mask);
                    onComplete();
                }, duration / 2);
            }, duration / 2);
        }, 0);
    };
    /**
     * 执行交叉淡入淡出过渡
     * @param fromScene 源场景
     * @param toScene 目标场景
     * @param duration 持续时间
     * @param onComplete 完成回调
     */
    SceneManager.prototype.executeCrossFadeTransition = function (fromScene, toScene, duration, onComplete) {
        // 由于我们不能直接控制Three.js的渲染，这里只是模拟一个交叉淡入淡出效果
        // 在实际应用中，应该使用后处理效果或者渲染到纹理来实现
        // 创建过渡遮罩
        var mask = document.createElement('div');
        mask.style.position = 'fixed';
        mask.style.top = '0';
        mask.style.left = '0';
        mask.style.width = '100%';
        mask.style.height = '100%';
        mask.style.backgroundColor = 'black';
        mask.style.opacity = '0';
        mask.style.transition = "opacity ".concat(duration, "ms ease-in-out");
        mask.style.zIndex = '9998';
        mask.style.pointerEvents = 'none';
        document.body.appendChild(mask);
        // 设置活跃场景
        this.world.setActiveScene(toScene);
        // 淡入淡出
        setTimeout(function () {
            mask.style.opacity = '0.5';
            setTimeout(function () {
                mask.style.opacity = '0';
                // 移除遮罩并完成过渡
                setTimeout(function () {
                    document.body.removeChild(mask);
                    onComplete();
                }, duration / 2);
            }, duration / 2);
        }, 0);
    };
    /**
     * 执行滑动过渡
     * @param direction 方向
     * @param duration 持续时间
     * @param onComplete 完成回调
     */
    SceneManager.prototype.executeSlideTransition = function (direction, duration, onComplete) {
        // 创建过渡遮罩
        var mask = document.createElement('div');
        mask.style.position = 'fixed';
        mask.style.top = '0';
        mask.style.left = '0';
        mask.style.width = '100%';
        mask.style.height = '100%';
        mask.style.backgroundColor = 'black';
        mask.style.transition = "transform ".concat(duration, "ms ease-in-out");
        mask.style.zIndex = '9998';
        mask.style.pointerEvents = 'none';
        // 设置初始位置
        switch (direction) {
            case 'left':
                mask.style.transform = 'translateX(100%)';
                break;
            case 'right':
                mask.style.transform = 'translateX(-100%)';
                break;
            case 'up':
                mask.style.transform = 'translateY(100%)';
                break;
            case 'down':
                mask.style.transform = 'translateY(-100%)';
                break;
        }
        document.body.appendChild(mask);
        // 滑入
        setTimeout(function () {
            mask.style.transform = 'translate(0, 0)';
            // 滑出
            setTimeout(function () {
                switch (direction) {
                    case 'left':
                        mask.style.transform = 'translateX(-100%)';
                        break;
                    case 'right':
                        mask.style.transform = 'translateX(100%)';
                        break;
                    case 'up':
                        mask.style.transform = 'translateY(-100%)';
                        break;
                    case 'down':
                        mask.style.transform = 'translateY(100%)';
                        break;
                }
                // 移除遮罩并完成过渡
                setTimeout(function () {
                    document.body.removeChild(mask);
                    onComplete();
                }, duration);
            }, duration);
        }, 0);
    };
    /**
     * 执行缩放过渡
     * @param duration 持续时间
     * @param onComplete 完成回调
     */
    SceneManager.prototype.executeZoomTransition = function (duration, onComplete) {
        // 创建过渡遮罩
        var mask = document.createElement('div');
        mask.style.position = 'fixed';
        mask.style.top = '0';
        mask.style.left = '0';
        mask.style.width = '100%';
        mask.style.height = '100%';
        mask.style.backgroundColor = 'black';
        mask.style.opacity = '0';
        mask.style.transition = "all ".concat(duration, "ms ease-in-out");
        mask.style.zIndex = '9998';
        mask.style.pointerEvents = 'none';
        mask.style.transform = 'scale(0)';
        document.body.appendChild(mask);
        // 缩放淡入
        setTimeout(function () {
            mask.style.opacity = '1';
            mask.style.transform = 'scale(1)';
            // 缩放淡出
            setTimeout(function () {
                mask.style.opacity = '0';
                mask.style.transform = 'scale(2)';
                // 移除遮罩并完成过渡
                setTimeout(function () {
                    document.body.removeChild(mask);
                    onComplete();
                }, duration);
            }, duration);
        }, 0);
    };
    /**
     * 添加场景到缓存
     * @param scene 场景实例
     */
    SceneManager.prototype.addToSceneCache = function (scene) {
        if (!this.enableSceneCache || !scene.id)
            return;
        // 如果已经在缓存中，则更新访问时间
        if (this.sceneCache.has(scene.id)) {
            this.sceneAccessTimes.set(scene.id, Date.now());
            return;
        }
        // 如果缓存已满，则移除最久未访问的场景
        if (this.sceneCache.size >= this.maxSceneCacheCount) {
            this.removeOldestSceneFromCache();
        }
        // 添加到缓存
        this.sceneCache.set(scene.id, scene);
        this.sceneAccessTimes.set(scene.id, Date.now());
    };
    /**
     * 从缓存中移除最久未访问的场景
     */
    SceneManager.prototype.removeOldestSceneFromCache = function () {
        if (this.sceneCache.size === 0)
            return;
        var oldestSceneId = null;
        var oldestAccessTime = Infinity;
        // 查找最久未访问的场景
        for (var _i = 0, _a = this.sceneAccessTimes.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], sceneId = _b[0], accessTime = _b[1];
            // 跳过当前场景和上一个场景
            if (this.currentScene && this.currentScene.id === sceneId)
                continue;
            if (this.previousScene && this.previousScene.id === sceneId)
                continue;
            if (accessTime < oldestAccessTime) {
                oldestAccessTime = accessTime;
                oldestSceneId = sceneId;
            }
        }
        // 如果找到最久未访问的场景，则从缓存中移除
        if (oldestSceneId) {
            this.sceneCache.delete(oldestSceneId);
            this.sceneAccessTimes.delete(oldestSceneId);
        }
    };
    /**
     * 从缓存中移除场景
     * @param sceneId 场景ID
     * @returns 是否成功移除
     */
    SceneManager.prototype.removeSceneFromCache = function (sceneId) {
        if (!this.sceneCache.has(sceneId))
            return false;
        // 从缓存中移除
        this.sceneCache.delete(sceneId);
        this.sceneAccessTimes.delete(sceneId);
        return true;
    };
    /**
     * 清空场景缓存
     */
    SceneManager.prototype.clearSceneCache = function () {
        this.sceneCache.clear();
        this.sceneAccessTimes.clear();
    };
    /**
     * 获取当前场景
     * @returns 当前场景
     */
    SceneManager.prototype.getCurrentScene = function () {
        return this.currentScene;
    };
    /**
     * 获取上一个场景
     * @returns 上一个场景
     */
    SceneManager.prototype.getPreviousScene = function () {
        return this.previousScene;
    };
    /**
     * 是否正在加载场景
     * @returns 是否正在加载
     */
    SceneManager.prototype.isSceneLoading = function () {
        return this.isLoading;
    };
    /**
     * 是否正在切换场景
     * @returns 是否正在切换
     */
    SceneManager.prototype.isSceneTransitioning = function () {
        return this.isTransitioning;
    };
    /**
     * 销毁场景管理器
     */
    SceneManager.prototype.dispose = function () {
        // 清空场景缓存
        this.clearSceneCache();
        // 销毁场景预加载器
        if (this.scenePreloader) {
            this.scenePreloader.dispose();
            this.scenePreloader = null;
        }
        // 移除加载界面
        if (this.loadingScreen && this.loadingScreen.parentNode) {
            this.loadingScreen.parentNode.removeChild(this.loadingScreen);
        }
        // 移除所有事件监听器
        this.removeAllListeners();
        this.initialized = false;
    };
    return SceneManager;
}(EventEmitter_1.EventEmitter));
exports.SceneManager = SceneManager;

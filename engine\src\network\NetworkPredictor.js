"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkPredictor = exports.PredictionAlgorithm = void 0;
/**
 * 网络预测器
 * 用于减少网络延迟影响，提供位置、旋转和其他属性的预测功能
 */
var THREE = require("three");
/**
 * 预测算法类型
 */
var PredictionAlgorithm;
(function (PredictionAlgorithm) {
    /** 线性预测 */
    PredictionAlgorithm["LINEAR"] = "linear";
    /** 二阶预测 */
    PredictionAlgorithm["QUADRATIC"] = "quadratic";
    /** 卡尔曼滤波 */
    PredictionAlgorithm["KALMAN"] = "kalman";
    /** 自适应预测 */
    PredictionAlgorithm["ADAPTIVE"] = "adaptive";
})(PredictionAlgorithm || (exports.PredictionAlgorithm = PredictionAlgorithm = {}));
/**
 * 网络预测器类
 */
var NetworkPredictor = /** @class */ (function () {
    /**
     * 创建网络预测器
     * @param config 预测配置
     */
    function NetworkPredictor(config) {
        if (config === void 0) { config = {}; }
        /** 历史状态 */
        this.history = [];
        /** 最大历史记录数 */
        this.MAX_HISTORY_SIZE = 10;
        // 默认配置
        this.config = __assign({ algorithm: PredictionAlgorithm.ADAPTIVE, maxPredictionTime: 500, useSmoothing: true, smoothingFactor: 0.3, useAdaptivePrediction: true, useJitterBuffer: true, jitterBufferSize: 100 }, config);
        // 初始化卡尔曼滤波器
        this.initKalmanFilter();
    }
    /**
     * 初始化卡尔曼滤波器
     */
    NetworkPredictor.prototype.initKalmanFilter = function () {
        // 位置卡尔曼滤波器 (x, y, z, vx, vy, vz, ax, ay, az)
        this.kalmanPosition = {
            x: [0, 0, 0, 0, 0, 0, 0, 0, 0],
            P: this.createIdentityMatrix(9, 100),
            Q: this.createIdentityMatrix(9, 0.01),
            R: this.createIdentityMatrix(3, 0.1),
            F: this.createStateTransitionMatrix(),
            H: this.createMeasurementMatrix(),
            initialized: false,
        };
        // 旋转卡尔曼滤波器 (qx, qy, qz, qw, wx, wy, wz)
        this.kalmanRotation = {
            x: [0, 0, 0, 1, 0, 0, 0],
            P: this.createIdentityMatrix(7, 100),
            Q: this.createIdentityMatrix(7, 0.01),
            R: this.createIdentityMatrix(4, 0.1),
            F: this.createRotationStateTransitionMatrix(),
            H: this.createRotationMeasurementMatrix(),
            initialized: false,
        };
    };
    /**
     * 创建单位矩阵
     * @param size 矩阵大小
     * @param value 对角线值
     * @returns 单位矩阵
     */
    NetworkPredictor.prototype.createIdentityMatrix = function (size, value) {
        if (value === void 0) { value = 1; }
        var matrix = [];
        for (var i = 0; i < size; i++) {
            matrix[i] = [];
            for (var j = 0; j < size; j++) {
                matrix[i][j] = i === j ? value : 0;
            }
        }
        return matrix;
    };
    /**
     * 创建状态转移矩阵
     * @returns 状态转移矩阵
     */
    NetworkPredictor.prototype.createStateTransitionMatrix = function () {
        // 状态: [x, y, z, vx, vy, vz, ax, ay, az]
        var dt = 1 / 60; // 假设60fps
        var F = this.createIdentityMatrix(9);
        // 位置更新: x += vx * dt + 0.5 * ax * dt^2
        F[0][3] = dt;
        F[0][6] = 0.5 * dt * dt;
        F[1][4] = dt;
        F[1][7] = 0.5 * dt * dt;
        F[2][5] = dt;
        F[2][8] = 0.5 * dt * dt;
        // 速度更新: vx += ax * dt
        F[3][6] = dt;
        F[4][7] = dt;
        F[5][8] = dt;
        return F;
    };
    /**
     * 创建测量矩阵
     * @returns 测量矩阵
     */
    NetworkPredictor.prototype.createMeasurementMatrix = function () {
        // 测量: [x, y, z]
        var H = [];
        for (var i = 0; i < 3; i++) {
            H[i] = [];
            for (var j = 0; j < 9; j++) {
                H[i][j] = i === j ? 1 : 0;
            }
        }
        return H;
    };
    /**
     * 创建旋转状态转移矩阵
     * @returns 旋转状态转移矩阵
     */
    NetworkPredictor.prototype.createRotationStateTransitionMatrix = function () {
        // 状态: [qx, qy, qz, qw, wx, wy, wz]
        return this.createIdentityMatrix(7);
        // 注意: 四元数的更新需要特殊处理，不能简单用线性模型
    };
    /**
     * 创建旋转测量矩阵
     * @returns 旋转测量矩阵
     */
    NetworkPredictor.prototype.createRotationMeasurementMatrix = function () {
        // 测量: [qx, qy, qz, qw]
        var H = [];
        for (var i = 0; i < 4; i++) {
            H[i] = [];
            for (var j = 0; j < 7; j++) {
                H[i][j] = i === j ? 1 : 0;
            }
        }
        return H;
    };
    /**
     * 矩阵乘法
     * @param A 矩阵A
     * @param b 向量b
     * @returns 结果向量
     */
    NetworkPredictor.prototype.matrixMultiply = function (A, b) {
        var result = [];
        for (var i = 0; i < A.length; i++) {
            var sum = 0;
            for (var j = 0; j < b.length; j++) {
                sum += A[i][j] * b[j];
            }
            result[i] = sum;
        }
        return result;
    };
    /**
     * 计算速度
     * @param position 当前位置
     * @param timestamp 时间戳
     * @returns 计算的速度
     */
    NetworkPredictor.prototype.calculateVelocity = function (position, timestamp) {
        // 如果没有历史记录，返回零速度
        if (this.history.length === 0) {
            return new THREE.Vector3(0, 0, 0);
        }
        // 获取最新状态
        var latestState = this.history[0];
        // 计算时间差（秒）
        var dt = (timestamp - latestState.timestamp) / 1000;
        // 如果时间差太小，返回上一次的速度
        if (dt < 0.001) {
            return latestState.velocity.clone();
        }
        // 计算位置差
        var posDiff = new THREE.Vector3().subVectors(position, latestState.position);
        // 计算速度
        return posDiff.divideScalar(dt);
    };
    /**
     * 计算角速度
     * @param rotation 当前旋转
     * @param timestamp 时间戳
     * @returns 计算的角速度
     */
    NetworkPredictor.prototype.calculateAngularVelocity = function (rotation, timestamp) {
        // 如果没有历史记录，返回零角速度
        if (this.history.length === 0) {
            return new THREE.Vector3(0, 0, 0);
        }
        // 获取最新状态
        var latestState = this.history[0];
        // 计算时间差（秒）
        var dt = (timestamp - latestState.timestamp) / 1000;
        // 如果时间差太小，返回上一次的角速度
        if (dt < 0.001) {
            return latestState.angularVelocity.clone();
        }
        // 计算四元数差
        var q1 = latestState.rotation;
        var q2 = rotation;
        var qDiff = new THREE.Quaternion().copy(q2).multiply(q1.clone().invert());
        // 转换为角速度
        var angle = 2 * Math.acos(qDiff.w);
        var s = Math.sqrt(1 - qDiff.w * qDiff.w);
        if (s < 0.001) {
            return new THREE.Vector3(0, 0, 0);
        }
        var x = qDiff.x / s;
        var y = qDiff.y / s;
        var z = qDiff.z / s;
        return new THREE.Vector3(x, y, z).multiplyScalar(angle / dt);
    };
    /**
     * 计算加速度
     * @param velocity 当前速度
     * @param timestamp 时间戳
     * @returns 计算的加速度
     */
    NetworkPredictor.prototype.calculateAcceleration = function (velocity, timestamp) {
        // 如果没有历史记录，返回零加速度
        if (this.history.length === 0) {
            return new THREE.Vector3(0, 0, 0);
        }
        // 获取最新状态
        var latestState = this.history[0];
        // 计算时间差（秒）
        var dt = (timestamp - latestState.timestamp) / 1000;
        // 如果时间差太小，返回上一次的加速度
        if (dt < 0.001) {
            return latestState.acceleration.clone();
        }
        // 计算速度差
        var velDiff = new THREE.Vector3().subVectors(velocity, latestState.velocity);
        // 计算加速度
        return velDiff.divideScalar(dt);
    };
    /**
     * 计算角加速度
     * @param angularVelocity 当前角速度
     * @param timestamp 时间戳
     * @returns 计算的角加速度
     */
    NetworkPredictor.prototype.calculateAngularAcceleration = function (angularVelocity, timestamp) {
        // 如果没有历史记录，返回零角加速度
        if (this.history.length === 0) {
            return new THREE.Vector3(0, 0, 0);
        }
        // 获取最新状态
        var latestState = this.history[0];
        // 计算时间差（秒）
        var dt = (timestamp - latestState.timestamp) / 1000;
        // 如果时间差太小，返回上一次的角加速度
        if (dt < 0.001) {
            return latestState.angularAcceleration.clone();
        }
        // 计算角速度差
        var angVelDiff = new THREE.Vector3().subVectors(angularVelocity, latestState.angularVelocity);
        // 计算角加速度
        return angVelDiff.divideScalar(dt);
    };
    /**
     * 更新卡尔曼滤波器
     * @param state 预测状态
     */
    NetworkPredictor.prototype.updateKalmanFilter = function (state) {
        // 更新位置卡尔曼滤波器
        this.updatePositionKalmanFilter(state);
        // 更新旋转卡尔曼滤波器
        this.updateRotationKalmanFilter(state);
    };
    /**
     * 更新位置卡尔曼滤波器
     * @param state 预测状态
     */
    NetworkPredictor.prototype.updatePositionKalmanFilter = function (state) {
        var position = state.position, velocity = state.velocity, acceleration = state.acceleration;
        // 如果未初始化，则初始化状态
        if (!this.kalmanPosition.initialized) {
            this.kalmanPosition.x = [
                position.x, position.y, position.z,
                velocity.x, velocity.y, velocity.z,
                acceleration.x, acceleration.y, acceleration.z
            ];
            this.kalmanPosition.initialized = true;
            return;
        }
        // 预测步骤
        var x = this.matrixMultiply(this.kalmanPosition.F, this.kalmanPosition.x);
        // 更新步骤
        var z = [position.x, position.y, position.z];
        var y = [
            z[0] - x[0],
            z[1] - x[1],
            z[2] - x[2]
        ];
        // 简化的卡尔曼增益计算
        var K = [0.5, 0.5, 0.5];
        // 更新状态
        this.kalmanPosition.x = [
            x[0] + K[0] * y[0],
            x[1] + K[1] * y[1],
            x[2] + K[2] * y[2],
            x[3], x[4], x[5], x[6], x[7], x[8]
        ];
    };
    /**
     * 更新旋转卡尔曼滤波器
     * @param state 预测状态
     */
    NetworkPredictor.prototype.updateRotationKalmanFilter = function (state) {
        var rotation = state.rotation, angularVelocity = state.angularVelocity;
        // 如果未初始化，则初始化状态
        if (!this.kalmanRotation.initialized) {
            this.kalmanRotation.x = [
                rotation.x, rotation.y, rotation.z, rotation.w,
                angularVelocity.x, angularVelocity.y, angularVelocity.z
            ];
            this.kalmanRotation.initialized = true;
            return;
        }
        // 预测步骤 - 四元数需要特殊处理
        // 这里使用简化的方法
        var x = __spreadArray([], this.kalmanRotation.x, true);
        // 更新步骤
        var z = [rotation.x, rotation.y, rotation.z, rotation.w];
        var y = [
            z[0] - x[0],
            z[1] - x[1],
            z[2] - x[2],
            z[3] - x[3]
        ];
        // 简化的卡尔曼增益计算
        var K = [0.3, 0.3, 0.3, 0.3];
        // 更新状态
        this.kalmanRotation.x = [
            x[0] + K[0] * y[0],
            x[1] + K[1] * y[1],
            x[2] + K[2] * y[2],
            x[3] + K[3] * y[3],
            x[4], x[5], x[6]
        ];
        // 归一化四元数
        var qNorm = Math.sqrt(this.kalmanRotation.x[0] * this.kalmanRotation.x[0] +
            this.kalmanRotation.x[1] * this.kalmanRotation.x[1] +
            this.kalmanRotation.x[2] * this.kalmanRotation.x[2] +
            this.kalmanRotation.x[3] * this.kalmanRotation.x[3]);
        this.kalmanRotation.x[0] /= qNorm;
        this.kalmanRotation.x[1] /= qNorm;
        this.kalmanRotation.x[2] /= qNorm;
        this.kalmanRotation.x[3] /= qNorm;
    };
    /**
     * 线性预测位置
     * @param state 预测状态
     * @param predictionTime 预测时间（毫秒）
     * @returns 预测位置
     */
    NetworkPredictor.prototype.linearPredictPosition = function (state, predictionTime) {
        var seconds = predictionTime / 1000;
        var result = state.position.clone();
        // 位置 = 当前位置 + 速度 * 时间
        result.add(state.velocity.clone().multiplyScalar(seconds));
        return result;
    };
    /**
     * 二阶预测位置
     * @param state 预测状态
     * @param predictionTime 预测时间（毫秒）
     * @returns 预测位置
     */
    NetworkPredictor.prototype.quadraticPredictPosition = function (state, predictionTime) {
        var seconds = predictionTime / 1000;
        var result = state.position.clone();
        // 位置 = 当前位置 + 速度 * 时间 + 0.5 * 加速度 * 时间^2
        result.add(state.velocity.clone().multiplyScalar(seconds));
        result.add(state.acceleration.clone().multiplyScalar(0.5 * seconds * seconds));
        return result;
    };
    /**
     * 卡尔曼预测位置
     * @param predictionTime 预测时间（毫秒）
     * @returns 预测位置
     */
    NetworkPredictor.prototype.kalmanPredictPosition = function (predictionTime) {
        // 如果卡尔曼滤波器未初始化，使用线性预测
        if (!this.kalmanPosition.initialized) {
            return this.linearPredictPosition(this.history[0], predictionTime);
        }
        // 获取当前状态
        var x = this.kalmanPosition.x;
        var seconds = predictionTime / 1000;
        // 预测位置 = 当前位置 + 速度 * 时间 + 0.5 * 加速度 * 时间^2
        var predictedPosition = new THREE.Vector3(x[0] + x[3] * seconds + 0.5 * x[6] * seconds * seconds, x[1] + x[4] * seconds + 0.5 * x[7] * seconds * seconds, x[2] + x[5] * seconds + 0.5 * x[8] * seconds * seconds);
        return predictedPosition;
    };
    /**
     * 自适应预测位置
     * @param predictionTime 预测时间（毫秒）
     * @returns 预测位置
     */
    NetworkPredictor.prototype.adaptivePredictPosition = function (predictionTime) {
        // 如果历史记录不足，使用线性预测
        if (this.history.length < 3) {
            return this.linearPredictPosition(this.history[0], predictionTime);
        }
        // 计算速度变化率
        var latestState = this.history[0];
        var prevState = this.history[1];
        var velocityChange = new THREE.Vector3().subVectors(latestState.velocity, prevState.velocity);
        var velocityChangeMagnitude = velocityChange.length();
        // 根据速度变化选择预测方法
        if (velocityChangeMagnitude > 1.0) {
            // 速度变化大，使用二阶预测
            return this.quadraticPredictPosition(latestState, predictionTime);
        }
        else {
            // 速度变化小，使用线性预测
            return this.linearPredictPosition(latestState, predictionTime);
        }
    };
    /**
     * 线性预测旋转
     * @param state 预测状态
     * @param predictionTime 预测时间（毫秒）
     * @returns 预测旋转
     */
    NetworkPredictor.prototype.linearPredictRotation = function (state, predictionTime) {
        var seconds = predictionTime / 1000;
        var result = state.rotation.clone();
        // 创建角速度四元数
        var angVelMag = state.angularVelocity.length();
        if (angVelMag < 0.0001) {
            return result;
        }
        var axis = state.angularVelocity.clone().divideScalar(angVelMag);
        var angle = angVelMag * seconds;
        var rotationDelta = new THREE.Quaternion().setFromAxisAngle(axis, angle);
        // 应用旋转
        result.multiply(rotationDelta);
        return result;
    };
    /**
     * 二阶预测旋转
     * @param state 预测状态
     * @param predictionTime 预测时间（毫秒）
     * @returns 预测旋转
     */
    NetworkPredictor.prototype.quadraticPredictRotation = function (state, predictionTime) {
        var seconds = predictionTime / 1000;
        // 计算角速度变化
        var angVel = state.angularVelocity.clone();
        angVel.add(state.angularAcceleration.clone().multiplyScalar(seconds * 0.5));
        // 创建角速度四元数
        var angVelMag = angVel.length();
        if (angVelMag < 0.0001) {
            return state.rotation.clone();
        }
        var axis = angVel.clone().divideScalar(angVelMag);
        var angle = angVelMag * seconds;
        var rotationDelta = new THREE.Quaternion().setFromAxisAngle(axis, angle);
        // 应用旋转
        var result = state.rotation.clone();
        result.multiply(rotationDelta);
        return result;
    };
    /**
     * 卡尔曼预测旋转
     * @param predictionTime 预测时间（毫秒）
     * @returns 预测旋转
     */
    NetworkPredictor.prototype.kalmanPredictRotation = function (predictionTime) {
        // 如果卡尔曼滤波器未初始化，使用线性预测
        if (!this.kalmanRotation.initialized) {
            return this.linearPredictRotation(this.history[0], predictionTime);
        }
        // 获取当前状态
        var x = this.kalmanRotation.x;
        // 创建四元数
        var quat = new THREE.Quaternion(x[0], x[1], x[2], x[3]);
        // 创建角速度向量
        var angVel = new THREE.Vector3(x[4], x[5], x[6]);
        // 预测旋转
        var seconds = predictionTime / 1000;
        var angVelMag = angVel.length();
        if (angVelMag < 0.0001) {
            return quat;
        }
        var axis = angVel.clone().divideScalar(angVelMag);
        var angle = angVelMag * seconds;
        var rotationDelta = new THREE.Quaternion().setFromAxisAngle(axis, angle);
        // 应用旋转
        quat.multiply(rotationDelta);
        return quat;
    };
    /**
     * 自适应预测旋转
     * @param predictionTime 预测时间（毫秒）
     * @returns 预测旋转
     */
    NetworkPredictor.prototype.adaptivePredictRotation = function (predictionTime) {
        // 如果历史记录不足，使用线性预测
        if (this.history.length < 3) {
            return this.linearPredictRotation(this.history[0], predictionTime);
        }
        // 计算角速度变化率
        var latestState = this.history[0];
        var prevState = this.history[1];
        var angVelChange = new THREE.Vector3().subVectors(latestState.angularVelocity, prevState.angularVelocity);
        var angVelChangeMagnitude = angVelChange.length();
        // 根据角速度变化选择预测方法
        if (angVelChangeMagnitude > 0.5) {
            // 角速度变化大，使用二阶预测
            return this.quadraticPredictRotation(latestState, predictionTime);
        }
        else {
            // 角速度变化小，使用线性预测
            return this.linearPredictRotation(latestState, predictionTime);
        }
    };
    /**
     * 更新预测状态
     * @param position 当前位置
     * @param rotation 当前旋转
     * @param velocity 当前速度
     * @param angularVelocity 当前角速度
     * @param timestamp 时间戳
     */
    NetworkPredictor.prototype.update = function (position, rotation, velocity, angularVelocity, timestamp) {
        if (timestamp === void 0) { timestamp = Date.now(); }
        // 计算速度和加速度（如果未提供）
        var calculatedVelocity = velocity || this.calculateVelocity(position, timestamp);
        var calculatedAngularVelocity = angularVelocity || this.calculateAngularVelocity(rotation, timestamp);
        var acceleration = this.calculateAcceleration(calculatedVelocity, timestamp);
        var angularAcceleration = this.calculateAngularAcceleration(calculatedAngularVelocity, timestamp);
        // 创建新的预测状态
        var state = {
            position: position.clone(),
            rotation: rotation.clone(),
            velocity: calculatedVelocity.clone(),
            angularVelocity: calculatedAngularVelocity.clone(),
            acceleration: acceleration.clone(),
            angularAcceleration: angularAcceleration.clone(),
            timestamp: timestamp,
        };
        // 添加到历史记录
        this.history.unshift(state);
        // 限制历史记录大小
        if (this.history.length > this.MAX_HISTORY_SIZE) {
            this.history.pop();
        }
        // 更新卡尔曼滤波器
        if (this.config.algorithm === PredictionAlgorithm.KALMAN) {
            this.updateKalmanFilter(state);
        }
    };
    /**
     * 预测位置
     * @param latency 延迟（毫秒）
     * @returns 预测位置
     */
    NetworkPredictor.prototype.predictPosition = function (latency) {
        // 限制最大预测时间
        var predictionTime = Math.min(latency, this.config.maxPredictionTime);
        // 如果没有历史记录，返回最后已知位置
        if (this.history.length === 0) {
            return new THREE.Vector3();
        }
        // 获取最新状态
        var latestState = this.history[0];
        // 根据算法选择预测方法
        switch (this.config.algorithm) {
            case PredictionAlgorithm.LINEAR:
                return this.linearPredictPosition(latestState, predictionTime);
            case PredictionAlgorithm.QUADRATIC:
                return this.quadraticPredictPosition(latestState, predictionTime);
            case PredictionAlgorithm.KALMAN:
                return this.kalmanPredictPosition(predictionTime);
            case PredictionAlgorithm.ADAPTIVE:
                return this.adaptivePredictPosition(predictionTime);
            default:
                return this.linearPredictPosition(latestState, predictionTime);
        }
    };
    /**
     * 预测旋转
     * @param latency 延迟（毫秒）
     * @returns 预测旋转
     */
    NetworkPredictor.prototype.predictRotation = function (latency) {
        // 限制最大预测时间
        var predictionTime = Math.min(latency, this.config.maxPredictionTime);
        // 如果没有历史记录，返回最后已知旋转
        if (this.history.length === 0) {
            return new THREE.Quaternion();
        }
        // 获取最新状态
        var latestState = this.history[0];
        // 根据算法选择预测方法
        switch (this.config.algorithm) {
            case PredictionAlgorithm.LINEAR:
                return this.linearPredictRotation(latestState, predictionTime);
            case PredictionAlgorithm.QUADRATIC:
                return this.quadraticPredictRotation(latestState, predictionTime);
            case PredictionAlgorithm.KALMAN:
                return this.kalmanPredictRotation(predictionTime);
            case PredictionAlgorithm.ADAPTIVE:
                return this.adaptivePredictRotation(predictionTime);
            default:
                return this.linearPredictRotation(latestState, predictionTime);
        }
    };
    return NetworkPredictor;
}());
exports.NetworkPredictor = NetworkPredictor;

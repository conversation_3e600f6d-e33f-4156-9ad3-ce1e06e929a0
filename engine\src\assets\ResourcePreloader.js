"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResourcePreloader = void 0;
/**
 * 资源预加载器
 * 用于预加载资源，提高游戏性能
 */
var EventEmitter_1 = require("../utils/EventEmitter");
/**
 * 资源预加载器
 */
var ResourcePreloader = /** @class */ (function (_super) {
    __extends(ResourcePreloader, _super);
    /**
     * 创建资源预加载器实例
     * @param options 资源预加载器选项
     */
    function ResourcePreloader(options) {
        var _this = _super.call(this) || this;
        /** 预加载组映射 */
        _this.groups = new Map();
        /** 预加载进度映射 */
        _this.progress = new Map();
        /** 当前加载组 */
        _this.currentGroup = null;
        /** 加载队列 */
        _this.loadQueue = [];
        /** 当前并发加载数 */
        _this.currentConcurrentLoads = 0;
        /** 是否正在加载 */
        _this.loading = false;
        /** 是否已暂停 */
        _this.paused = false;
        /** 是否已初始化 */
        _this.initialized = false;
        _this.assetManager = options.assetManager;
        _this.autoRegisterAssets = options.autoRegisterAssets !== undefined ? options.autoRegisterAssets : true;
        _this.autoLoadDependencies = options.autoLoadDependencies !== undefined ? options.autoLoadDependencies : true;
        _this.maxConcurrentLoads = options.maxConcurrentLoads || 6;
        _this.retryCount = options.retryCount || 3;
        _this.retryDelay = options.retryDelay || 1000;
        return _this;
    }
    /**
     * 初始化资源预加载器
     */
    ResourcePreloader.prototype.initialize = function () {
        if (this.initialized) {
            return;
        }
        this.initialized = true;
        this.emit('initialized');
    };
    /**
     * 添加预加载组
     * @param group 预加载组信息
     * @returns 是否成功添加
     */
    ResourcePreloader.prototype.addGroup = function (group) {
        // 检查组名是否已存在
        if (this.groups.has(group.name)) {
            return false;
        }
        // 添加组
        this.groups.set(group.name, __assign(__assign({}, group), { priority: group.priority || 0, dependencies: group.dependencies || [], resources: __spreadArray([], group.resources, true) }));
        // 初始化进度信息
        this.progress.set(group.name, {
            group: group.name,
            loaded: 0,
            total: group.resources.length,
            progress: 0,
            loadedResources: [],
            failedResources: [],
        });
        // 如果启用自动注册资源，则注册组中的资源
        if (this.autoRegisterAssets) {
            this.registerGroupAssets(group.name);
        }
        // 发出组添加事件
        this.emit('groupAdded', group);
        return true;
    };
    /**
     * 移除预加载组
     * @param name 组名
     * @returns 是否成功移除
     */
    ResourcePreloader.prototype.removeGroup = function (name) {
        // 检查组名是否存在
        if (!this.groups.has(name)) {
            return false;
        }
        // 如果正在加载此组，则无法移除
        if (this.currentGroup === name) {
            return false;
        }
        // 获取组信息
        var group = this.groups.get(name);
        // 移除组
        this.groups.delete(name);
        // 移除进度信息
        this.progress.delete(name);
        // 发出组移除事件
        this.emit('groupRemoved', group);
        return true;
    };
    /**
     * 注册组中的资源
     * @param name 组名
     * @returns 是否成功注册
     */
    ResourcePreloader.prototype.registerGroupAssets = function (name) {
        // 检查组名是否存在
        if (!this.groups.has(name)) {
            return false;
        }
        // 获取组信息
        var group = this.groups.get(name);
        // 注册组中的资源
        for (var _i = 0, _a = group.resources; _i < _a.length; _i++) {
            var resource = _a[_i];
            try {
                // 检查资源是否已注册
                if (this.assetManager.getAssetInfo(resource.id)) {
                    continue;
                }
                // 注册资源
                this.assetManager.registerAsset(resource.id, resource.id, resource.type, resource.url, resource.metadata);
            }
            catch (error) {
                console.error("\u6CE8\u518C\u8D44\u6E90 ".concat(resource.id, " \u5931\u8D25:"), error);
            }
        }
        return true;
    };
    /**
     * 加载预加载组
     * @param name 组名
     * @param onProgress 进度回调
     * @returns Promise，解析为加载结果
     */
    ResourcePreloader.prototype.loadGroup = function (name, onProgress) {
        return __awaiter(this, void 0, void 0, function () {
            var group, _i, _a, depName, progressInfo, error_1;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        // 检查组名是否存在
                        if (!this.groups.has(name)) {
                            throw new Error("\u627E\u4E0D\u5230\u9884\u52A0\u8F7D\u7EC4: ".concat(name));
                        }
                        // 如果正在加载，则返回
                        if (this.loading && !this.paused) {
                            throw new Error('已有预加载组正在加载');
                        }
                        group = this.groups.get(name);
                        // 重置进度信息
                        this.resetProgress(name);
                        // 设置当前加载组
                        this.currentGroup = name;
                        this.loading = true;
                        this.paused = false;
                        // 发出加载开始事件
                        this.emit('loadStart', { group: name });
                        _b.label = 1;
                    case 1:
                        _b.trys.push([1, 7, 8, 9]);
                        if (!(this.autoLoadDependencies && group.dependencies && group.dependencies.length > 0)) return [3 /*break*/, 5];
                        _i = 0, _a = group.dependencies;
                        _b.label = 2;
                    case 2:
                        if (!(_i < _a.length)) return [3 /*break*/, 5];
                        depName = _a[_i];
                        return [4 /*yield*/, this.loadGroup(depName)];
                    case 3:
                        _b.sent();
                        _b.label = 4;
                    case 4:
                        _i++;
                        return [3 /*break*/, 2];
                    case 5:
                        // 准备加载队列
                        this.prepareLoadQueue(name);
                        // 开始加载
                        return [4 /*yield*/, this.startLoading(onProgress)];
                    case 6:
                        // 开始加载
                        _b.sent();
                        progressInfo = this.progress.get(name);
                        // 发出加载完成事件
                        this.emit('loadComplete', progressInfo);
                        return [2 /*return*/, progressInfo];
                    case 7:
                        error_1 = _b.sent();
                        // 发出加载错误事件
                        this.emit('loadError', { group: name, error: error_1 });
                        throw error_1;
                    case 8:
                        // 重置状态
                        this.currentGroup = null;
                        this.loading = false;
                        this.paused = false;
                        this.loadQueue = [];
                        this.currentConcurrentLoads = 0;
                        return [7 /*endfinally*/];
                    case 9: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 重置进度信息
     * @param name 组名
     */
    ResourcePreloader.prototype.resetProgress = function (name) {
        // 检查组名是否存在
        if (!this.groups.has(name)) {
            return;
        }
        // 获取组信息
        var group = this.groups.get(name);
        // 重置进度信息
        this.progress.set(name, {
            group: name,
            loaded: 0,
            total: group.resources.length,
            progress: 0,
            loadedResources: [],
            failedResources: [],
        });
    };
    /**
     * 准备加载队列
     * @param name 组名
     */
    ResourcePreloader.prototype.prepareLoadQueue = function (name) {
        // 检查组名是否存在
        if (!this.groups.has(name)) {
            return;
        }
        // 获取组信息
        var group = this.groups.get(name);
        // 清空加载队列
        this.loadQueue = [];
        // 添加资源到加载队列
        for (var _i = 0, _a = group.resources; _i < _a.length; _i++) {
            var resource = _a[_i];
            // 检查资源是否已加载
            if (this.assetManager.getAsset(resource.id)) {
                // 更新进度信息
                this.updateProgress(name, resource.id, true);
                continue;
            }
            // 添加到加载队列
            this.loadQueue.push(resource);
        }
        // 按优先级排序
        this.loadQueue.sort(function (a, b) { return (b.priority || 0) - (a.priority || 0); });
    };
    /**
     * 开始加载
     * @param onProgress 进度回调
     * @returns Promise，解析为加载结果
     */
    ResourcePreloader.prototype.startLoading = function (onProgress) {
        return __awaiter(this, void 0, void 0, function () {
            var progressInfo;
            var _this = this;
            return __generator(this, function (_a) {
                // 如果没有当前加载组，则返回
                if (!this.currentGroup) {
                    return [2 /*return*/];
                }
                progressInfo = this.progress.get(this.currentGroup);
                // 如果已加载完成，则返回
                if (progressInfo.loaded === progressInfo.total) {
                    return [2 /*return*/];
                }
                // 创建加载Promise
                return [2 /*return*/, new Promise(function (resolve, _reject) {
                        // 加载完成回调
                        var onComplete = function () {
                            resolve();
                        };
                        // 注意：onError 回调在此处未使用，但保留为注释以便将来可能的扩展
                        // const onError = (error: Error) => {
                        //   reject(error);
                        // };
                        // 加载下一个资源
                        var loadNext = function () {
                            // 如果已暂停，则返回
                            if (_this.paused) {
                                return;
                            }
                            // 如果没有当前加载组，则返回
                            if (!_this.currentGroup) {
                                return;
                            }
                            // 获取进度信息
                            var progressInfo = _this.progress.get(_this.currentGroup);
                            // 如果已加载完成，则调用完成回调
                            if (progressInfo.loaded === progressInfo.total) {
                                onComplete();
                                return;
                            }
                            // 如果加载队列为空，则返回
                            if (_this.loadQueue.length === 0) {
                                return;
                            }
                            // 如果当前并发加载数达到最大值，则返回
                            if (_this.currentConcurrentLoads >= _this.maxConcurrentLoads) {
                                return;
                            }
                            // 增加并发加载计数
                            _this.currentConcurrentLoads++;
                            // 获取下一个资源
                            var resource = _this.loadQueue.shift();
                            // 加载资源
                            _this.loadResource(resource, 0)
                                .then(function () {
                                // 更新进度信息
                                _this.updateProgress(_this.currentGroup, resource.id, true);
                                // 调用进度回调
                                if (onProgress) {
                                    onProgress(_this.progress.get(_this.currentGroup));
                                }
                                // 减少并发加载计数
                                _this.currentConcurrentLoads--;
                                // 加载下一个资源
                                loadNext();
                            })
                                .catch(function (_error) {
                                // 更新进度信息
                                _this.updateProgress(_this.currentGroup, resource.id, false);
                                // 调用进度回调
                                if (onProgress) {
                                    onProgress(_this.progress.get(_this.currentGroup));
                                }
                                // 减少并发加载计数
                                _this.currentConcurrentLoads--;
                                // 如果所有资源都已处理，则调用完成回调
                                if (progressInfo.loaded + progressInfo.failedResources.length === progressInfo.total) {
                                    onComplete();
                                }
                                else {
                                    // 否则加载下一个资源
                                    loadNext();
                                }
                            });
                            // 继续加载下一个资源
                            loadNext();
                        };
                        // 开始加载
                        for (var i = 0; i < _this.maxConcurrentLoads; i++) {
                            loadNext();
                        }
                    })];
            });
        });
    };
    /**
     * 加载资源
     * @param resource 资源信息
     * @param retryCount 当前重试次数
     * @returns Promise
     */
    ResourcePreloader.prototype.loadResource = function (resource, retryCount) {
        return __awaiter(this, void 0, void 0, function () {
            var error_2;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 5]);
                        // 加载资源
                        return [4 /*yield*/, this.assetManager.loadAsset(resource.id)];
                    case 1:
                        // 加载资源
                        _a.sent();
                        return [3 /*break*/, 5];
                    case 2:
                        error_2 = _a.sent();
                        if (!(retryCount < this.retryCount)) return [3 /*break*/, 4];
                        // 等待重试延迟
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, _this.retryDelay); })];
                    case 3:
                        // 等待重试延迟
                        _a.sent();
                        // 重试加载
                        return [2 /*return*/, this.loadResource(resource, retryCount + 1)];
                    case 4: 
                    // 否则抛出错误
                    throw error_2;
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 更新进度信息
     * @param group 组名
     * @param resourceId 资源ID
     * @param success 是否成功
     */
    ResourcePreloader.prototype.updateProgress = function (group, resourceId, success) {
        // 检查组名是否存在
        if (!this.groups.has(group)) {
            return;
        }
        // 获取进度信息
        var progressInfo = this.progress.get(group);
        // 更新进度信息
        if (success) {
            // 如果资源已在已加载列表中，则返回
            if (progressInfo.loadedResources.includes(resourceId)) {
                return;
            }
            // 添加到已加载列表
            progressInfo.loadedResources.push(resourceId);
            progressInfo.loaded++;
        }
        else {
            // 如果资源已在失败列表中，则返回
            if (progressInfo.failedResources.includes(resourceId)) {
                return;
            }
            // 添加到失败列表
            progressInfo.failedResources.push(resourceId);
        }
        // 更新进度
        progressInfo.progress = progressInfo.loaded / progressInfo.total;
        // 发出进度更新事件
        this.emit('progress', progressInfo);
    };
    /**
     * 暂停加载
     */
    ResourcePreloader.prototype.pause = function () {
        if (!this.loading || this.paused) {
            return;
        }
        this.paused = true;
        // 发出暂停事件
        this.emit('pause', { group: this.currentGroup });
    };
    /**
     * 恢复加载
     */
    ResourcePreloader.prototype.resume = function () {
        if (!this.loading || !this.paused) {
            return;
        }
        this.paused = false;
        // 发出恢复事件
        this.emit('resume', { group: this.currentGroup });
        // 继续加载
        this.startLoading();
    };
    /**
     * 获取预加载组
     * @param name 组名
     * @returns 预加载组信息
     */
    ResourcePreloader.prototype.getGroup = function (name) {
        return this.groups.get(name) || null;
    };
    /**
     * 获取所有预加载组
     * @returns 预加载组信息数组
     */
    ResourcePreloader.prototype.getAllGroups = function () {
        return Array.from(this.groups.values());
    };
    /**
     * 获取预加载进度
     * @param name 组名
     * @returns 预加载进度信息
     */
    ResourcePreloader.prototype.getProgress = function (name) {
        return this.progress.get(name) || null;
    };
    /**
     * 获取当前加载组
     * @returns 当前加载组名
     */
    ResourcePreloader.prototype.getCurrentGroup = function () {
        return this.currentGroup;
    };
    /**
     * 是否正在加载
     * @returns 是否正在加载
     */
    ResourcePreloader.prototype.isLoading = function () {
        return this.loading;
    };
    /**
     * 是否已暂停
     * @returns 是否已暂停
     */
    ResourcePreloader.prototype.isPaused = function () {
        return this.paused;
    };
    /**
     * 清空所有预加载组
     */
    ResourcePreloader.prototype.clear = function () {
        // 如果正在加载，则先暂停
        if (this.loading && !this.paused) {
            this.pause();
        }
        // 清空预加载组
        this.groups.clear();
        // 清空进度信息
        this.progress.clear();
        // 重置状态
        this.currentGroup = null;
        this.loading = false;
        this.paused = false;
        this.loadQueue = [];
        this.currentConcurrentLoads = 0;
        // 发出清空事件
        this.emit('cleared');
    };
    /**
     * 销毁资源预加载器
     */
    ResourcePreloader.prototype.dispose = function () {
        // 清空所有预加载组
        this.clear();
        // 移除所有事件监听器
        this.removeAllListeners();
        this.initialized = false;
    };
    return ResourcePreloader;
}(EventEmitter_1.EventEmitter));
exports.ResourcePreloader = ResourcePreloader;

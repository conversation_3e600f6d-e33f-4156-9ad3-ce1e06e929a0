"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeviceCapabilities = exports.DevicePerformanceLevel = void 0;
/**
 * 设备性能级别枚举
 */
var DevicePerformanceLevel;
(function (DevicePerformanceLevel) {
    /** 低性能 */
    DevicePerformanceLevel[DevicePerformanceLevel["LOW"] = 0] = "LOW";
    /** 中等性能 */
    DevicePerformanceLevel[DevicePerformanceLevel["MEDIUM"] = 1] = "MEDIUM";
    /** 高性能 */
    DevicePerformanceLevel[DevicePerformanceLevel["HIGH"] = 2] = "HIGH";
})(DevicePerformanceLevel || (exports.DevicePerformanceLevel = DevicePerformanceLevel = {}));
/**
 * 设备能力检测类
 * 单例模式
 */
var DeviceCapabilities = /** @class */ (function () {
    /**
     * 创建设备能力检测
     * @param options 设备能力检测配置
     */
    function DeviceCapabilities(options) {
        if (options === void 0) { options = {}; }
        /** 电池电量 */
        this.batteryLevel = 100;
        /** 电池是否正在充电 */
        this.batteryCharging = false;
        /** 设备温度 */
        this.temperature = 25;
        /** 当前帧率 */
        this.currentFPS = 60;
        /** CPU使用率 */
        this.cpuUsage = 0;
        /** GPU使用率 */
        this.gpuUsage = 0;
        /** 内存使用率 */
        this.memoryUsage = 0;
        /** 渲染器 */
        this.renderer = null;
        this.forceLowPerformance = options.forceLowPerformance || false;
        this.forceHighPerformance = options.forceHighPerformance || false;
        this.enableBatteryMonitoring = options.enableBatteryMonitoring !== undefined ? options.enableBatteryMonitoring : true;
        this.enableTemperatureMonitoring = options.enableTemperatureMonitoring !== undefined ? options.enableTemperatureMonitoring : true;
        this.enableNetworkMonitoring = options.enableNetworkMonitoring !== undefined ? options.enableNetworkMonitoring : true;
        this.enablePerformanceMonitoring = options.enablePerformanceMonitoring !== undefined ? options.enablePerformanceMonitoring : true;
        this.enableAutoPerformanceAdjustment = options.enableAutoPerformanceAdjustment !== undefined ? options.enableAutoPerformanceAdjustment : true;
        this.lowBatteryThreshold = options.lowBatteryThreshold || 20;
        this.highTemperatureThreshold = options.highTemperatureThreshold || 40;
        this.targetFPS = options.targetFPS || 60;
        this.minAcceptableFPS = options.minAcceptableFPS || 30;
        // 检测是否是移动设备
        this.isMobile = this.detectMobileDevice();
        // 检测是否是VR/AR设备
        this.isXRDevice = this.detectXRDevice();
        // 初始化默认值
        this.supportsWebGL2 = false;
        this.supportsFloatTextures = false;
        this.supportsAnisotropy = false;
        this.supportsInstancing = false;
        this.supportsCompressedTextures = false;
        this.supportsHDR = false;
        this.supportsShadows = false;
        this.maxTextureSize = 0;
        this.maxAnisotropy = 0;
        // 设置初始性能级别
        this.performanceLevel = this.determinePerformanceLevel();
        // 初始化电池监控
        if (this.enableBatteryMonitoring) {
            this.initializeBatteryMonitoring();
        }
        // 初始化温度监控
        if (this.enableTemperatureMonitoring) {
            this.initializeTemperatureMonitoring();
        }
        // 初始化网络监控
        if (this.enableNetworkMonitoring) {
            this.initializeNetworkMonitoring();
        }
        // 初始化性能监控
        if (this.enablePerformanceMonitoring) {
            this.initializePerformanceMonitoring();
        }
    }
    /**
     * 获取单例实例
     * @returns DeviceCapabilities实例
     */
    DeviceCapabilities.getInstance = function (options) {
        if (options === void 0) { options = {}; }
        if (!DeviceCapabilities.instance) {
            DeviceCapabilities.instance = new DeviceCapabilities(options);
        }
        return DeviceCapabilities.instance;
    };
    /**
     * 初始化设备能力检测
     * @param renderer Three.js渲染器
     */
    DeviceCapabilities.prototype.initialize = function (renderer) {
        this.renderer = renderer;
        // 获取WebGL上下文
        var gl = renderer.getContext();
        // 检测WebGL2支持
        this.supportsWebGL2 = typeof WebGL2RenderingContext !== 'undefined' && gl instanceof WebGL2RenderingContext;
        // 获取渲染器能力
        var capabilities = renderer.capabilities;
        // 检测浮点纹理支持
        this.supportsFloatTextures = capabilities.isWebGL2 && capabilities.floatFragmentTextures;
        // 检测各向异性过滤支持
        this.supportsAnisotropy = capabilities.anisotropyLevel > 0;
        this.maxAnisotropy = capabilities.getMaxAnisotropy();
        // 检测实例化渲染支持
        this.supportsInstancing = capabilities.isWebGL2 || !!capabilities.instancing;
        // 检测压缩纹理支持
        this.supportsCompressedTextures = !!capabilities.s3tc || !!capabilities.etc1 || !!capabilities.etc2 || !!capabilities.astc;
        // 检测HDR支持
        this.supportsHDR = capabilities.isWebGL2 && capabilities.floatFragmentTextures;
        // 检测阴影支持
        this.supportsShadows = true; // 假设所有WebGL设备都支持基本阴影
        // 获取最大纹理大小
        this.maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);
        // 重新确定性能级别
        this.performanceLevel = this.determinePerformanceLevel();
    };
    /**
     * 获取设备性能级别
     * @returns 设备性能级别
     */
    DeviceCapabilities.prototype.getPerformanceLevel = function () {
        return this.performanceLevel;
    };
    /**
     * 是否是低性能设备
     * @returns 是否是低性能设备
     */
    DeviceCapabilities.prototype.isLowPerformanceDevice = function () {
        return this.performanceLevel === DevicePerformanceLevel.LOW || this.forceLowPerformance;
    };
    /**
     * 是否是高性能设备
     * @returns 是否是高性能设备
     */
    DeviceCapabilities.prototype.isHighPerformanceDevice = function () {
        return this.performanceLevel === DevicePerformanceLevel.HIGH || this.forceHighPerformance;
    };
    /**
     * 是否支持WebGL2
     * @returns 是否支持WebGL2
     */
    DeviceCapabilities.prototype.isWebGL2Supported = function () {
        return this.supportsWebGL2;
    };
    /**
     * 是否支持浮点纹理
     * @returns 是否支持浮点纹理
     */
    DeviceCapabilities.prototype.isFloatTexturesSupported = function () {
        return this.supportsFloatTextures;
    };
    /**
     * 是否支持各向异性过滤
     * @returns 是否支持各向异性过滤
     */
    DeviceCapabilities.prototype.isAnisotropySupported = function () {
        return this.supportsAnisotropy;
    };
    /**
     * 获取最大各向异性级别
     * @returns 最大各向异性级别
     */
    DeviceCapabilities.prototype.getMaxAnisotropy = function () {
        return this.maxAnisotropy;
    };
    /**
     * 是否支持实例化渲染
     * @returns 是否支持实例化渲染
     */
    DeviceCapabilities.prototype.isInstancingSupported = function () {
        return this.supportsInstancing;
    };
    /**
     * 是否支持压缩纹理
     * @returns 是否支持压缩纹理
     */
    DeviceCapabilities.prototype.isCompressedTexturesSupported = function () {
        return this.supportsCompressedTextures;
    };
    /**
     * 是否支持HDR
     * @returns 是否支持HDR
     */
    DeviceCapabilities.prototype.isHDRSupported = function () {
        return this.supportsHDR;
    };
    /**
     * 是否支持阴影
     * @returns 是否支持阴影
     */
    DeviceCapabilities.prototype.isShadowsSupported = function () {
        return this.supportsShadows;
    };
    /**
     * 获取最大纹理大小
     * @returns 最大纹理大小
     */
    DeviceCapabilities.prototype.getMaxTextureSize = function () {
        return this.maxTextureSize;
    };
    /**
     * 是否是移动设备
     * @returns 是否是移动设备
     */
    DeviceCapabilities.prototype.isMobileDevice = function () {
        return this.isMobile;
    };
    /**
     * 是否是VR/AR设备
     * @returns 是否是VR/AR设备
     */
    DeviceCapabilities.prototype.isXRDeviceEnabled = function () {
        return this.isXRDevice;
    };
    /**
     * 设置是否强制使用低性能模式
     * @param force 是否强制
     */
    DeviceCapabilities.prototype.setForceLowPerformance = function (force) {
        this.forceLowPerformance = force;
    };
    /**
     * 设置是否强制使用高性能模式
     * @param force 是否强制
     */
    DeviceCapabilities.prototype.setForceHighPerformance = function (force) {
        this.forceHighPerformance = force;
    };
    /**
     * 检测是否是移动设备
     * @returns 是否是移动设备
     */
    DeviceCapabilities.prototype.detectMobileDevice = function () {
        if (typeof navigator === 'undefined')
            return false;
        var userAgent = navigator.userAgent || navigator.vendor || window.opera;
        // 检测常见移动设备标识
        var mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i;
        return mobileRegex.test(userAgent);
    };
    /**
     * 检测是否是VR/AR设备
     * @returns 是否是VR/AR设备
     */
    DeviceCapabilities.prototype.detectXRDevice = function () {
        if (typeof navigator === 'undefined')
            return false;
        // 检测WebXR支持
        return 'xr' in navigator;
    };
    /**
     * 确定设备性能级别
     * @returns 设备性能级别
     */
    DeviceCapabilities.prototype.determinePerformanceLevel = function () {
        // 如果强制使用低性能模式
        if (this.forceLowPerformance) {
            return DevicePerformanceLevel.LOW;
        }
        // 如果强制使用高性能模式
        if (this.forceHighPerformance) {
            return DevicePerformanceLevel.HIGH;
        }
        // 如果是VR/AR设备，通常需要高性能
        if (this.isXRDevice) {
            return DevicePerformanceLevel.HIGH;
        }
        // 如果是移动设备，通常是中等或低性能
        if (this.isMobile) {
            // 如果不支持WebGL2，则为低性能
            if (!this.supportsWebGL2) {
                return DevicePerformanceLevel.LOW;
            }
            // 如果支持WebGL2但不支持浮点纹理，则为中等性能
            if (!this.supportsFloatTextures) {
                return DevicePerformanceLevel.MEDIUM;
            }
        }
        // 如果支持WebGL2和浮点纹理，则为高性能
        if (this.supportsWebGL2 && this.supportsFloatTextures) {
            return DevicePerformanceLevel.HIGH;
        }
        // 如果支持WebGL2但不支持浮点纹理，则为中等性能
        if (this.supportsWebGL2) {
            return DevicePerformanceLevel.MEDIUM;
        }
        // 默认为低性能
        return DevicePerformanceLevel.LOW;
    };
    /**
     * 初始化电池监控
     */
    DeviceCapabilities.prototype.initializeBatteryMonitoring = function () {
        var _this = this;
        // 检查是否支持电池API
        if ('getBattery' in navigator) {
            navigator.getBattery().then(function (battery) {
                // 更新电池状态
                _this.updateBatteryStatus(battery);
                // 监听电池状态变化
                battery.addEventListener('levelchange', function () {
                    _this.updateBatteryStatus(battery);
                });
                battery.addEventListener('chargingchange', function () {
                    _this.updateBatteryStatus(battery);
                });
            });
        }
    };
    /**
     * 更新电池状态
     * @param battery 电池对象
     */
    DeviceCapabilities.prototype.updateBatteryStatus = function (battery) {
        this.batteryLevel = battery.level * 100;
        this.batteryCharging = battery.charging;
    };
    /**
     * 初始化温度监控
     */
    DeviceCapabilities.prototype.initializeTemperatureMonitoring = function () {
        // 目前浏览器没有直接获取设备温度的API
        // 这里使用一个模拟的温度值，实际应用中可能需要通过其他方式获取
        this.temperature = 25 + Math.random() * 10;
    };
    /**
     * 初始化网络监控
     */
    DeviceCapabilities.prototype.initializeNetworkMonitoring = function () {
        // 检查是否支持网络信息API
        if ('connection' in navigator) {
            var connection = navigator.connection;
            // 监听网络状态变化
            connection.addEventListener('change', function () {
                // 更新网络状态
            });
        }
    };
    /**
     * 初始化性能监控
     */
    DeviceCapabilities.prototype.initializePerformanceMonitoring = function () {
        var _this = this;
        // 初始化FPS计数器
        var lastTime = performance.now();
        var frames = 0;
        var updateFPS = function () {
            var now = performance.now();
            frames++;
            if (now >= lastTime + 1000) {
                _this.currentFPS = frames * 1000 / (now - lastTime);
                frames = 0;
                lastTime = now;
                // 模拟CPU和GPU使用率
                _this.cpuUsage = Math.min(0.1 + Math.random() * 0.3 + (60 / _this.currentFPS) * 0.1, 1);
                _this.gpuUsage = Math.min(0.1 + Math.random() * 0.3 + (60 / _this.currentFPS) * 0.1, 1);
                _this.memoryUsage = Math.min(0.2 + Math.random() * 0.2, 1);
                // 如果启用了自动性能调整，根据性能数据调整性能级别
                if (_this.enableAutoPerformanceAdjustment) {
                    _this.adjustPerformanceLevel();
                }
            }
            requestAnimationFrame(updateFPS);
        };
        updateFPS();
    };
    /**
     * 调整性能级别
     */
    DeviceCapabilities.prototype.adjustPerformanceLevel = function () {
        // 如果强制使用低性能或高性能模式，则不自动调整
        if (this.forceLowPerformance || this.forceHighPerformance) {
            return;
        }
        // 如果帧率低于最小可接受帧率，降低性能级别
        if (this.currentFPS < this.minAcceptableFPS) {
            if (this.performanceLevel > DevicePerformanceLevel.LOW) {
                this.performanceLevel--;
            }
        }
        // 如果帧率高于目标帧率的1.2倍，提高性能级别
        else if (this.currentFPS > this.targetFPS * 1.2) {
            if (this.performanceLevel < DevicePerformanceLevel.HIGH) {
                this.performanceLevel++;
            }
        }
        // 如果电池电量低于阈值，降低性能级别
        if (this.enableBatteryMonitoring && this.batteryLevel < this.lowBatteryThreshold && !this.batteryCharging) {
            if (this.performanceLevel > DevicePerformanceLevel.LOW) {
                this.performanceLevel = DevicePerformanceLevel.LOW;
            }
        }
        // 如果设备温度高于阈值，降低性能级别
        if (this.enableTemperatureMonitoring && this.temperature > this.highTemperatureThreshold) {
            if (this.performanceLevel > DevicePerformanceLevel.LOW) {
                this.performanceLevel = DevicePerformanceLevel.LOW;
            }
        }
    };
    /**
     * 获取电池电量
     * @returns 电池电量（百分比）
     */
    DeviceCapabilities.prototype.getBatteryLevel = function () {
        return this.batteryLevel;
    };
    /**
     * 获取电池是否正在充电
     * @returns 电池是否正在充电
     */
    DeviceCapabilities.prototype.isBatteryCharging = function () {
        return this.batteryCharging;
    };
    /**
     * 获取设备温度
     * @returns 设备温度（摄氏度）
     */
    DeviceCapabilities.prototype.getTemperature = function () {
        return this.temperature;
    };
    /**
     * 获取当前帧率
     * @returns 当前帧率
     */
    DeviceCapabilities.prototype.getCurrentFPS = function () {
        return this.currentFPS;
    };
    /**
     * 获取CPU使用率
     * @returns CPU使用率（0-1）
     */
    DeviceCapabilities.prototype.getCPUUsage = function () {
        return this.cpuUsage;
    };
    /**
     * 获取GPU使用率
     * @returns GPU使用率（0-1）
     */
    DeviceCapabilities.prototype.getGPUUsage = function () {
        return this.gpuUsage;
    };
    /**
     * 获取内存使用率
     * @returns 内存使用率（0-1）
     */
    DeviceCapabilities.prototype.getMemoryUsage = function () {
        return this.memoryUsage;
    };
    /**
     * 设置性能级别
     * @param level 性能级别
     */
    DeviceCapabilities.prototype.setPerformanceLevel = function (level) {
        this.performanceLevel = level;
    };
    /**
     * 启用自动性能调整
     * @param enable 是否启用
     */
    DeviceCapabilities.prototype.enableAutoPerformanceAdjustment = function (enable) {
        this.enableAutoPerformanceAdjustment = enable;
    };
    /**
     * 设置低电量阈值
     * @param threshold 阈值（百分比）
     */
    DeviceCapabilities.prototype.setLowBatteryThreshold = function (threshold) {
        this.lowBatteryThreshold = threshold;
    };
    /**
     * 设置高温阈值
     * @param threshold 阈值（摄氏度）
     */
    DeviceCapabilities.prototype.setHighTemperatureThreshold = function (threshold) {
        this.highTemperatureThreshold = threshold;
    };
    /**
     * 设置目标帧率
     * @param fps 帧率
     */
    DeviceCapabilities.prototype.setTargetFPS = function (fps) {
        this.targetFPS = fps;
    };
    /**
     * 设置最小可接受帧率
     * @param fps 帧率
     */
    DeviceCapabilities.prototype.setMinAcceptableFPS = function (fps) {
        this.minAcceptableFPS = fps;
    };
    return DeviceCapabilities;
}());
exports.DeviceCapabilities = DeviceCapabilities;

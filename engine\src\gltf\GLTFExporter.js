"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GLTFExporter = void 0;
/**
 * GLTF导出器
 * 用于导出场景或对象为GLTF格式
 */
var THREE = require("three");
var EventEmitter_1 = require("../utils/EventEmitter");
/**
 * GLTF导出器
 */
var GLTFExporter = /** @class */ (function (_super) {
    __extends(GLTFExporter, _super);
    /**
     * 创建GLTF导出器
     */
    function GLTFExporter() {
        var _this = _super.call(this) || this;
        // 动态导入GLTFExporter
        _this.exporter = new THREE.GLTFExporter();
        return _this;
    }
    /**
     * 导出场景
     * @param scene 场景
     * @param options 导出选项
     * @returns Promise，解析为导出的GLTF数据
     */
    GLTFExporter.prototype.exportScene = function (scene, options) {
        if (options === void 0) { options = {}; }
        return __awaiter(this, void 0, void 0, function () {
            var threeScene;
            return __generator(this, function (_a) {
                threeScene = scene.getThreeScene();
                // 导出场景
                return [2 /*return*/, this.export(threeScene, options)];
            });
        });
    };
    /**
     * 导出实体
     * @param entity 实体
     * @param options 导出选项
     * @returns Promise，解析为导出的GLTF数据
     */
    GLTFExporter.prototype.exportEntity = function (entity, options) {
        if (options === void 0) { options = {}; }
        return __awaiter(this, void 0, void 0, function () {
            var transform, object;
            return __generator(this, function (_a) {
                transform = entity.getTransform();
                if (!transform) {
                    throw new Error('实体没有变换组件');
                }
                object = transform.getObject3D();
                // 导出对象
                return [2 /*return*/, this.export(object, options)];
            });
        });
    };
    /**
     * 导出对象
     * @param input 输入对象（Three.js对象、场景或数组）
     * @param options 导出选项
     * @returns Promise，解析为导出的GLTF数据
     */
    GLTFExporter.prototype.export = function (input, options) {
        if (options === void 0) { options = {}; }
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve, reject) {
                        try {
                            _this.exporter.parse(input, function (gltf) {
                                resolve(gltf);
                                _this.emit('exported', { input: input, gltf: gltf });
                            }, function (error) {
                                reject(new Error("\u5BFC\u51FAGLTF\u5931\u8D25: ".concat(error.message)));
                                _this.emit('error', error);
                            }, {
                                animations: options.animations || [],
                                binary: options.binary !== undefined ? options.binary : false,
                                includeCustomExtensions: options.includeCustomExtensions !== undefined ? options.includeCustomExtensions : false,
                                onlyVisible: options.onlyVisible !== undefined ? options.onlyVisible : true,
                                truncateDrawRange: options.truncateDrawRange !== undefined ? options.truncateDrawRange : true,
                                forceIndices: options.forceIndices !== undefined ? options.forceIndices : false,
                                maxTextureSize: options.maxTextureSize || Infinity
                            });
                        }
                        catch (error) {
                            reject(error);
                            _this.emit('error', error);
                        }
                    })];
            });
        });
    };
    /**
     * 将GLTF数据保存为文件
     * @param gltf GLTF数据
     * @param filename 文件名
     */
    GLTFExporter.prototype.saveAs = function (gltf, filename) {
        var blob;
        if (gltf instanceof ArrayBuffer) {
            // 二进制GLTF
            blob = new Blob([gltf], { type: 'application/octet-stream' });
            // 如果没有扩展名，添加.glb扩展名
            if (!filename.toLowerCase().endsWith('.glb')) {
                filename += '.glb';
            }
        }
        else {
            // JSON GLTF
            var json = JSON.stringify(gltf, null, 2);
            blob = new Blob([json], { type: 'application/json' });
            // 如果没有扩展名，添加.gltf扩展名
            if (!filename.toLowerCase().endsWith('.gltf')) {
                filename += '.gltf';
            }
        }
        // 创建下载链接
        var link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
        // 清理
        URL.revokeObjectURL(link.href);
    };
    return GLTFExporter;
}(EventEmitter_1.EventEmitter));
exports.GLTFExporter = GLTFExporter;

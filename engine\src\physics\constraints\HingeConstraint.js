"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.HingeConstraint = void 0;
/**
 * 铰链约束
 * 将两个物体通过一个轴连接起来，允许它们绕该轴旋转
 */
var THREE = require("three");
var CANNON = require("cannon-es");
var PhysicsConstraint_1 = require("./PhysicsConstraint");
/**
 * 铰链约束
 */
var HingeConstraint = exports.HingeConstraint = /** @class */ (function (_super) {
    __extends(HingeConstraint, _super);
    /**
     * 创建铰链约束
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    function HingeConstraint(targetEntity, options) {
        if (targetEntity === void 0) { targetEntity = null; }
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, PhysicsConstraint_1.ConstraintType.HINGE, targetEntity, options) || this;
        // 设置源物体上的连接点
        var pivotA = options.pivotA || new THREE.Vector3(0, 0, 0);
        _this.pivotA = new CANNON.Vec3(pivotA.x, pivotA.y, pivotA.z);
        // 设置目标物体上的连接点
        var pivotB = options.pivotB || new THREE.Vector3(0, 0, 0);
        _this.pivotB = new CANNON.Vec3(pivotB.x, pivotB.y, pivotB.z);
        // 设置源物体上的轴
        var axisA = options.axisA || new THREE.Vector3(1, 0, 0);
        _this.axisA = new CANNON.Vec3(axisA.x, axisA.y, axisA.z);
        _this.axisA.normalize();
        // 设置目标物体上的轴
        var axisB = options.axisB || new THREE.Vector3(1, 0, 0);
        _this.axisB = new CANNON.Vec3(axisB.x, axisB.y, axisB.z);
        _this.axisB.normalize();
        // 设置最大力
        _this.maxForce = options.maxForce !== undefined ? options.maxForce : 1e6;
        // 设置电机参数
        _this.motorEnabled = options.motorEnabled || false;
        _this.motorSpeed = options.motorSpeed || 0;
        _this.motorMaxForce = options.motorMaxForce || 1e6;
        // 设置角度限制参数
        _this.angleEnabled = options.angleEnabled || false;
        _this.angleMin = options.angleMin || 0;
        _this.angleMax = options.angleMax || 0;
        return _this;
    }
    /**
     * 创建约束
     */
    HingeConstraint.prototype.createConstraint = function () {
        // 获取源物体和目标物体
        var bodyA = this.getSourceBody();
        var bodyB = this.getTargetBody();
        // 如果没有源物体或目标物体，则无法创建约束
        if (!bodyA || !bodyB) {
            console.warn('无法创建铰链约束：缺少源物体或目标物体');
            return;
        }
        // 创建铰链约束
        this.constraint = new CANNON.HingeConstraint(bodyA, bodyB, {
            pivotA: this.pivotA,
            pivotB: this.pivotB,
            axisA: this.axisA,
            axisB: this.axisB,
            maxForce: this.maxForce,
            collideConnected: this.collideConnected
        });
        // 设置电机
        if (this.motorEnabled) {
            this.enableMotor();
            this.setMotorSpeed(this.motorSpeed);
            this.setMotorMaxForce(this.motorMaxForce);
        }
        // 设置角度限制
        if (this.angleEnabled) {
            this.enableAngleLimits();
            this.setAngleLimits(this.angleMin, this.angleMax);
        }
    };
    /**
     * 设置源物体上的连接点
     * @param pivot 连接点（局部坐标）
     */
    HingeConstraint.prototype.setPivotA = function (pivot) {
        this.pivotA.set(pivot.x, pivot.y, pivot.z);
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取源物体上的连接点
     * @returns 连接点（局部坐标）
     */
    HingeConstraint.prototype.getPivotA = function () {
        return new THREE.Vector3(this.pivotA.x, this.pivotA.y, this.pivotA.z);
    };
    /**
     * 设置目标物体上的连接点
     * @param pivot 连接点（局部坐标）
     */
    HingeConstraint.prototype.setPivotB = function (pivot) {
        this.pivotB.set(pivot.x, pivot.y, pivot.z);
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取目标物体上的连接点
     * @returns 连接点（局部坐标）
     */
    HingeConstraint.prototype.getPivotB = function () {
        return new THREE.Vector3(this.pivotB.x, this.pivotB.y, this.pivotB.z);
    };
    /**
     * 设置源物体上的轴
     * @param axis 轴（局部坐标）
     */
    HingeConstraint.prototype.setAxisA = function (axis) {
        this.axisA.set(axis.x, axis.y, axis.z);
        this.axisA.normalize();
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取源物体上的轴
     * @returns 轴（局部坐标）
     */
    HingeConstraint.prototype.getAxisA = function () {
        return new THREE.Vector3(this.axisA.x, this.axisA.y, this.axisA.z);
    };
    /**
     * 设置目标物体上的轴
     * @param axis 轴（局部坐标）
     */
    HingeConstraint.prototype.setAxisB = function (axis) {
        this.axisB.set(axis.x, axis.y, axis.z);
        this.axisB.normalize();
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取目标物体上的轴
     * @returns 轴（局部坐标）
     */
    HingeConstraint.prototype.getAxisB = function () {
        return new THREE.Vector3(this.axisB.x, this.axisB.y, this.axisB.z);
    };
    /**
     * 启用电机
     */
    HingeConstraint.prototype.enableMotor = function () {
        this.motorEnabled = true;
        if (this.constraint instanceof CANNON.HingeConstraint) {
            this.constraint.enableMotor();
        }
    };
    /**
     * 禁用电机
     */
    HingeConstraint.prototype.disableMotor = function () {
        this.motorEnabled = false;
        if (this.constraint instanceof CANNON.HingeConstraint) {
            this.constraint.disableMotor();
        }
    };
    /**
     * 设置电机速度
     * @param speed 速度（弧度/秒）
     */
    HingeConstraint.prototype.setMotorSpeed = function (speed) {
        this.motorSpeed = speed;
        if (this.constraint instanceof CANNON.HingeConstraint) {
            this.constraint.setMotorSpeed(speed);
        }
    };
    /**
     * 获取电机速度
     * @returns 速度（弧度/秒）
     */
    HingeConstraint.prototype.getMotorSpeed = function () {
        return this.motorSpeed;
    };
    /**
     * 设置电机最大力
     * @param maxForce 最大力
     */
    HingeConstraint.prototype.setMotorMaxForce = function (maxForce) {
        this.motorMaxForce = maxForce;
        if (this.constraint instanceof CANNON.HingeConstraint) {
            this.constraint.motorEquation.maxForce = maxForce;
            this.constraint.motorEquation.minForce = -maxForce;
        }
    };
    /**
     * 获取电机最大力
     * @returns 最大力
     */
    HingeConstraint.prototype.getMotorMaxForce = function () {
        return this.motorMaxForce;
    };
    /**
     * 启用角度限制
     */
    HingeConstraint.prototype.enableAngleLimits = function () {
        this.angleEnabled = true;
        if (this.constraint instanceof CANNON.HingeConstraint) {
            this.constraint.setLimits(this.angleMin, this.angleMax);
        }
    };
    /**
     * 禁用角度限制
     */
    HingeConstraint.prototype.disableAngleLimits = function () {
        this.angleEnabled = false;
        if (this.constraint instanceof CANNON.HingeConstraint) {
            // 设置一个非常大的范围，相当于禁用限制
            this.constraint.setLimits(-1e10, 1e10);
        }
    };
    /**
     * 设置角度限制
     * @param min 最小角度（弧度）
     * @param max 最大角度（弧度）
     */
    HingeConstraint.prototype.setAngleLimits = function (min, max) {
        this.angleMin = min;
        this.angleMax = max;
        if (this.constraint instanceof CANNON.HingeConstraint && this.angleEnabled) {
            this.constraint.setLimits(min, max);
        }
    };
    /**
     * 获取最小角度
     * @returns 最小角度（弧度）
     */
    HingeConstraint.prototype.getAngleMin = function () {
        return this.angleMin;
    };
    /**
     * 获取最大角度
     * @returns 最大角度（弧度）
     */
    HingeConstraint.prototype.getAngleMax = function () {
        return this.angleMax;
    };
    /**
     * 获取当前角度
     * @returns 当前角度（弧度）
     */
    HingeConstraint.prototype.getAngle = function () {
        if (this.constraint instanceof CANNON.HingeConstraint) {
            return this.constraint.getAngle();
        }
        return 0;
    };
    /**
     * 重新创建约束
     */
    HingeConstraint.prototype.recreateConstraint = function () {
        if (this.initialized && this.constraint && this.world) {
            this.world.removeConstraint(this.constraint);
            this.constraint = null;
            this.initialized = false;
            this.initialize(this.world);
        }
    };
    /** 组件类型 */
    HingeConstraint.type = 'HingeConstraint';
    return HingeConstraint;
}(PhysicsConstraint_1.PhysicsConstraint));

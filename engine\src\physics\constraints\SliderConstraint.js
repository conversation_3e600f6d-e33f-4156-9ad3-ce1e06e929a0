"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SliderConstraint = void 0;
/**
 * 滑动约束
 * 限制两个物体沿着一个轴线滑动
 */
var THREE = require("three");
var CANNON = require("cannon-es");
var PhysicsConstraint_1 = require("./PhysicsConstraint");
/**
 * 滑动约束
 */
var SliderConstraint = exports.SliderConstraint = /** @class */ (function (_super) {
    __extends(SliderConstraint, _super);
    /**
     * 创建滑动约束
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    function SliderConstraint(targetEntity, options) {
        if (targetEntity === void 0) { targetEntity = null; }
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, PhysicsConstraint_1.ConstraintType.SLIDER, targetEntity, options) || this;
        // 设置轴向
        _this.axisA = options.axisA ? options.axisA.clone() : new THREE.Vector3(1, 0, 0);
        _this.axisB = options.axisB ? options.axisB.clone() : new THREE.Vector3(1, 0, 0);
        // 设置限制
        _this.lowerLimit = options.lowerLimit !== undefined ? options.lowerLimit : -1;
        _this.upperLimit = options.upperLimit !== undefined ? options.upperLimit : 1;
        // 设置最大力
        _this.maxForce = options.maxForce !== undefined ? options.maxForce : 1e6;
        return _this;
    }
    /**
     * 创建约束
     */
    SliderConstraint.prototype.createConstraint = function () {
        // 获取源物体和目标物体
        var bodyA = this.getSourceBody();
        var bodyB = this.getTargetBody();
        // 如果没有源物体或目标物体，则无法创建约束
        if (!bodyA || !bodyB) {
            console.warn('无法创建滑动约束：缺少源物体或目标物体');
            return;
        }
        // 创建滑动约束
        // 注意：CANNON.js没有内置的滑动约束，我们使用点对点约束和铰链约束组合实现
        // 这是一个简化的实现，实际应用中可能需要更复杂的算法
        // 创建点对点约束
        var pointConstraint = new CANNON.PointToPointConstraint(bodyA, new CANNON.Vec3(0, 0, 0), bodyB, new CANNON.Vec3(0, 0, 0), this.maxForce);
        // 创建铰链约束
        var hingeConstraint = new CANNON.HingeConstraint(bodyA, bodyB, {
            pivotA: new CANNON.Vec3(0, 0, 0),
            axisA: new CANNON.Vec3(this.axisA.x, this.axisA.y, this.axisA.z),
            pivotB: new CANNON.Vec3(0, 0, 0),
            axisB: new CANNON.Vec3(this.axisB.x, this.axisB.y, this.axisB.z)
        });
        // 设置是否允许连接的物体之间碰撞
        pointConstraint.collideConnected = this.collideConnected;
        hingeConstraint.collideConnected = this.collideConnected;
        // 保存约束
        this.constraint = pointConstraint;
        // 保存辅助约束
        this.auxiliaryConstraint = hingeConstraint;
        // 添加辅助约束到物理世界
        if (this.world) {
            this.world.addConstraint(hingeConstraint);
        }
    };
    /**
     * 设置轴向A
     * @param axis 轴向
     */
    SliderConstraint.prototype.setAxisA = function (axis) {
        this.axisA.copy(axis);
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取轴向A
     * @returns 轴向
     */
    SliderConstraint.prototype.getAxisA = function () {
        return this.axisA.clone();
    };
    /**
     * 设置轴向B
     * @param axis 轴向
     */
    SliderConstraint.prototype.setAxisB = function (axis) {
        this.axisB.copy(axis);
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取轴向B
     * @returns 轴向
     */
    SliderConstraint.prototype.getAxisB = function () {
        return this.axisB.clone();
    };
    /**
     * 设置最小位移
     * @param limit 最小位移
     */
    SliderConstraint.prototype.setLowerLimit = function (limit) {
        this.lowerLimit = limit;
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取最小位移
     * @returns 最小位移
     */
    SliderConstraint.prototype.getLowerLimit = function () {
        return this.lowerLimit;
    };
    /**
     * 设置最大位移
     * @param limit 最大位移
     */
    SliderConstraint.prototype.setUpperLimit = function (limit) {
        this.upperLimit = limit;
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取最大位移
     * @returns 最大位移
     */
    SliderConstraint.prototype.getUpperLimit = function () {
        return this.upperLimit;
    };
    /**
     * 设置最大力
     * @param maxForce 最大力
     */
    SliderConstraint.prototype.setMaxForce = function (maxForce) {
        this.maxForce = maxForce;
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取最大力
     * @returns 最大力
     */
    SliderConstraint.prototype.getMaxForce = function () {
        return this.maxForce;
    };
    /**
     * 重新创建约束
     */
    SliderConstraint.prototype.recreateConstraint = function () {
        if (this.initialized && this.constraint && this.world) {
            this.world.removeConstraint(this.constraint);
            this.constraint = null;
            this.initialized = false;
            this.initialize(this.world);
        }
    };
    /**
     * 销毁约束
     */
    SliderConstraint.prototype.dispose = function () {
        // 移除辅助约束
        if (this.auxiliaryConstraint && this.world) {
            this.world.removeConstraint(this.auxiliaryConstraint);
            this.auxiliaryConstraint = null;
        }
        // 调用父类的销毁方法
        _super.prototype.dispose.call(this);
    };
    /** 组件类型 */
    SliderConstraint.type = 'SliderConstraint';
    return SliderConstraint;
}(PhysicsConstraint_1.PhysicsConstraint));

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedNetworkSystem = exports.NetworkEntitySyncMode = exports.NetworkEntityType = void 0;
/**
 * 增强型网络系统
 * 集成了预测和插值、空间分区、网络质量监控和带宽控制等高级功能
 */
var System_1 = require("../core/System");
var Debug_1 = require("../utils/Debug");
var EventEmitter_1 = require("../utils/EventEmitter");
var WebRTCConnectionManager_1 = require("./WebRTCConnectionManager");
var MediaStreamManager_1 = require("./MediaStreamManager");
var EntitySyncManager_1 = require("./EntitySyncManager");
var UserSessionManager_1 = require("./UserSessionManager");
var NetworkEventDispatcher_1 = require("./NetworkEventDispatcher");
var NetworkEventBuffer_1 = require("./NetworkEventBuffer");
var NetworkQualityMonitor_1 = require("./NetworkQualityMonitor");
var DataCompressor_1 = require("./DataCompressor");
var ServiceDiscoveryClient_1 = require("./ServiceDiscoveryClient");
var NetworkPredictor_1 = require("./NetworkPredictor");
var NetworkAdaptiveController_1 = require("./NetworkAdaptiveController");
var AdvancedBandwidthController_1 = require("./AdvancedBandwidthController");
var QuadtreePartitioning_1 = require("./spatial/QuadtreePartitioning");
var NetworkEntityComponent_1 = require("./components/NetworkEntityComponent");
var types_1 = require("./types");
// 定义缺失的枚举类型
var NetworkEntityType;
(function (NetworkEntityType) {
    NetworkEntityType["STATIC"] = "static";
    NetworkEntityType["DYNAMIC"] = "dynamic";
    NetworkEntityType["PLAYER"] = "player";
    NetworkEntityType["NPC"] = "npc";
    NetworkEntityType["OBJECT"] = "object";
})(NetworkEntityType || (exports.NetworkEntityType = NetworkEntityType = {}));
var NetworkEntitySyncMode;
(function (NetworkEntitySyncMode) {
    NetworkEntitySyncMode["FULL"] = "full";
    NetworkEntitySyncMode["DELTA"] = "delta";
    NetworkEntitySyncMode["PRIORITY"] = "priority";
    NetworkEntitySyncMode["SPATIAL"] = "spatial";
})(NetworkEntitySyncMode || (exports.NetworkEntitySyncMode = NetworkEntitySyncMode = {}));
/**
 * 增强型网络系统
 */
var EnhancedNetworkSystem = /** @class */ (function (_super) {
    __extends(EnhancedNetworkSystem, _super);
    /**
     * 创建增强型网络系统
     * @param options 配置
     */
    function EnhancedNetworkSystem(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, 1) || this;
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 网络状态 */
        _this.state = types_1.NetworkState.DISCONNECTED;
        /** 本地用户ID */
        _this.localUserId = null;
        /** 重连尝试次数 */
        _this.reconnectAttempts = 0;
        /** 重连定时器ID */
        _this.reconnectTimerId = null;
        /** 同步定时器ID */
        _this.syncTimerId = null;
        /** 网络管理器 */
        _this.networkManager = null;
        /** 媒体流管理器 */
        _this.mediaStreamManager = null;
        /** 实体同步管理器 */
        _this.entitySyncManager = null;
        /** 用户会话管理器 */
        _this.userSessionManager = null;
        /** 网络事件调度器 */
        _this.eventDispatcher = null;
        /** 网络事件缓冲 */
        _this.eventBuffer = null;
        /** 网络质量监控器 */
        _this.networkQualityMonitor = null;
        /** 数据压缩器 */
        _this.dataCompressor = null;
        /** 服务发现客户端 */
        _this.serviceDiscoveryClient = null;
        /** 网络预测器 */
        _this.networkPredictor = null;
        /** 网络自适应控制器 */
        _this.networkAdaptiveController = null;
        /** 高级带宽控制器 */
        _this.bandwidthController = null;
        /** 空间分区系统 */
        _this.spatialPartitioning = null;
        /** 网络实体映射表 */
        _this.networkEntities = new Map();
        // 默认配置
        _this.options = __assign({ enabled: true, localUserId: '', syncInterval: 100, maxReconnectAttempts: 5, reconnectInterval: 3000, enableCompression: true, compressionAlgorithm: DataCompressor_1.CompressionAlgorithm.LZ_STRING, compressionLevel: DataCompressor_1.CompressionLevel.MEDIUM, enableMediaStream: true, enableAudio: false, enableVideo: false, enableScreenShare: false, enableNetworkQualityMonitor: true, enableBandwidthControl: true, bandwidthControlStrategy: AdvancedBandwidthController_1.BandwidthControlStrategy.ADAPTIVE, maxUploadBandwidth: 1024 * 1024, maxDownloadBandwidth: 1024 * 1024, enableEntitySync: true, enableUserSessionManagement: true, defaultUserRole: UserSessionManager_1.UserRole.USER, enablePermissionCheck: true, enableEventBuffer: true, enableEventLogging: false, enableServiceDiscovery: true, serviceRegistryUrl: 'http://localhost:4010/api/registry', enableMicroserviceClient: true, apiGatewayUrl: 'http://localhost:3000/api', useApiGateway: true, enablePrediction: true, predictionAlgorithm: NetworkPredictor_1.PredictionAlgorithm.ADAPTIVE, maxPredictionTime: 200, enableInterpolation: true, interpolationFactor: 0.5, enableSpatialPartitioning: true, spatialPartitioningMaxDepth: 8, spatialPartitioningMaxEntities: 16, enableAdaptiveControl: true, adaptiveStrategy: NetworkAdaptiveController_1.AdaptiveStrategy.BALANCED, enableJitterBuffer: true, jitterBufferSize: 100, enablePrioritySync: true, enableDeltaSync: true }, options);
        // 设置本地用户ID
        _this.localUserId = _this.options.localUserId || null;
        // 初始化系统
        _this.initialize();
        return _this;
    }
    /**
     * 初始化系统
     */
    EnhancedNetworkSystem.prototype.initialize = function () {
        var _this = this;
        var _a, _b, _c, _d;
        if (!this.options.enabled) {
            Debug_1.Debug.log('EnhancedNetworkSystem', '网络系统已禁用');
            return;
        }
        // 初始化数据压缩器
        if (this.options.enableCompression) {
            this.dataCompressor = new DataCompressor_1.DataCompressor({
                algorithm: this.options.compressionAlgorithm,
                level: this.options.compressionLevel,
            });
        }
        // 初始化网络质量监控器
        if (this.options.enableNetworkQualityMonitor) {
            this.networkQualityMonitor = new NetworkQualityMonitor_1.NetworkQualityMonitor({
                sampleInterval: 1000,
                historySize: 10,
                pingInterval: 2000,
            });
            // 监听网络质量变化
            this.networkQualityMonitor.on('qualityChanged', function (quality) {
                _this.handleNetworkQualityChange(quality);
            });
        }
        // 初始化带宽控制器
        if (this.options.enableBandwidthControl) {
            this.bandwidthController = new AdvancedBandwidthController_1.AdvancedBandwidthController({
                maxUploadBandwidth: this.options.maxUploadBandwidth,
                maxDownloadBandwidth: this.options.maxDownloadBandwidth,
                strategy: this.options.bandwidthControlStrategy,
                autoAdjust: true,
            });
        }
        // 初始化网络预测器
        if (this.options.enablePrediction) {
            this.networkPredictor = new NetworkPredictor_1.NetworkPredictor({
                algorithm: this.options.predictionAlgorithm,
                maxPredictionTime: this.options.maxPredictionTime,
                useSmoothing: true,
                smoothingFactor: 0.3,
                useAdaptivePrediction: true,
                useJitterBuffer: this.options.enableJitterBuffer,
                jitterBufferSize: this.options.jitterBufferSize,
            });
        }
        // 初始化空间分区系统
        if (this.options.enableSpatialPartitioning) {
            this.spatialPartitioning = new QuadtreePartitioning_1.QuadtreePartitioning({
                maxDepth: this.options.spatialPartitioningMaxDepth,
                maxEntities: this.options.spatialPartitioningMaxEntities,
                worldSize: 1000,
                enableDynamicAdjustment: true,
            });
        }
        // 初始化网络事件缓冲
        if (this.options.enableEventBuffer) {
            this.eventBuffer = new NetworkEventBuffer_1.NetworkEventBuffer({
                maxBufferSize: 100,
            });
        }
        // 初始化网络事件调度器
        this.eventDispatcher = new NetworkEventDispatcher_1.NetworkEventDispatcher({
            enableEventLogging: this.options.enableEventLogging,
            useEventBuffer: !!this.eventBuffer,
        });
        // 初始化用户会话管理器
        if (this.options.enableUserSessionManagement) {
            this.userSessionManager = new UserSessionManager_1.UserSessionManager({
                defaultRole: this.options.defaultUserRole,
                enablePermissionCheck: this.options.enablePermissionCheck,
            });
        }
        // 初始化实体同步管理器
        if (this.options.enableEntitySync) {
            this.entitySyncManager = new EntitySyncManager_1.EntitySyncManager({
                minSyncInterval: this.options.syncInterval,
                useDeltaSync: this.options.enableDeltaSync,
                usePrioritySync: this.options.enablePrioritySync,
                useSpatialPartitioning: this.options.enableSpatialPartitioning,
                useInterpolation: this.options.enableInterpolation,
                useExtrapolation: this.options.enablePrediction,
                extrapolationTime: this.options.maxPredictionTime,
            });
            // 设置空间分区系统（如果支持）
            if (this.spatialPartitioning && typeof this.entitySyncManager.setSpatialPartitioning === 'function') {
                this.entitySyncManager.setSpatialPartitioning(this.spatialPartitioning);
            }
            // 设置网络预测器（如果支持）
            if (this.networkPredictor && typeof this.entitySyncManager.setNetworkPredictor === 'function') {
                this.entitySyncManager.setNetworkPredictor(this.networkPredictor);
            }
        }
        // 初始化媒体流管理器
        if (this.options.enableMediaStream) {
            this.mediaStreamManager = new MediaStreamManager_1.MediaStreamManager({});
        }
        // 初始化网络自适应控制器
        if (this.options.enableAdaptiveControl) {
            // 创建初始网络参数配置
            var initialParams = {
                syncInterval: this.options.syncInterval,
                compressionLevel: this.options.compressionLevel,
                useDeltaSync: this.options.enableDeltaSync,
                usePrediction: this.options.enablePrediction,
                predictionTime: this.options.maxPredictionTime,
                useInterpolation: this.options.enableInterpolation,
                interpolationFactor: this.options.interpolationFactor,
                usePrioritySync: this.options.enablePrioritySync,
                useSpatialPartitioning: this.options.enableSpatialPartitioning,
                useJitterBuffer: this.options.enableJitterBuffer,
                jitterBufferSize: this.options.jitterBufferSize,
                maxUploadBandwidth: this.options.maxUploadBandwidth,
                maxDownloadBandwidth: this.options.maxDownloadBandwidth,
            };
            this.networkAdaptiveController = new NetworkAdaptiveController_1.NetworkAdaptiveController(initialParams, {
                strategy: this.options.adaptiveStrategy,
                enableAutoAdjust: true,
                enableHistory: true,
                enablePredictiveAdjustment: true,
            });
            // 设置带宽控制器（使用类型断言）
            if (this.bandwidthController) {
                (_b = (_a = this.networkAdaptiveController).setBandwidthController) === null || _b === void 0 ? void 0 : _b.call(_a, this.bandwidthController);
            }
            // 设置实体同步管理器
            if (this.entitySyncManager) {
                (_d = (_c = this.networkAdaptiveController).setEntitySyncManager) === null || _d === void 0 ? void 0 : _d.call(_c, this.entitySyncManager);
            }
            // 监听参数调整事件
            this.networkAdaptiveController.on('paramsAdjusted', function (data) {
                _this.handleNetworkParamsAdjusted(data.params);
            });
        }
        // 初始化服务发现客户端
        if (this.options.enableServiceDiscovery) {
            this.serviceDiscoveryClient = new ServiceDiscoveryClient_1.ServiceDiscoveryClient({
                registryUrl: this.options.serviceRegistryUrl,
            });
        }
        // 初始化WebRTC连接管理器
        this.networkManager = new WebRTCConnectionManager_1.WebRTCConnectionManager({
            enableDataChannel: true,
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' },
            ],
        });
        // 监听连接事件
        this.networkManager.on('connected', function (peerId) {
            _this.handlePeerConnected(peerId);
        });
        this.networkManager.on('disconnected', function (peerId) {
            _this.handlePeerDisconnected(peerId);
        });
        this.networkManager.on('dataReceived', function (peerId, data) {
            _this.handleDataReceived(peerId, data);
        });
        // 启动同步定时器
        this.startSyncTimer();
        Debug_1.Debug.log('EnhancedNetworkSystem', '网络系统已初始化');
    };
    /**
     * 启动同步定时器
     */
    EnhancedNetworkSystem.prototype.startSyncTimer = function () {
        var _this = this;
        if (this.syncTimerId !== null) {
            return;
        }
        this.syncTimerId = window.setInterval(function () {
            _this.syncEntities();
        }, this.options.syncInterval);
    };
    /**
     * 停止同步定时器
     */
    EnhancedNetworkSystem.prototype.stopSyncTimer = function () {
        if (this.syncTimerId !== null) {
            clearInterval(this.syncTimerId);
            this.syncTimerId = null;
        }
    };
    /**
     * 同步实体
     */
    EnhancedNetworkSystem.prototype.syncEntities = function () {
        var _a, _b;
        if (!this.entitySyncManager || this.state !== types_1.NetworkState.CONNECTED) {
            return;
        }
        // 使用类型断言调用私有方法
        (_b = (_a = this.entitySyncManager).syncEntities) === null || _b === void 0 ? void 0 : _b.call(_a);
    };
    /**
     * 处理网络质量变化
     * @param quality 网络质量数据
     */
    EnhancedNetworkSystem.prototype.handleNetworkQualityChange = function (quality) {
        var _a, _b;
        // 更新带宽控制器
        if (this.bandwidthController) {
            (_b = (_a = this.bandwidthController).setNetworkQuality) === null || _b === void 0 ? void 0 : _b.call(_a, quality);
        }
        // 更新网络自适应控制器
        if (this.networkAdaptiveController) {
            this.networkAdaptiveController.setNetworkQuality(quality);
        }
        // 触发网络质量变化事件
        this.eventEmitter.emit('networkQualityChanged', quality);
    };
    /**
     * 处理网络参数调整
     * @param params 网络参数
     */
    EnhancedNetworkSystem.prototype.handleNetworkParamsAdjusted = function (params) {
        // 更新同步间隔
        if (params.syncInterval !== this.options.syncInterval) {
            this.options.syncInterval = params.syncInterval;
            // 重启同步定时器
            this.stopSyncTimer();
            this.startSyncTimer();
        }
        // 更新压缩级别
        if (params.compressionLevel !== this.options.compressionLevel) {
            this.options.compressionLevel = params.compressionLevel;
            if (this.dataCompressor) {
                // 使用setOptions方法更新压缩级别
                this.dataCompressor.setOptions({ level: params.compressionLevel });
            }
        }
        // 更新预测时间
        if (params.predictionTime !== this.options.maxPredictionTime) {
            this.options.maxPredictionTime = params.predictionTime;
        }
        // 更新插值因子
        if (params.interpolationFactor !== this.options.interpolationFactor) {
            this.options.interpolationFactor = params.interpolationFactor;
        }
        // 更新抖动缓冲大小
        if (params.jitterBufferSize !== this.options.jitterBufferSize) {
            this.options.jitterBufferSize = params.jitterBufferSize;
        }
        // 触发参数调整事件
        this.eventEmitter.emit('networkParamsAdjusted', params);
    };
    /**
     * 处理对等连接
     * @param peerId 对等ID
     */
    EnhancedNetworkSystem.prototype.handlePeerConnected = function (peerId) {
        var _a, _b;
        Debug_1.Debug.log('EnhancedNetworkSystem', "\u5BF9\u7B49\u8FDE\u63A5\u5DF2\u5EFA\u7ACB: ".concat(peerId));
        // 更新状态
        this.state = types_1.NetworkState.CONNECTED;
        // 重置重连尝试次数
        this.reconnectAttempts = 0;
        // 添加用户会话
        if (this.userSessionManager) {
            (_b = (_a = this.userSessionManager).addUser) === null || _b === void 0 ? void 0 : _b.call(_a, peerId, this.options.defaultUserRole);
        }
        // 触发连接事件
        this.eventEmitter.emit('peerConnected', peerId);
    };
    /**
     * 处理对等断开连接
     * @param peerId 对等ID
     */
    EnhancedNetworkSystem.prototype.handlePeerDisconnected = function (peerId) {
        var _a, _b, _c, _d, _e, _f;
        Debug_1.Debug.log('EnhancedNetworkSystem', "\u5BF9\u7B49\u8FDE\u63A5\u5DF2\u65AD\u5F00: ".concat(peerId));
        // 移除用户会话
        if (this.userSessionManager) {
            (_b = (_a = this.userSessionManager).removeUser) === null || _b === void 0 ? void 0 : _b.call(_a, peerId);
        }
        // 移除实体
        if (this.entitySyncManager) {
            (_d = (_c = this.entitySyncManager).removeRemoteEntities) === null || _d === void 0 ? void 0 : _d.call(_c, peerId);
        }
        // 触发断开连接事件
        this.eventEmitter.emit('peerDisconnected', peerId);
        // 如果没有连接的对等点，则更新状态
        if (this.networkManager && ((_f = (_e = this.networkManager).getPeerCount) === null || _f === void 0 ? void 0 : _f.call(_e)) === 0) {
            this.state = types_1.NetworkState.DISCONNECTED;
            // 尝试重连
            this.tryReconnect();
        }
    };
    /**
     * 处理接收到的数据
     * @param peerId 对等ID
     * @param data 数据
     */
    EnhancedNetworkSystem.prototype.handleDataReceived = function (peerId, data) {
        var _a, _b, _c, _d;
        // 解压数据
        var decompressedData = data;
        if (this.options.enableCompression && this.dataCompressor && typeof data === 'string') {
            try {
                decompressedData = this.dataCompressor.decompress(data);
            }
            catch (error) {
                Debug_1.Debug.error('EnhancedNetworkSystem', "\u89E3\u538B\u6570\u636E\u5931\u8D25: ".concat(error));
                return;
            }
        }
        // 处理事件
        if (this.eventDispatcher) {
            (_b = (_a = this.eventDispatcher).handleIncomingEvent) === null || _b === void 0 ? void 0 : _b.call(_a, peerId, decompressedData);
        }
        // 处理实体同步
        if (this.entitySyncManager) {
            (_d = (_c = this.entitySyncManager).handleSyncData) === null || _d === void 0 ? void 0 : _d.call(_c, peerId, decompressedData);
        }
        // 触发数据接收事件
        this.eventEmitter.emit('dataReceived', peerId, decompressedData);
    };
    /**
     * 尝试重连
     */
    EnhancedNetworkSystem.prototype.tryReconnect = function () {
        var _this = this;
        if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
            Debug_1.Debug.warn('EnhancedNetworkSystem', '已达到最大重连尝试次数');
            return;
        }
        if (this.reconnectTimerId !== null) {
            return;
        }
        this.reconnectAttempts++;
        Debug_1.Debug.log('EnhancedNetworkSystem', "\u5C1D\u8BD5\u91CD\u8FDE (".concat(this.reconnectAttempts, "/").concat(this.options.maxReconnectAttempts, ")"));
        this.reconnectTimerId = window.setTimeout(function () {
            var _a, _b;
            _this.reconnectTimerId = null;
            if (_this.networkManager) {
                (_b = (_a = _this.networkManager).reconnect) === null || _b === void 0 ? void 0 : _b.call(_a);
            }
        }, this.options.reconnectInterval);
    };
    /**
     * 系统更新
     * @param deltaTime 时间增量
     */
    EnhancedNetworkSystem.prototype.update = function (deltaTime) {
        var _a, _b, _c, _d, _e, _f;
        if (!this.options.enabled || this.state === types_1.NetworkState.DISCONNECTED) {
            return;
        }
        // 更新网络质量监控器
        if (this.networkQualityMonitor) {
            (_b = (_a = this.networkQualityMonitor).update) === null || _b === void 0 ? void 0 : _b.call(_a, deltaTime);
        }
        // 更新实体同步管理器
        if (this.entitySyncManager) {
            (_d = (_c = this.entitySyncManager).update) === null || _d === void 0 ? void 0 : _d.call(_c, deltaTime);
        }
        // 更新网络事件调度器
        if (this.eventDispatcher) {
            (_f = (_e = this.eventDispatcher).update) === null || _f === void 0 ? void 0 : _f.call(_e, deltaTime);
        }
    };
    /**
     * 连接到服务器
     * @param serverUrl 服务器URL
     */
    EnhancedNetworkSystem.prototype.connect = function (serverUrl) {
        var _a, _b;
        if (!this.networkManager) {
            Debug_1.Debug.error('EnhancedNetworkSystem', '网络管理器未初始化');
            return;
        }
        if (this.state === types_1.NetworkState.CONNECTING || this.state === types_1.NetworkState.CONNECTED) {
            Debug_1.Debug.warn('EnhancedNetworkSystem', '已经在连接或已连接');
            return;
        }
        this.state = types_1.NetworkState.CONNECTING;
        Debug_1.Debug.log('EnhancedNetworkSystem', "\u6B63\u5728\u8FDE\u63A5\u5230\u670D\u52A1\u5668: ".concat(serverUrl));
        (_b = (_a = this.networkManager).connect) === null || _b === void 0 ? void 0 : _b.call(_a, serverUrl);
    };
    /**
     * 断开连接
     */
    EnhancedNetworkSystem.prototype.disconnect = function () {
        var _a, _b;
        if (!this.networkManager) {
            return;
        }
        if (this.state === types_1.NetworkState.DISCONNECTED) {
            return;
        }
        Debug_1.Debug.log('EnhancedNetworkSystem', '正在断开连接');
        (_b = (_a = this.networkManager).disconnect) === null || _b === void 0 ? void 0 : _b.call(_a);
        this.state = types_1.NetworkState.DISCONNECTED;
        // 停止重连
        if (this.reconnectTimerId !== null) {
            clearTimeout(this.reconnectTimerId);
            this.reconnectTimerId = null;
        }
    };
    /**
     * 发送数据到所有对等点
     * @param data 数据
     * @param compress 是否压缩
     */
    EnhancedNetworkSystem.prototype.sendToAll = function (data, compress) {
        var _a, _b, _c, _d;
        if (compress === void 0) { compress = this.options.enableCompression; }
        if (!this.networkManager || this.state !== types_1.NetworkState.CONNECTED) {
            return;
        }
        var processedData = data;
        // 压缩数据
        if (compress && this.dataCompressor) {
            try {
                processedData = this.dataCompressor.compress(data);
            }
            catch (error) {
                Debug_1.Debug.error('EnhancedNetworkSystem', "\u538B\u7F29\u6570\u636E\u5931\u8D25: ".concat(error));
                return;
            }
        }
        // 记录带宽使用
        if (this.bandwidthController) {
            var dataSize = typeof processedData === 'string' ? processedData.length : JSON.stringify(processedData).length;
            (_b = (_a = this.bandwidthController).recordUpload) === null || _b === void 0 ? void 0 : _b.call(_a, dataSize);
        }
        (_d = (_c = this.networkManager).sendToAll) === null || _d === void 0 ? void 0 : _d.call(_c, processedData);
    };
    /**
     * 发送数据到特定对等点
     * @param peerId 对等ID
     * @param data 数据
     * @param compress 是否压缩
     */
    EnhancedNetworkSystem.prototype.sendToPeer = function (peerId, data, compress) {
        var _a, _b, _c, _d;
        if (compress === void 0) { compress = this.options.enableCompression; }
        return __awaiter(this, void 0, void 0, function () {
            var processedData, result, error_1, dataSize;
            return __generator(this, function (_e) {
                switch (_e.label) {
                    case 0:
                        if (!this.networkManager || this.state !== types_1.NetworkState.CONNECTED) {
                            return [2 /*return*/];
                        }
                        processedData = data;
                        if (!(compress && this.dataCompressor)) return [3 /*break*/, 4];
                        _e.label = 1;
                    case 1:
                        _e.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, this.dataCompressor.compress(data)];
                    case 2:
                        result = _e.sent();
                        processedData = result.data;
                        return [3 /*break*/, 4];
                    case 3:
                        error_1 = _e.sent();
                        Debug_1.Debug.error('EnhancedNetworkSystem', "\u538B\u7F29\u6570\u636E\u5931\u8D25: ".concat(error_1));
                        return [2 /*return*/];
                    case 4:
                        // 记录带宽使用
                        if (this.bandwidthController) {
                            dataSize = typeof processedData === 'string' ? processedData.length : JSON.stringify(processedData).length;
                            (_b = (_a = this.bandwidthController).recordUpload) === null || _b === void 0 ? void 0 : _b.call(_a, dataSize);
                        }
                        (_d = (_c = this.networkManager).sendToPeer) === null || _d === void 0 ? void 0 : _d.call(_c, peerId, processedData);
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 注册网络实体
     * @param entity 实体
     * @param entityType 实体类型
     * @param syncMode 同步模式
     */
    EnhancedNetworkSystem.prototype.registerNetworkEntity = function (entity, entityType, syncMode) {
        var _a, _b;
        if (entityType === void 0) { entityType = NetworkEntityType.DYNAMIC; }
        if (syncMode === void 0) { syncMode = NetworkEntitySyncMode.FULL; }
        if (!this.entitySyncManager) {
            Debug_1.Debug.error('EnhancedNetworkSystem', '实体同步管理器未初始化');
            return;
        }
        // 添加网络实体组件
        if (!entity.hasComponent('NetworkEntity')) {
            entity.addComponent(new NetworkEntityComponent_1.NetworkEntityComponent({
                entityId: entity.id,
                ownerId: this.localUserId || 'unknown'
            }));
        }
        // 注册到同步管理器
        (_b = (_a = this.entitySyncManager).registerEntity) === null || _b === void 0 ? void 0 : _b.call(_a, entity);
        // 添加到空间分区
        if (this.spatialPartitioning && entity.hasComponent('Transform')) {
            this.spatialPartitioning.addEntity(entity.id, entity);
        }
        // 添加到映射表
        this.networkEntities.set(entity.id, entity);
        // 使用参数避免未使用警告
        Debug_1.Debug.log('EnhancedNetworkSystem', "\u6CE8\u518C\u7F51\u7EDC\u5B9E\u4F53: ".concat(entity.id, ", \u7C7B\u578B: ").concat(entityType, ", \u540C\u6B65\u6A21\u5F0F: ").concat(syncMode));
    };
    /**
     * 注销网络实体
     * @param entityId 实体ID
     */
    EnhancedNetworkSystem.prototype.unregisterNetworkEntity = function (entityId) {
        var _a, _b;
        if (!this.entitySyncManager) {
            return;
        }
        // 从同步管理器注销
        (_b = (_a = this.entitySyncManager).unregisterEntity) === null || _b === void 0 ? void 0 : _b.call(_a, entityId);
        // 从空间分区移除
        if (this.spatialPartitioning) {
            this.spatialPartitioning.removeEntity(entityId);
        }
        // 从映射表移除
        this.networkEntities.delete(entityId);
    };
    /**
     * 获取网络实体
     * @param entityId 实体ID
     * @returns 实体
     */
    EnhancedNetworkSystem.prototype.getNetworkEntity = function (entityId) {
        return this.networkEntities.get(entityId);
    };
    /**
     * 获取所有网络实体
     * @returns 实体映射表
     */
    EnhancedNetworkSystem.prototype.getAllNetworkEntities = function () {
        return new Map(this.networkEntities);
    };
    /**
     * 查询区域内的网络实体
     * @param minX 最小X坐标
     * @param minZ 最小Z坐标
     * @param maxX 最大X坐标
     * @param maxZ 最大Z坐标
     * @returns 实体映射表
     */
    EnhancedNetworkSystem.prototype.queryNetworkEntitiesInRegion = function (minX, minZ, maxX, maxZ) {
        if (!this.spatialPartitioning) {
            return new Map();
        }
        return this.spatialPartitioning.queryRegion(minX, minZ, maxX, maxZ);
    };
    /**
     * 查询半径内的网络实体
     * @param x 中心X坐标
     * @param z 中心Z坐标
     * @param radius 半径
     * @returns 实体映射表
     */
    EnhancedNetworkSystem.prototype.queryNetworkEntitiesInRadius = function (x, z, radius) {
        if (!this.spatialPartitioning) {
            return new Map();
        }
        return this.spatialPartitioning.queryRadius(x, z, radius);
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param listener 监听器
     */
    EnhancedNetworkSystem.prototype.addEventListener = function (event, listener) {
        this.eventEmitter.on(event, listener);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param listener 监听器
     */
    EnhancedNetworkSystem.prototype.removeEventListener = function (event, listener) {
        this.eventEmitter.off(event, listener);
    };
    /**
     * 获取网络状态
     * @returns 网络状态
     */
    EnhancedNetworkSystem.prototype.getState = function () {
        return this.state;
    };
    /**
     * 获取本地用户ID
     * @returns 本地用户ID
     */
    EnhancedNetworkSystem.prototype.getLocalUserId = function () {
        return this.localUserId;
    };
    /**
     * 获取网络质量
     * @returns 网络质量数据
     */
    EnhancedNetworkSystem.prototype.getNetworkQuality = function () {
        var _a, _b;
        if (!this.networkQualityMonitor) {
            return null;
        }
        return ((_b = (_a = this.networkQualityMonitor).getLatestQuality) === null || _b === void 0 ? void 0 : _b.call(_a)) || null;
    };
    /**
     * 获取服务发现客户端
     * @returns 服务发现客户端
     */
    EnhancedNetworkSystem.prototype.getServiceDiscoveryClient = function () {
        return this.serviceDiscoveryClient;
    };
    /**
     * 获取带宽使用
     * @returns 带宽使用数据
     */
    EnhancedNetworkSystem.prototype.getBandwidthUsage = function () {
        if (!this.bandwidthController) {
            return null;
        }
        return this.bandwidthController.getBandwidthUsage();
    };
    /**
     * 销毁系统
     */
    EnhancedNetworkSystem.prototype.dispose = function () {
        // 停止同步定时器
        this.stopSyncTimer();
        // 停止重连定时器
        if (this.reconnectTimerId !== null) {
            clearTimeout(this.reconnectTimerId);
            this.reconnectTimerId = null;
        }
        // 断开连接
        this.disconnect();
        // 销毁各个管理器
        if (this.networkManager) {
            this.networkManager.dispose();
        }
        if (this.mediaStreamManager) {
            this.mediaStreamManager.dispose();
        }
        if (this.entitySyncManager) {
            this.entitySyncManager.dispose();
        }
        if (this.userSessionManager) {
            this.userSessionManager.dispose();
        }
        if (this.eventDispatcher) {
            this.eventDispatcher.dispose();
        }
        if (this.networkQualityMonitor) {
            this.networkQualityMonitor.dispose();
        }
        if (this.bandwidthController) {
            this.bandwidthController.dispose();
        }
        if (this.networkAdaptiveController) {
            this.networkAdaptiveController.dispose();
        }
        if (this.spatialPartitioning) {
            this.spatialPartitioning.clear();
        }
        // 清空实体映射表
        this.networkEntities.clear();
        // 移除所有事件监听器
        this.eventEmitter.removeAllListeners();
        Debug_1.Debug.log('EnhancedNetworkSystem', '网络系统已销毁');
    };
    return EnhancedNetworkSystem;
}(System_1.System));
exports.EnhancedNetworkSystem = EnhancedNetworkSystem;

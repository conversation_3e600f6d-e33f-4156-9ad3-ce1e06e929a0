"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkTracer = void 0;
/**
 * 网络路由跟踪器
 * 用于跟踪网络路由路径和分析网络延迟
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var Debug_1 = require("../utils/Debug");
/**
 * 网络路由跟踪器
 * 用于跟踪网络路由路径和分析网络延迟
 */
var NetworkTracer = /** @class */ (function (_super) {
    __extends(NetworkTracer, _super);
    /**
     * 创建网络路由跟踪器
     * @param config 配置
     */
    function NetworkTracer(config) {
        var _this = _super.call(this) || this;
        /** 当前跟踪结果 */
        _this.currentResult = {};
        /** 跟踪是否正在进行 */
        _this.tracing = false;
        /** 当前跳数 */
        _this.currentHop = 0;
        /** 跟踪超时定时器ID */
        _this.timeoutId = null;
        // 默认配置
        _this.config = {
            targetHost: config.targetHost,
            maxHops: config.maxHops || 30,
            hopTimeout: config.hopTimeout || 3000,
            hopRetries: config.hopRetries || 3,
            detailedLogging: config.detailedLogging || false,
            resolveHostnames: config.resolveHostnames !== undefined ? config.resolveHostnames : true,
            geoLocation: config.geoLocation !== undefined ? config.geoLocation : true,
            analyzeRouteQuality: config.analyzeRouteQuality !== undefined ? config.analyzeRouteQuality : true,
            detectBottlenecks: config.detectBottlenecks !== undefined ? config.detectBottlenecks : true,
            useIcmp: config.useIcmp !== undefined ? config.useIcmp : true,
            useUdp: config.useUdp !== undefined ? config.useUdp : false,
            useTcp: config.useTcp !== undefined ? config.useTcp : false,
        };
        if (_this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkTracer', '网络路由跟踪器已创建');
        }
        return _this;
    }
    /**
     * 开始跟踪
     * @returns 跟踪结果Promise
     */
    NetworkTracer.prototype.startTrace = function () {
        return __awaiter(this, void 0, void 0, function () {
            var error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.tracing) {
                            throw new Error('跟踪已在进行中');
                        }
                        this.tracing = true;
                        this.resetTraceResult();
                        // 记录跟踪开始时间
                        this.currentResult.startTime = Date.now();
                        this.currentResult.targetHost = this.config.targetHost;
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 4, , 5]);
                        // 发送跟踪开始事件
                        this.emit('traceStart', {
                            targetHost: this.config.targetHost,
                            time: this.currentResult.startTime
                        });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('NetworkTracer', "\u5F00\u59CB\u8DDF\u8E2A\u8DEF\u7531\u5230 ".concat(this.config.targetHost));
                        }
                        // 解析目标主机IP
                        return [4 /*yield*/, this.resolveTargetIp()];
                    case 2:
                        // 解析目标主机IP
                        _a.sent();
                        // 开始跟踪路由
                        return [4 /*yield*/, this.traceRoute()];
                    case 3:
                        // 开始跟踪路由
                        _a.sent();
                        // 分析路由质量
                        if (this.config.analyzeRouteQuality) {
                            this.analyzeRouteQuality();
                        }
                        // 检测瓶颈
                        if (this.config.detectBottlenecks) {
                            this.detectBottlenecks();
                        }
                        // 记录跟踪结束时间
                        this.currentResult.endTime = Date.now();
                        this.currentResult.duration = this.currentResult.endTime - this.currentResult.startTime;
                        this.currentResult.success = true;
                        // 发送跟踪完成事件
                        this.emit('traceComplete', this.getTraceResult());
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('NetworkTracer', "\u8DEF\u7531\u8DDF\u8E2A\u5B8C\u6210: ".concat(this.currentResult.hops, " \u8DF3, \u7AEF\u5230\u7AEF\u5EF6\u8FDF=").concat(this.currentResult.endToEndLatency, "ms"));
                        }
                        this.tracing = false;
                        return [2 /*return*/, this.getTraceResult()];
                    case 4:
                        error_1 = _a.sent();
                        // 记录跟踪结束时间
                        this.currentResult.endTime = Date.now();
                        this.currentResult.duration = this.currentResult.endTime - this.currentResult.startTime;
                        this.currentResult.success = false;
                        this.currentResult.error = error_1 instanceof Error ? error_1.message : String(error_1);
                        // 发送跟踪失败事件
                        this.emit('traceError', {
                            error: this.currentResult.error,
                            result: this.getTraceResult()
                        });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('NetworkTracer', "\u8DEF\u7531\u8DDF\u8E2A\u5931\u8D25: ".concat(this.currentResult.error));
                        }
                        this.tracing = false;
                        return [2 /*return*/, this.getTraceResult()];
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 取消跟踪
     */
    NetworkTracer.prototype.cancelTrace = function () {
        if (!this.tracing) {
            return;
        }
        // 清除超时
        if (this.timeoutId !== null) {
            clearTimeout(this.timeoutId);
            this.timeoutId = null;
        }
        // 记录跟踪结束时间
        this.currentResult.endTime = Date.now();
        this.currentResult.duration = this.currentResult.endTime - this.currentResult.startTime;
        this.currentResult.success = false;
        this.currentResult.error = '跟踪被取消';
        // 发送跟踪取消事件
        this.emit('traceCancel', this.getTraceResult());
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkTracer', '路由跟踪被取消');
        }
        this.tracing = false;
    };
    /**
     * 重置跟踪结果
     */
    NetworkTracer.prototype.resetTraceResult = function () {
        this.currentResult = {
            targetHost: this.config.targetHost,
            success: false,
            startTime: 0,
            endTime: 0,
            duration: 0,
            hops: 0,
            nodes: [],
            endToEndLatency: 0,
        };
        this.currentHop = 0;
    };
    /**
     * 获取跟踪结果
     * @returns 跟踪结果
     */
    NetworkTracer.prototype.getTraceResult = function () {
        return this.currentResult;
    };
    /**
     * 解析目标主机IP
     */
    NetworkTracer.prototype.resolveTargetIp = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('NetworkTracer', "\u89E3\u6790\u76EE\u6807\u4E3B\u673A ".concat(this.config.targetHost, " \u7684IP\u5730\u5740"));
                        }
                        // 模拟DNS解析
                        // 在实际应用中，这里应该使用DNS解析API
                        return [4 /*yield*/, new Promise(function (resolve) {
                                setTimeout(function () {
                                    // 模拟IP地址
                                    _this.currentResult.targetIp = '***********';
                                    if (_this.config.detailedLogging) {
                                        Debug_1.Debug.log('NetworkTracer', "\u76EE\u6807\u4E3B\u673A ".concat(_this.config.targetHost, " \u7684IP\u5730\u5740\u4E3A ").concat(_this.currentResult.targetIp));
                                    }
                                    resolve();
                                }, 100); // 模拟100ms的DNS解析时间
                            })];
                    case 1:
                        // 模拟DNS解析
                        // 在实际应用中，这里应该使用DNS解析API
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 跟踪路由
     */
    NetworkTracer.prototype.traceRoute = function () {
        return __awaiter(this, void 0, void 0, function () {
            var nodes, reachedTarget, hop, node, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('NetworkTracer', '开始跟踪路由');
                        }
                        nodes = [];
                        reachedTarget = false;
                        hop = 1;
                        _a.label = 1;
                    case 1:
                        if (!(hop <= this.config.maxHops && !reachedTarget)) return [3 /*break*/, 6];
                        this.currentHop = hop;
                        // 发送跳跟踪开始事件
                        this.emit('hopTraceStart', { hop: hop });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('NetworkTracer', "\u8DDF\u8E2A\u7B2C ".concat(hop, " \u8DF3"));
                        }
                        _a.label = 2;
                    case 2:
                        _a.trys.push([2, 4, , 5]);
                        return [4 /*yield*/, this.traceHop(hop)];
                    case 3:
                        node = _a.sent();
                        nodes.push(node);
                        // 发送跳跟踪完成事件
                        this.emit('hopTraceComplete', {
                            hop: hop,
                            node: node
                        });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('NetworkTracer', "\u7B2C ".concat(hop, " \u8DF3: ").concat(node.ip, ", \u54CD\u5E94\u65F6\u95F4=").concat(node.responseTime, "ms, \u4E22\u5305\u7387=").concat(node.packetLoss * 100, "%"));
                        }
                        // 检查是否到达目标
                        if (node.ip === this.currentResult.targetIp) {
                            reachedTarget = true;
                            if (this.config.detailedLogging) {
                                Debug_1.Debug.log('NetworkTracer', "\u5DF2\u5230\u8FBE\u76EE\u6807\u4E3B\u673A ".concat(this.config.targetHost));
                            }
                        }
                        return [3 /*break*/, 5];
                    case 4:
                        error_2 = _a.sent();
                        // 发送跳跟踪失败事件
                        this.emit('hopTraceError', {
                            hop: hop,
                            error: error_2 instanceof Error ? error_2.message : String(error_2)
                        });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('NetworkTracer', "\u7B2C ".concat(hop, " \u8DF3\u8DDF\u8E2A\u5931\u8D25: ").concat(error_2));
                        }
                        // 添加超时节点
                        nodes.push({
                            hop: hop,
                            ip: '*',
                            responseTime: this.config.hopTimeout,
                            packetLoss: 1.0,
                        });
                        return [3 /*break*/, 5];
                    case 5:
                        hop++;
                        return [3 /*break*/, 1];
                    case 6:
                        // 更新跟踪结果
                        this.currentResult.nodes = nodes;
                        this.currentResult.hops = nodes.length;
                        // 计算端到端延迟
                        if (reachedTarget && nodes.length > 0) {
                            this.currentResult.endToEndLatency = nodes[nodes.length - 1].responseTime;
                        }
                        else {
                            this.currentResult.endToEndLatency = 0;
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 跟踪单个跳
     * @param hop 跳数
     * @returns 路由节点
     */
    NetworkTracer.prototype.traceHop = function (hop) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                // 模拟跟踪单个跳
                // 在实际应用中，这里应该发送TTL为hop的数据包
                return [2 /*return*/, new Promise(function (resolve, reject) {
                        setTimeout(function () {
                            // 模拟节点数据
                            var node = {
                                hop: hop,
                                ip: _this.generateIpForHop(hop),
                                responseTime: _this.generateResponseTimeForHop(hop),
                                packetLoss: _this.generatePacketLossForHop(hop),
                            };
                            // 解析主机名
                            if (_this.config.resolveHostnames) {
                                node.hostname = _this.generateHostnameForIp(node.ip);
                            }
                            // 获取地理位置
                            if (_this.config.geoLocation) {
                                node.location = _this.generateLocationForIp(node.ip);
                            }
                            resolve(node);
                        }, 50 + Math.random() * 100); // 模拟50-150ms的跟踪时间
                    })];
            });
        });
    };
    /**
     * 分析路由质量
     */
    NetworkTracer.prototype.analyzeRouteQuality = function () {
        var _this = this;
        if (!this.currentResult.nodes || this.currentResult.nodes.length === 0) {
            return;
        }
        var nodes = this.currentResult.nodes;
        // 计算平均每跳延迟
        var validResponseTimes = nodes
            .filter(function (node) { return node.responseTime < _this.config.hopTimeout; })
            .map(function (node) { return node.responseTime; });
        var avgHopLatency = validResponseTimes.length > 0
            ? validResponseTimes.reduce(function (sum, time) { return sum + time; }, 0) / validResponseTimes.length
            : 0;
        // 找出最大跳延迟
        var maxHopLatency = Math.max.apply(Math, validResponseTimes);
        var maxHopLatencyIndex = nodes.findIndex(function (node) { return node.responseTime === maxHopLatency; });
        // 计算平均丢包率
        var avgPacketLoss = nodes.reduce(function (sum, node) { return sum + node.packetLoss; }, 0) / nodes.length;
        // 找出最大丢包率
        var maxPacketLoss = Math.max.apply(Math, nodes.map(function (node) { return node.packetLoss; }));
        var maxPacketLossIndex = nodes.findIndex(function (node) { return node.packetLoss === maxPacketLoss; });
        // 计算跨国跳数和跨ISP跳数
        var internationalHops = 0;
        var crossIspHops = 0;
        var prevCountry = '';
        var prevIsp = '';
        for (var _i = 0, nodes_1 = nodes; _i < nodes_1.length; _i++) {
            var node = nodes_1[_i];
            if (node.location) {
                // 检查跨国
                if (prevCountry && node.location.country && prevCountry !== node.location.country) {
                    internationalHops++;
                }
                prevCountry = node.location.country || '';
                // 检查跨ISP
                if (prevIsp && node.location.isp && prevIsp !== node.location.isp) {
                    crossIspHops++;
                }
                prevIsp = node.location.isp || '';
            }
        }
        // 计算路由质量评分（0-100）
        // 基于延迟、丢包率、跨国跳数和跨ISP跳数
        var latencyScore = Math.max(0, 100 - (avgHopLatency / 10));
        var packetLossScore = Math.max(0, 100 - (avgPacketLoss * 100 * 2));
        var hopCountScore = Math.max(0, 100 - (nodes.length * 2));
        var internationalHopsScore = Math.max(0, 100 - (internationalHops * 10));
        var crossIspHopsScore = Math.max(0, 100 - (crossIspHops * 5));
        var routeQualityScore = Math.round(latencyScore * 0.3 +
            packetLossScore * 0.3 +
            hopCountScore * 0.2 +
            internationalHopsScore * 0.1 +
            crossIspHopsScore * 0.1);
        // 更新路由质量评分
        this.currentResult.routeQualityScore = routeQualityScore;
        // 更新路由分析
        this.currentResult.routeAnalysis = {
            avgHopLatency: avgHopLatency,
            maxHopLatency: maxHopLatency,
            maxHopLatencyIndex: maxHopLatencyIndex,
            avgPacketLoss: avgPacketLoss,
            maxPacketLoss: maxPacketLoss,
            maxPacketLossIndex: maxPacketLossIndex,
            internationalHops: internationalHops,
            crossIspHops: crossIspHops,
        };
        // 更新节点质量评分
        for (var _a = 0, nodes_2 = nodes; _a < nodes_2.length; _a++) {
            var node = nodes_2[_a];
            // 计算节点质量评分（0-100）
            var responseTimeScore = Math.max(0, 100 - (node.responseTime / 5));
            var packetLossScore_1 = Math.max(0, 100 - (node.packetLoss * 100 * 2));
            node.qualityScore = Math.round(responseTimeScore * 0.7 + packetLossScore_1 * 0.3);
        }
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkTracer', "\u8DEF\u7531\u8D28\u91CF\u5206\u6790\u5B8C\u6210: \u8BC4\u5206=".concat(routeQualityScore, ", \u5E73\u5747\u6BCF\u8DF3\u5EF6\u8FDF=").concat(avgHopLatency, "ms, \u5E73\u5747\u4E22\u5305\u7387=").concat(avgPacketLoss * 100, "%"));
        }
    };
    /**
     * 检测瓶颈
     */
    NetworkTracer.prototype.detectBottlenecks = function () {
        if (!this.currentResult.nodes || this.currentResult.nodes.length === 0) {
            return;
        }
        var nodes = this.currentResult.nodes;
        var bottleneckNodeIndices = [];
        // 检测延迟瓶颈
        for (var i = 1; i < nodes.length; i++) {
            var prevNode = nodes[i - 1];
            var currNode = nodes[i];
            // 如果当前节点的响应时间比前一个节点高出很多，则可能是瓶颈
            if (currNode.responseTime > prevNode.responseTime * 2 && currNode.responseTime > 100) {
                currNode.isBottleneck = true;
                currNode.bottleneckReason = '延迟突增';
                bottleneckNodeIndices.push(i);
            }
            // 如果当前节点的丢包率很高，则可能是瓶颈
            if (currNode.packetLoss > 0.1) {
                currNode.isBottleneck = true;
                currNode.bottleneckReason = currNode.bottleneckReason
                    ? "".concat(currNode.bottleneckReason, ", \u9AD8\u4E22\u5305\u7387")
                    : '高丢包率';
                if (!bottleneckNodeIndices.includes(i)) {
                    bottleneckNodeIndices.push(i);
                }
            }
        }
        // 更新瓶颈节点索引
        this.currentResult.bottleneckNodeIndices = bottleneckNodeIndices;
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkTracer', "\u74F6\u9888\u68C0\u6D4B\u5B8C\u6210: \u53D1\u73B0 ".concat(bottleneckNodeIndices.length, " \u4E2A\u74F6\u9888\u8282\u70B9"));
        }
    };
    /**
     * 为跳生成IP地址
     * @param hop 跳数
     * @returns IP地址
     */
    NetworkTracer.prototype.generateIpForHop = function (hop) {
        // 模拟IP地址生成
        if (hop === this.config.maxHops) {
            return this.currentResult.targetIp || '***********';
        }
        switch (hop) {
            case 1: return '***********'; // 本地网关
            case 2: return '********'; // ISP网关
            case 3: return '**********'; // ISP核心路由器
            case 4: return '**********'; // ISP区域路由器
            case 5: return '**********'; // ISP边界路由器
            case 6: return '************'; // 互联网交换中心
            case 7: return '************'; // 骨干网路由器1
            case 8: return '************'; // 骨干网路由器2
            case 9: return '***********'; // 目标ISP边界路由器
            case 10: return '***********'; // 目标ISP核心路由器
            default: return "203.0.113.".concat(hop - 7); // 目标网络路由器
        }
    };
    /**
     * 为跳生成响应时间
     * @param hop 跳数
     * @returns 响应时间（毫秒）
     */
    NetworkTracer.prototype.generateResponseTimeForHop = function (hop) {
        // 模拟响应时间生成
        // 通常响应时间会随着跳数增加而增加
        var baseTime = hop * 10; // 每跳增加10ms
        var jitter = Math.random() * 20 - 10; // -10ms到+10ms的抖动
        // 模拟某些跳的延迟突增
        var delaySpike = hop === 5 ? 50 : 0; // 第5跳延迟突增50ms
        return Math.max(1, baseTime + jitter + delaySpike);
    };
    /**
     * 为跳生成丢包率
     * @param hop 跳数
     * @returns 丢包率（0-1）
     */
    NetworkTracer.prototype.generatePacketLossForHop = function (hop) {
        // 模拟丢包率生成
        // 通常丢包率较低，但某些跳可能较高
        var baseLoss = 0.01; // 基础丢包率1%
        var randomLoss = Math.random() * 0.02; // 0-2%的随机丢包
        // 模拟某些跳的丢包率突增
        var lossSpike = hop === 7 ? 0.1 : 0; // 第7跳丢包率突增10%
        return Math.min(1, Math.max(0, baseLoss + randomLoss + lossSpike));
    };
    /**
     * 为IP生成主机名
     * @param ip IP地址
     * @returns 主机名
     */
    NetworkTracer.prototype.generateHostnameForIp = function (ip) {
        // 模拟主机名生成
        switch (ip) {
            case '***********': return 'router.home';
            case '********': return 'gateway.isp.com';
            case '**********': return 'core1.isp.com';
            case '**********': return 'region1.isp.com';
            case '**********': return 'border1.isp.com';
            case '************': return 'ix1.internet.com';
            case '************': return 'backbone1.internet.com';
            case '************': return 'backbone2.internet.com';
            case '***********': return this.config.targetHost;
            case '***********': return 'border1.target-isp.com';
            case '***********': return 'core1.target-isp.com';
            default: return "router".concat(Math.floor(Math.random() * 100), ".target-isp.com");
        }
    };
    /**
     * 为IP生成地理位置
     * @param ip IP地址
     * @returns 地理位置
     */
    NetworkTracer.prototype.generateLocationForIp = function (ip) {
        // 模拟地理位置生成
        if (ip.startsWith('192.168.') || ip.startsWith('10.')) {
            return {
                country: '中国',
                city: '本地网络',
                isp: '本地网络',
            };
        }
        else if (ip.startsWith('172.16.')) {
            return {
                country: '中国',
                city: '北京',
                longitude: 116.4074,
                latitude: 39.9042,
                isp: '中国电信',
            };
        }
        else if (ip.startsWith('198.51.100.')) {
            return {
                country: '美国',
                city: '旧金山',
                longitude: -122.4194,
                latitude: 37.7749,
                isp: '国际骨干网',
            };
        }
        else {
            return {
                country: '美国',
                city: '纽约',
                longitude: -74.0060,
                latitude: 40.7128,
                isp: 'Amazon AWS',
            };
        }
    };
    /**
     * 销毁跟踪器
     */
    NetworkTracer.prototype.dispose = function () {
        if (this.tracing) {
            this.cancelTrace();
        }
        this.removeAllListeners();
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkTracer', '网络路由跟踪器已销毁');
        }
    };
    return NetworkTracer;
}(EventEmitter_1.EventEmitter));
exports.NetworkTracer = NetworkTracer;

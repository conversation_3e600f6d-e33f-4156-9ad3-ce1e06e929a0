"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FixedConstraint = void 0;
var CANNON = require("cannon-es");
var PhysicsConstraint_1 = require("./PhysicsConstraint");
/**
 * 固定约束
 */
var FixedConstraint = exports.FixedConstraint = /** @class */ (function (_super) {
    __extends(FixedConstraint, _super);
    /**
     * 创建固定约束
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    function FixedConstraint(targetEntity, options) {
        if (targetEntity === void 0) { targetEntity = null; }
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, PhysicsConstraint_1.ConstraintType.FIXED, targetEntity, options) || this;
        // 设置最大力
        _this.maxForce = options.maxForce !== undefined ? options.maxForce : 1e6;
        return _this;
    }
    /**
     * 创建约束
     */
    FixedConstraint.prototype.createConstraint = function () {
        // 获取源物体和目标物体
        var bodyA = this.getSourceBody();
        var bodyB = this.getTargetBody();
        // 如果没有源物体或目标物体，则无法创建约束
        if (!bodyA || !bodyB) {
            console.warn('无法创建固定约束：缺少源物体或目标物体');
            return;
        }
        // 计算相对位置和旋转
        var relativePosition = new CANNON.Vec3();
        var relativeQuaternion = new CANNON.Quaternion();
        // 计算相对位置
        bodyB.position.vsub(bodyA.position, relativePosition);
        bodyA.quaternion.inverse().mult(relativePosition, relativePosition);
        // 计算相对旋转
        bodyA.quaternion.inverse().mult(bodyB.quaternion, relativeQuaternion);
        // 创建固定约束
        // 注意：CANNON.js没有内置的固定约束，我们使用锁定约束实现
        this.constraint = new CANNON.LockConstraint(bodyA, bodyB, {
            maxForce: this.maxForce
        });
        // 设置是否允许连接的物体之间碰撞
        this.constraint.collideConnected = this.collideConnected;
    };
    /**
     * 设置最大力
     * @param maxForce 最大力
     */
    FixedConstraint.prototype.setMaxForce = function (maxForce) {
        this.maxForce = maxForce;
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取最大力
     * @returns 最大力
     */
    FixedConstraint.prototype.getMaxForce = function () {
        return this.maxForce;
    };
    /**
     * 重新创建约束
     */
    FixedConstraint.prototype.recreateConstraint = function () {
        if (this.initialized && this.constraint && this.world) {
            this.world.removeConstraint(this.constraint);
            this.constraint = null;
            this.initialized = false;
            this.initialize(this.world);
        }
    };
    /** 组件类型 */
    FixedConstraint.type = 'FixedConstraint';
    return FixedConstraint;
}(PhysicsConstraint_1.PhysicsConstraint));

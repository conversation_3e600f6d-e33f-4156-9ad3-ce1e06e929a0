"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MaterialOptimizer = void 0;
/**
 * 材质优化器
 * 用于优化材质性能
 */
var THREE = require("three");
var DeviceCapabilities_1 = require("../../utils/DeviceCapabilities");
/**
 * 材质优化器类
 */
var MaterialOptimizer = /** @class */ (function () {
    /**
     * 创建材质优化器
     * @param options 材质优化器配置
     */
    function MaterialOptimizer(options) {
        if (options === void 0) { options = {}; }
        this.deviceCapabilities = options.deviceCapabilities || new DeviceCapabilities_1.DeviceCapabilities();
        this.enableTextureCompression = options.enableTextureCompression !== undefined ? options.enableTextureCompression : true;
        this.enableTextureSizeLimit = options.enableTextureSizeLimit !== undefined ? options.enableTextureSizeLimit : true;
        this.maxTextureSize = options.maxTextureSize || 2048;
        this.enableAnisotropy = options.enableAnisotropy !== undefined ? options.enableAnisotropy : true;
        this.maxAnisotropy = options.maxAnisotropy || 4;
        this.enableMipmap = options.enableMipmap !== undefined ? options.enableMipmap : true;
        this.enableShaderOptimization = options.enableShaderOptimization !== undefined ? options.enableShaderOptimization : true;
    }
    /**
     * 优化材质
     * @param material 材质
     * @returns 优化后的材质
     */
    MaterialOptimizer.prototype.optimizeMaterial = function (material) {
        // 根据设备性能级别优化材质
        var performanceLevel = this.deviceCapabilities.getPerformanceLevel();
        switch (performanceLevel) {
            case DeviceCapabilities_1.DevicePerformanceLevel.LOW:
                return this.optimizeForLowPerformance(material);
            case DeviceCapabilities_1.DevicePerformanceLevel.MEDIUM:
                return this.optimizeForMediumPerformance(material);
            case DeviceCapabilities_1.DevicePerformanceLevel.HIGH:
                return this.optimizeForHighPerformance(material);
            default:
                return material;
        }
    };
    /**
     * 为低性能设备优化材质
     * @param material 材质
     * @returns 优化后的材质
     */
    MaterialOptimizer.prototype.optimizeForLowPerformance = function (material) {
        // 禁用复杂特性
        material.fog = false;
        material.lights = false;
        // 优化纹理
        this.optimizeTextures(material, 1024, 1);
        // 优化着色器
        if (this.enableShaderOptimization) {
            this.optimizeShader(material, true);
        }
        return material;
    };
    /**
     * 为中等性能设备优化材质
     * @param material 材质
     * @returns 优化后的材质
     */
    MaterialOptimizer.prototype.optimizeForMediumPerformance = function (material) {
        // 优化纹理
        this.optimizeTextures(material, 2048, 2);
        // 优化着色器
        if (this.enableShaderOptimization) {
            this.optimizeShader(material, false);
        }
        return material;
    };
    /**
     * 为高性能设备优化材质
     * @param material 材质
     * @returns 优化后的材质
     */
    MaterialOptimizer.prototype.optimizeForHighPerformance = function (material) {
        // 优化纹理
        this.optimizeTextures(material, 4096, 8);
        return material;
    };
    /**
     * 优化纹理
     * @param material 材质
     * @param maxSize 最大纹理大小
     * @param anisotropyLevel 各向异性级别
     */
    MaterialOptimizer.prototype.optimizeTextures = function (material, maxSize, anisotropyLevel) {
        // 获取材质中的所有纹理
        var textures = this.getTexturesFromMaterial(material);
        // 优化每个纹理
        for (var _i = 0, textures_1 = textures; _i < textures_1.length; _i++) {
            var texture = textures_1[_i];
            this.optimizeTexture(texture, maxSize, anisotropyLevel);
        }
    };
    /**
     * 优化纹理
     * @param texture 纹理
     * @param maxSize 最大纹理大小
     * @param anisotropyLevel 各向异性级别
     */
    MaterialOptimizer.prototype.optimizeTexture = function (texture, maxSize, anisotropyLevel) {
        if (!texture)
            return;
        // 限制纹理大小
        if (this.enableTextureSizeLimit) {
            var size = Math.min(this.maxTextureSize, maxSize);
            if (texture.image && (texture.image.width > size || texture.image.height > size)) {
                // 在实际应用中，这里应该调整纹理大小
                // 但在Three.js中，我们通常在加载纹理前就处理好大小
                console.warn("\u7EB9\u7406\u5927\u5C0F\u8D85\u8FC7\u9650\u5236: ".concat(texture.image.width, "x").concat(texture.image.height, ", \u6700\u5927\u5141\u8BB8: ").concat(size, "x").concat(size));
            }
        }
        // 设置各向异性过滤
        if (this.enableAnisotropy && this.deviceCapabilities.isAnisotropySupported()) {
            texture.anisotropy = Math.min(this.deviceCapabilities.getMaxAnisotropy(), this.maxAnisotropy, anisotropyLevel);
        }
        else {
            texture.anisotropy = 1;
        }
        // 设置MIP映射
        if (this.enableMipmap) {
            texture.generateMipmaps = true;
            texture.minFilter = THREE.LinearMipmapLinearFilter;
        }
        else {
            texture.generateMipmaps = false;
            texture.minFilter = THREE.LinearFilter;
        }
        // 设置纹理包装模式
        texture.wrapS = THREE.ClampToEdgeWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;
        // 标记纹理需要更新
        texture.needsUpdate = true;
    };
    /**
     * 优化着色器
     * @param material 材质
     * @param simplify 是否简化
     */
    MaterialOptimizer.prototype.optimizeShader = function (material, simplify) {
        // 在实际应用中，这里应该修改着色器代码
        // 但在Three.js中，我们通常使用内置材质，很少直接修改着色器
        if (material instanceof THREE.ShaderMaterial || material instanceof THREE.RawShaderMaterial) {
            // 为着色器材质添加优化标记
            material.defines = material.defines || {};
            material.defines.USE_OPTIMIZED_SHADER = 1;
            if (simplify) {
                material.defines.USE_SIMPLIFIED_SHADER = 1;
            }
            // 标记材质需要更新
            material.needsUpdate = true;
        }
    };
    /**
     * 从材质中获取所有纹理
     * @param material 材质
     * @returns 纹理数组
     */
    MaterialOptimizer.prototype.getTexturesFromMaterial = function (material) {
        var textures = [];
        // 检查常见的纹理属性
        var textureProperties = [
            'map', 'alphaMap', 'aoMap', 'bumpMap', 'displacementMap',
            'emissiveMap', 'envMap', 'lightMap', 'metalnessMap',
            'normalMap', 'roughnessMap', 'specularMap', 'gradientMap',
            'matcap', 'clearcoatMap', 'clearcoatNormalMap', 'clearcoatRoughnessMap',
            'sheenColorMap', 'sheenRoughnessMap', 'transmissionMap', 'thicknessMap'
        ];
        // 遍历所有可能的纹理属性
        for (var _i = 0, textureProperties_1 = textureProperties; _i < textureProperties_1.length; _i++) {
            var prop = textureProperties_1[_i];
            if (prop in material && material[prop] instanceof THREE.Texture) {
                textures.push(material[prop]);
            }
        }
        return textures;
    };
    /**
     * 设置是否启用纹理压缩
     * @param enabled 是否启用
     */
    MaterialOptimizer.prototype.setEnableTextureCompression = function (enabled) {
        this.enableTextureCompression = enabled;
    };
    /**
     * 设置是否启用纹理大小限制
     * @param enabled 是否启用
     */
    MaterialOptimizer.prototype.setEnableTextureSizeLimit = function (enabled) {
        this.enableTextureSizeLimit = enabled;
    };
    /**
     * 设置最大纹理大小
     * @param size 最大纹理大小
     */
    MaterialOptimizer.prototype.setMaxTextureSize = function (size) {
        this.maxTextureSize = size;
    };
    /**
     * 设置是否启用各向异性过滤
     * @param enabled 是否启用
     */
    MaterialOptimizer.prototype.setEnableAnisotropy = function (enabled) {
        this.enableAnisotropy = enabled;
    };
    /**
     * 设置最大各向异性级别
     * @param level 最大各向异性级别
     */
    MaterialOptimizer.prototype.setMaxAnisotropy = function (level) {
        this.maxAnisotropy = level;
    };
    /**
     * 设置是否启用MIP映射
     * @param enabled 是否启用
     */
    MaterialOptimizer.prototype.setEnableMipmap = function (enabled) {
        this.enableMipmap = enabled;
    };
    /**
     * 设置是否启用着色器优化
     * @param enabled 是否启用
     */
    MaterialOptimizer.prototype.setEnableShaderOptimization = function (enabled) {
        this.enableShaderOptimization = enabled;
    };
    return MaterialOptimizer;
}());
exports.MaterialOptimizer = MaterialOptimizer;

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedPhysicsDebugger = void 0;
/**
 * 增强型物理调试器
 * 提供更多物理调试功能和性能监控
 */
var THREE = require("three");
var CANNON = require("cannon-es");
var PhysicsDebugger_1 = require("./PhysicsDebugger");
/**
 * 增强型物理调试器
 */
var EnhancedPhysicsDebugger = /** @class */ (function (_super) {
    __extends(EnhancedPhysicsDebugger, _super);
    /**
     * 创建增强型物理调试器
     * @param physicsSystem 物理系统
     * @param options 调试器选项
     */
    function EnhancedPhysicsDebugger(physicsSystem, options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, physicsSystem, options) || this;
        /** 速度向量映射 */
        _this.velocityArrows = new Map();
        /** 力向量映射 */
        _this.forceArrows = new Map();
        /** 碰撞法线向量 */
        _this.contactNormalArrows = [];
        /** 碰撞力向量 */
        _this.contactForceArrows = [];
        /** 摩擦力向量 */
        _this.frictionForceArrows = [];
        /** 质心网格映射 */
        _this.centerOfMassMeshes = new Map();
        /** 睡眠状态指示器映射 */
        _this.sleepStateMeshes = new Map();
        /** 性能统计数据 */
        _this.performanceStats = [];
        /** 性能统计面板 */
        _this.statsPanel = null;
        /** 性能监控开始时间 */
        _this.monitorStartTime = 0;
        /** 性能监控结束时间 */
        _this.monitorEndTime = 0;
        /** 上一帧时间 */
        _this.lastFrameTime = 0;
        /** 帧计数器 */
        _this.frameCount = 0;
        /** 帧率计算间隔 (ms) */
        _this.fpsInterval = 1000;
        /** 帧率计算时间戳 */
        _this.fpsTimestamp = 0;
        /** 当前帧率 */
        _this.currentFps = 0;
        // 设置增强选项
        _this.showVelocities = options.showVelocities !== undefined ? options.showVelocities : false;
        _this.showForces = options.showForces !== undefined ? options.showForces : false;
        _this.showCenterOfMass = options.showCenterOfMass !== undefined ? options.showCenterOfMass : false;
        _this.showSleepState = options.showSleepState !== undefined ? options.showSleepState : false;
        _this.showPerformanceStats = options.showPerformanceStats !== undefined ? options.showPerformanceStats : false;
        _this.showContactNormals = options.showContactNormals !== undefined ? options.showContactNormals : false;
        _this.showContactForces = options.showContactForces !== undefined ? options.showContactForces : false;
        _this.showFrictionForces = options.showFrictionForces !== undefined ? options.showFrictionForces : false;
        // 设置颜色
        _this.velocityColor = options.velocityColor instanceof THREE.Color
            ? options.velocityColor
            : new THREE.Color(options.velocityColor !== undefined ? options.velocityColor : 0x00ffff);
        _this.forceColor = options.forceColor instanceof THREE.Color
            ? options.forceColor
            : new THREE.Color(options.forceColor !== undefined ? options.forceColor : 0xff00ff);
        _this.centerOfMassColor = options.centerOfMassColor instanceof THREE.Color
            ? options.centerOfMassColor
            : new THREE.Color(options.centerOfMassColor !== undefined ? options.centerOfMassColor : 0xffff00);
        _this.sleepStateColor = options.sleepStateColor instanceof THREE.Color
            ? options.sleepStateColor
            : new THREE.Color(options.sleepStateColor !== undefined ? options.sleepStateColor : 0x888888);
        _this.contactNormalColor = options.contactNormalColor instanceof THREE.Color
            ? options.contactNormalColor
            : new THREE.Color(options.contactNormalColor !== undefined ? options.contactNormalColor : 0x00ff00);
        _this.contactForceColor = options.contactForceColor instanceof THREE.Color
            ? options.contactForceColor
            : new THREE.Color(options.contactForceColor !== undefined ? options.contactForceColor : 0xff0000);
        _this.frictionForceColor = options.frictionForceColor instanceof THREE.Color
            ? options.frictionForceColor
            : new THREE.Color(options.frictionForceColor !== undefined ? options.frictionForceColor : 0xffa500);
        // 设置向量缩放因子
        _this.vectorScale = options.vectorScale !== undefined ? options.vectorScale : 0.1;
        _this.contactForceScale = options.contactForceScale !== undefined ? options.contactForceScale : 0.01;
        // 初始化性能监控
        if (_this.showPerformanceStats) {
            _this.initPerformanceStats();
        }
        return _this;
    }
    /**
     * 初始化性能统计面板
     */
    EnhancedPhysicsDebugger.prototype.initPerformanceStats = function () {
        // 创建性能统计面板
        this.statsPanel = document.createElement('div');
        this.statsPanel.style.position = 'absolute';
        this.statsPanel.style.top = '10px';
        this.statsPanel.style.right = '10px';
        this.statsPanel.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        this.statsPanel.style.color = 'white';
        this.statsPanel.style.padding = '10px';
        this.statsPanel.style.borderRadius = '5px';
        this.statsPanel.style.fontFamily = 'monospace';
        this.statsPanel.style.fontSize = '12px';
        this.statsPanel.style.zIndex = '1000';
        document.body.appendChild(this.statsPanel);
    };
    /**
     * 更新性能统计
     */
    EnhancedPhysicsDebugger.prototype.updatePerformanceStats = function () {
        var _a, _b, _c, _d;
        if (!this.showPerformanceStats || !this.statsPanel)
            return;
        var world = this.getPhysicsWorld();
        // 计算帧率
        var now = performance.now();
        this.frameCount++;
        if (now - this.fpsTimestamp >= this.fpsInterval) {
            this.currentFps = Math.round((this.frameCount * 1000) / (now - this.fpsTimestamp));
            this.fpsTimestamp = now;
            this.frameCount = 0;
        }
        // 收集性能数据
        var stats = {
            updateTime: world.lastCallTime,
            collisionTime: ((_b = (_a = world.collisionMatrix) === null || _a === void 0 ? void 0 : _a.stats) === null || _b === void 0 ? void 0 : _b.broadphase) || 0,
            constraintTime: ((_d = (_c = world.solver) === null || _c === void 0 ? void 0 : _c.stats) === null || _d === void 0 ? void 0 : _d.solve) || 0,
            bodyCount: world.bodies.length,
            constraintCount: world.constraints.length,
            contactCount: world.contacts.length,
            fps: this.currentFps,
            timestamp: now
        };
        // 保存性能数据
        this.performanceStats.push(stats);
        if (this.performanceStats.length > 100) {
            this.performanceStats.shift();
        }
        // 更新性能统计面板
        this.statsPanel.innerHTML = "\n      <div>\u7269\u7406\u6027\u80FD\u7EDF\u8BA1</div>\n      <div>FPS: ".concat(stats.fps, "</div>\n      <div>\u7269\u7406\u66F4\u65B0\u65F6\u95F4: ").concat(stats.updateTime.toFixed(2), " ms</div>\n      <div>\u78B0\u649E\u68C0\u6D4B\u65F6\u95F4: ").concat(stats.collisionTime.toFixed(2), " ms</div>\n      <div>\u7EA6\u675F\u6C42\u89E3\u65F6\u95F4: ").concat(stats.constraintTime.toFixed(2), " ms</div>\n      <div>\u7269\u7406\u4F53\u6570\u91CF: ").concat(stats.bodyCount, "</div>\n      <div>\u7EA6\u675F\u6570\u91CF: ").concat(stats.constraintCount, "</div>\n      <div>\u78B0\u649E\u5BF9\u6570\u91CF: ").concat(stats.contactCount, "</div>\n    ");
    };
    /**
     * 获取物理世界
     * @returns 物理世界
     */
    EnhancedPhysicsDebugger.prototype.getPhysicsWorld = function () {
        return this.getPhysicsSystem().getPhysicsWorld();
    };
    /**
     * 获取物理系统
     * @returns 物理系统
     */
    EnhancedPhysicsDebugger.prototype.getPhysicsSystem = function () {
        return this.physicsSystem;
    };
    /**
     * 开始性能监控
     */
    EnhancedPhysicsDebugger.prototype.startPerformanceMonitor = function () {
        this.monitorStartTime = performance.now();
    };
    /**
     * 结束性能监控
     * @returns 监控时间 (ms)
     */
    EnhancedPhysicsDebugger.prototype.endPerformanceMonitor = function () {
        this.monitorEndTime = performance.now();
        return this.monitorEndTime - this.monitorStartTime;
    };
    /**
     * 获取性能统计数据
     * @returns 性能统计数据
     */
    EnhancedPhysicsDebugger.prototype.getPerformanceStats = function () {
        return this.performanceStats;
    };
    /**
     * 清除性能统计数据
     */
    EnhancedPhysicsDebugger.prototype.clearPerformanceStats = function () {
        this.performanceStats = [];
    };
    /**
     * 设置是否显示速度向量
     * @param show 是否显示
     */
    EnhancedPhysicsDebugger.prototype.setShowVelocities = function (show) {
        this.showVelocities = show;
        if (!show) {
            // 移除所有速度向量
            for (var _i = 0, _a = this.velocityArrows.values(); _i < _a.length; _i++) {
                var arrow = _a[_i];
                this.getScene().remove(arrow);
            }
            this.velocityArrows.clear();
        }
    };
    /**
     * 设置是否显示力向量
     * @param show 是否显示
     */
    EnhancedPhysicsDebugger.prototype.setShowForces = function (show) {
        this.showForces = show;
        if (!show) {
            // 移除所有力向量
            for (var _i = 0, _a = this.forceArrows.values(); _i < _a.length; _i++) {
                var arrow = _a[_i];
                this.getScene().remove(arrow);
            }
            this.forceArrows.clear();
        }
    };
    /**
     * 设置是否显示质心
     * @param show 是否显示
     */
    EnhancedPhysicsDebugger.prototype.setShowCenterOfMass = function (show) {
        this.showCenterOfMass = show;
        if (!show) {
            // 移除所有质心网格
            for (var _i = 0, _a = this.centerOfMassMeshes.values(); _i < _a.length; _i++) {
                var mesh = _a[_i];
                this.getScene().remove(mesh);
            }
            this.centerOfMassMeshes.clear();
        }
    };
    /**
     * 设置是否显示睡眠状态
     * @param show 是否显示
     */
    EnhancedPhysicsDebugger.prototype.setShowSleepState = function (show) {
        this.showSleepState = show;
        if (!show) {
            // 移除所有睡眠状态指示器
            for (var _i = 0, _a = this.sleepStateMeshes.values(); _i < _a.length; _i++) {
                var mesh = _a[_i];
                this.getScene().remove(mesh);
            }
            this.sleepStateMeshes.clear();
        }
    };
    /**
     * 设置是否显示物理性能统计
     * @param show 是否显示
     */
    EnhancedPhysicsDebugger.prototype.setShowPerformanceStats = function (show) {
        this.showPerformanceStats = show;
        if (show && !this.statsPanel) {
            this.initPerformanceStats();
        }
        else if (!show && this.statsPanel) {
            document.body.removeChild(this.statsPanel);
            this.statsPanel = null;
        }
    };
    /**
     * 设置是否显示碰撞法线
     * @param show 是否显示
     */
    EnhancedPhysicsDebugger.prototype.setShowContactNormals = function (show) {
        this.showContactNormals = show;
        if (!show) {
            // 移除所有碰撞法线向量
            for (var _i = 0, _a = this.contactNormalArrows; _i < _a.length; _i++) {
                var arrow = _a[_i];
                this.getScene().remove(arrow);
            }
            this.contactNormalArrows = [];
        }
    };
    /**
     * 设置是否显示碰撞力
     * @param show 是否显示
     */
    EnhancedPhysicsDebugger.prototype.setShowContactForces = function (show) {
        this.showContactForces = show;
        if (!show) {
            // 移除所有碰撞力向量
            for (var _i = 0, _a = this.contactForceArrows; _i < _a.length; _i++) {
                var arrow = _a[_i];
                this.getScene().remove(arrow);
            }
            this.contactForceArrows = [];
        }
    };
    /**
     * 设置是否显示摩擦力
     * @param show 是否显示
     */
    EnhancedPhysicsDebugger.prototype.setShowFrictionForces = function (show) {
        this.showFrictionForces = show;
        if (!show) {
            // 移除所有摩擦力向量
            for (var _i = 0, _a = this.frictionForceArrows; _i < _a.length; _i++) {
                var arrow = _a[_i];
                this.getScene().remove(arrow);
            }
            this.frictionForceArrows = [];
        }
    };
    /**
     * 获取调试场景
     * @returns 调试场景
     */
    EnhancedPhysicsDebugger.prototype.getScene = function () {
        return this.scene;
    };
    /**
     * 初始化调试器
     */
    EnhancedPhysicsDebugger.prototype.initialize = function () {
        // 调用父类的初始化方法
        _super.prototype.initialize.call(this);
        if (!this.initialized)
            return;
        // 获取物理世界
        var world = this.getPhysicsWorld();
        // 创建速度向量
        if (this.showVelocities) {
            for (var i = 0; i < world.bodies.length; i++) {
                var body = world.bodies[i];
                if (body.type === CANNON.Body.DYNAMIC) {
                    var arrow = this.createVelocityArrow(body);
                    this.velocityArrows.set(body, arrow);
                    this.getScene().add(arrow);
                }
            }
        }
        // 创建力向量
        if (this.showForces) {
            for (var i = 0; i < world.bodies.length; i++) {
                var body = world.bodies[i];
                if (body.type === CANNON.Body.DYNAMIC) {
                    var arrow = this.createForceArrow(body);
                    this.forceArrows.set(body, arrow);
                    this.getScene().add(arrow);
                }
            }
        }
        // 创建质心网格
        if (this.showCenterOfMass) {
            for (var i = 0; i < world.bodies.length; i++) {
                var body = world.bodies[i];
                if (body.type === CANNON.Body.DYNAMIC) {
                    var mesh = this.createCenterOfMassMesh(body);
                    this.centerOfMassMeshes.set(body, mesh);
                    this.getScene().add(mesh);
                }
            }
        }
        // 创建睡眠状态指示器
        if (this.showSleepState) {
            for (var i = 0; i < world.bodies.length; i++) {
                var body = world.bodies[i];
                if (body.type === CANNON.Body.DYNAMIC) {
                    var mesh = this.createSleepStateMesh(body);
                    this.sleepStateMeshes.set(body, mesh);
                    this.getScene().add(mesh);
                }
            }
        }
    };
    /**
     * 更新调试器
     */
    EnhancedPhysicsDebugger.prototype.update = function () {
        // 开始性能监控
        this.startPerformanceMonitor();
        // 调用父类的更新方法
        _super.prototype.update.call(this);
        if (!this.initialized)
            return;
        // 获取物理世界
        var world = this.getPhysicsWorld();
        // 更新速度向量
        if (this.showVelocities) {
            for (var i = 0; i < world.bodies.length; i++) {
                var body = world.bodies[i];
                if (body.type === CANNON.Body.DYNAMIC) {
                    // 如果物理体没有速度向量，创建一个
                    if (!this.velocityArrows.has(body)) {
                        var arrow_1 = this.createVelocityArrow(body);
                        this.velocityArrows.set(body, arrow_1);
                        this.getScene().add(arrow_1);
                    }
                    // 更新速度向量
                    var arrow = this.velocityArrows.get(body);
                    this.updateVelocityArrow(body, arrow);
                }
            }
            // 移除不存在的速度向量
            for (var _i = 0, _a = this.velocityArrows.entries(); _i < _a.length; _i++) {
                var _b = _a[_i], body = _b[0], arrow = _b[1];
                if (!world.bodies.includes(body)) {
                    this.getScene().remove(arrow);
                    this.velocityArrows.delete(body);
                }
            }
        }
        // 更新力向量
        if (this.showForces) {
            for (var i = 0; i < world.bodies.length; i++) {
                var body = world.bodies[i];
                if (body.type === CANNON.Body.DYNAMIC) {
                    // 如果物理体没有力向量，创建一个
                    if (!this.forceArrows.has(body)) {
                        var arrow_2 = this.createForceArrow(body);
                        this.forceArrows.set(body, arrow_2);
                        this.getScene().add(arrow_2);
                    }
                    // 更新力向量
                    var arrow = this.forceArrows.get(body);
                    this.updateForceArrow(body, arrow);
                }
            }
            // 移除不存在的力向量
            for (var _c = 0, _d = this.forceArrows.entries(); _c < _d.length; _c++) {
                var _e = _d[_c], body = _e[0], arrow = _e[1];
                if (!world.bodies.includes(body)) {
                    this.getScene().remove(arrow);
                    this.forceArrows.delete(body);
                }
            }
        }
        // 更新质心网格
        if (this.showCenterOfMass) {
            for (var i = 0; i < world.bodies.length; i++) {
                var body = world.bodies[i];
                if (body.type === CANNON.Body.DYNAMIC) {
                    // 如果物理体没有质心网格，创建一个
                    if (!this.centerOfMassMeshes.has(body)) {
                        var mesh_1 = this.createCenterOfMassMesh(body);
                        this.centerOfMassMeshes.set(body, mesh_1);
                        this.getScene().add(mesh_1);
                    }
                    // 更新质心网格
                    var mesh = this.centerOfMassMeshes.get(body);
                    this.updateCenterOfMassMesh(body, mesh);
                }
            }
            // 移除不存在的质心网格
            for (var _f = 0, _g = this.centerOfMassMeshes.entries(); _f < _g.length; _f++) {
                var _h = _g[_f], body = _h[0], mesh = _h[1];
                if (!world.bodies.includes(body)) {
                    this.getScene().remove(mesh);
                    this.centerOfMassMeshes.delete(body);
                }
            }
        }
        // 更新睡眠状态指示器
        if (this.showSleepState) {
            for (var i = 0; i < world.bodies.length; i++) {
                var body = world.bodies[i];
                if (body.type === CANNON.Body.DYNAMIC) {
                    // 如果物理体没有睡眠状态指示器，创建一个
                    if (!this.sleepStateMeshes.has(body)) {
                        var mesh_2 = this.createSleepStateMesh(body);
                        this.sleepStateMeshes.set(body, mesh_2);
                        this.getScene().add(mesh_2);
                    }
                    // 更新睡眠状态指示器
                    var mesh = this.sleepStateMeshes.get(body);
                    this.updateSleepStateMesh(body, mesh);
                }
            }
            // 移除不存在的睡眠状态指示器
            for (var _j = 0, _k = this.sleepStateMeshes.entries(); _j < _k.length; _j++) {
                var _l = _k[_j], body = _l[0], mesh = _l[1];
                if (!world.bodies.includes(body)) {
                    this.getScene().remove(mesh);
                    this.sleepStateMeshes.delete(body);
                }
            }
        }
        // 更新碰撞可视化
        if (this.showContactNormals || this.showContactForces || this.showFrictionForces) {
            this.updateContactVisualization();
        }
        // 更新性能统计
        if (this.showPerformanceStats) {
            this.updatePerformanceStats();
        }
        // 结束性能监控
        this.endPerformanceMonitor();
    };
    /**
     * 更新碰撞可视化
     */
    EnhancedPhysicsDebugger.prototype.updateContactVisualization = function () {
        // 获取物理世界
        var world = this.getPhysicsWorld();
        // 清除现有的碰撞可视化
        for (var _i = 0, _a = this.contactNormalArrows; _i < _a.length; _i++) {
            var arrow = _a[_i];
            this.getScene().remove(arrow);
        }
        this.contactNormalArrows = [];
        for (var _b = 0, _c = this.contactForceArrows; _b < _c.length; _b++) {
            var arrow = _c[_b];
            this.getScene().remove(arrow);
        }
        this.contactForceArrows = [];
        for (var _d = 0, _e = this.frictionForceArrows; _d < _e.length; _d++) {
            var arrow = _e[_d];
            this.getScene().remove(arrow);
        }
        this.frictionForceArrows = [];
        // 遍历所有碰撞
        for (var i = 0; i < world.contacts.length; i++) {
            var contact = world.contacts[i];
            // 遍历所有接触方程
            for (var j = 0; j < contact.contactEquations.length; j++) {
                var equation = contact.contactEquations[j];
                // 获取碰撞点和法线
                var contactPoint = new THREE.Vector3(equation.contactPointA.x + equation.bodyA.getPosition().x, equation.contactPointA.y + equation.bodyA.getPosition().y, equation.contactPointA.z + equation.bodyA.getPosition().z);
                var normal = new THREE.Vector3(equation.normalA.x, equation.normalA.y, equation.normalA.z);
                // 创建碰撞法线向量
                if (this.showContactNormals) {
                    var normalArrow = new THREE.ArrowHelper(normal.clone().normalize(), contactPoint, 0.2, this.contactNormalColor.getHex(), 0.05, 0.02);
                    this.contactNormalArrows.push(normalArrow);
                    this.getScene().add(normalArrow);
                }
                // 创建碰撞力向量
                if (this.showContactForces) {
                    var force = normal.clone().multiplyScalar(Math.abs(equation.multiplier) * this.contactForceScale);
                    if (force.length() > 0.01) {
                        var forceArrow = new THREE.ArrowHelper(force.clone().normalize(), contactPoint, force.length(), this.contactForceColor.getHex(), 0.05, 0.02);
                        this.contactForceArrows.push(forceArrow);
                        this.getScene().add(forceArrow);
                    }
                }
            }
            // 遍历所有摩擦方程
            if (this.showFrictionForces) {
                for (var j = 0; j < contact.frictionEquations.length; j++) {
                    var equation = contact.frictionEquations[j];
                    // 获取碰撞点
                    var contactPoint = new THREE.Vector3(equation.contactPointA.x + equation.bodyA.getPosition().x, equation.contactPointA.y + equation.bodyA.getPosition().y, equation.contactPointA.z + equation.bodyA.getPosition().z);
                    // 获取摩擦力方向
                    var frictionDirection = new THREE.Vector3(equation.t.x, equation.t.y, equation.t.z);
                    // 创建摩擦力向量
                    var force = frictionDirection.clone().multiplyScalar(Math.abs(equation.multiplier) * this.contactForceScale);
                    if (force.length() > 0.01) {
                        var forceArrow = new THREE.ArrowHelper(force.clone().normalize(), contactPoint, force.length(), this.frictionForceColor.getHex(), 0.05, 0.02);
                        this.frictionForceArrows.push(forceArrow);
                        this.getScene().add(forceArrow);
                    }
                }
            }
        }
    };
    /**
     * 创建速度向量
     * @param body 物理体
     * @returns 速度向量
     */
    EnhancedPhysicsDebugger.prototype.createVelocityArrow = function (body) {
        var origin = new THREE.Vector3(body.getPosition().x, body.getPosition().y, body.getPosition().z);
        var direction = new THREE.Vector3(body.velocity.x, body.velocity.y, body.velocity.z).normalize();
        var length = body.velocity.length() * this.vectorScale;
        return new THREE.ArrowHelper(direction, origin, length, this.velocityColor.getHex(), 0.05, 0.02);
    };
    /**
     * 更新速度向量
     * @param body 物理体
     * @param arrow 速度向量
     */
    EnhancedPhysicsDebugger.prototype.updateVelocityArrow = function (body, arrow) {
        if (!arrow)
            return;
        var origin = new THREE.Vector3(body.getPosition().x, body.getPosition().y, body.getPosition().z);
        var direction = new THREE.Vector3(body.velocity.x, body.velocity.y, body.velocity.z);
        var length = direction.length() * this.vectorScale;
        if (length > 0.001) {
            direction.normalize();
            arrow.position.copy(origin);
            arrow.setDirection(direction);
            arrow.setLength(length, length * 0.2, length * 0.1);
            arrow.visible = true;
        }
        else {
            arrow.visible = false;
        }
    };
    /**
     * 创建力向量
     * @param body 物理体
     * @returns 力向量
     */
    EnhancedPhysicsDebugger.prototype.createForceArrow = function (body) {
        var origin = new THREE.Vector3(body.getPosition().x, body.getPosition().y, body.getPosition().z);
        var direction = new THREE.Vector3(body.force.x, body.force.y, body.force.z);
        var length = direction.length() * this.vectorScale;
        if (length > 0.001) {
            direction.normalize();
        }
        else {
            direction.set(0, 1, 0);
        }
        return new THREE.ArrowHelper(direction, origin, length, this.forceColor.getHex(), 0.05, 0.02);
    };
    /**
     * 更新力向量
     * @param body 物理体
     * @param arrow 力向量
     */
    EnhancedPhysicsDebugger.prototype.updateForceArrow = function (body, arrow) {
        if (!arrow)
            return;
        var origin = new THREE.Vector3(body.getPosition().x, body.getPosition().y, body.getPosition().z);
        var direction = new THREE.Vector3(body.force.x, body.force.y, body.force.z);
        var length = direction.length() * this.vectorScale;
        if (length > 0.001) {
            direction.normalize();
            arrow.position.copy(origin);
            arrow.setDirection(direction);
            arrow.setLength(length, length * 0.2, length * 0.1);
            arrow.visible = true;
        }
        else {
            arrow.visible = false;
        }
    };
    /**
     * 创建质心网格
     * @param body 物理体
     * @returns 质心网格
     */
    EnhancedPhysicsDebugger.prototype.createCenterOfMassMesh = function (body) {
        var geometry = new THREE.SphereGeometry(0.05, 8, 8);
        var material = new THREE.MeshBasicMaterial({ color: this.centerOfMassColor });
        var mesh = new THREE.Mesh(geometry, material);
        mesh.setPosition(body.getPosition().x, body.getPosition().y, body.getPosition().z);
        return mesh;
    };
    /**
     * 更新质心网格
     * @param body 物理体
     * @param mesh 质心网格
     */
    EnhancedPhysicsDebugger.prototype.updateCenterOfMassMesh = function (body, mesh) {
        if (!mesh)
            return;
        mesh.setPosition(body.getPosition().x, body.getPosition().y, body.getPosition().z);
    };
    /**
     * 创建睡眠状态网格
     * @param body 物理体
     * @returns 睡眠状态网格
     */
    EnhancedPhysicsDebugger.prototype.createSleepStateMesh = function (body) {
        var canvas = document.createElement('canvas');
        canvas.width = 64;
        canvas.height = 64;
        var context = canvas.getContext('2d');
        context.fillStyle = body.sleepState === CANNON.Body.SLEEPING ? '#ff0000' : '#00ff00';
        context.fillRect(0, 0, 64, 64);
        context.fillStyle = '#ffffff';
        context.font = '12px Arial';
        context.textAlign = 'center';
        context.fillText(body.sleepState === CANNON.Body.SLEEPING ? 'S' : 'A', 32, 36);
        var texture = new THREE.CanvasTexture(canvas);
        var material = new THREE.SpriteMaterial({ map: texture });
        var sprite = new THREE.Sprite(material);
        sprite.setPosition(body.getPosition().x, body.getPosition().y + 1, body.getPosition().z);
        sprite.setScale(0.5, 0.5, 0.5);
        return sprite;
    };
    /**
     * 更新睡眠状态网格
     * @param body 物理体
     * @param sprite 睡眠状态精灵
     */
    EnhancedPhysicsDebugger.prototype.updateSleepStateMesh = function (body, sprite) {
        if (!sprite)
            return;
        sprite.setPosition(body.getPosition().x, body.getPosition().y + 1, body.getPosition().z);
        // 更新睡眠状态颜色
        var canvas = document.createElement('canvas');
        canvas.width = 64;
        canvas.height = 64;
        var context = canvas.getContext('2d');
        context.fillStyle = body.sleepState === CANNON.Body.SLEEPING ? '#ff0000' : '#00ff00';
        context.fillRect(0, 0, 64, 64);
        context.fillStyle = '#ffffff';
        context.font = '12px Arial';
        context.textAlign = 'center';
        context.fillText(body.sleepState === CANNON.Body.SLEEPING ? 'S' : 'A', 32, 36);
        var texture = new THREE.CanvasTexture(canvas);
        sprite.material.map = texture;
        sprite.material.needsUpdate = true;
    };
    /**
     * 销毁调试器
     */
    EnhancedPhysicsDebugger.prototype.dispose = function () {
        // 移除性能统计面板
        if (this.statsPanel) {
            document.body.removeChild(this.statsPanel);
            this.statsPanel = null;
        }
        // 清理所有向量和网格
        this.velocityArrows.clear();
        this.forceArrows.clear();
        this.centerOfMassMeshes.clear();
        this.sleepStateMeshes.clear();
        this.contactNormalArrows = [];
        this.contactForceArrows = [];
        this.frictionForceArrows = [];
        // 调用父类的销毁方法
        _super.prototype.dispose.call(this);
    };
    return EnhancedPhysicsDebugger;
}(PhysicsDebugger_1.PhysicsDebugger));
exports.EnhancedPhysicsDebugger = EnhancedPhysicsDebugger;

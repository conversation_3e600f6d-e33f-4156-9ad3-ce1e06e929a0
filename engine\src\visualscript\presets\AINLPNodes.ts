/**
 * 视觉脚本AI自然语言处理节点
 * 提供文本分类、命名实体识别、文本摘要和语言翻译等功能
 */
import type { Entity } from '../../core/Entity';
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';
import { AINLPSystem } from '../../ai/AINLPSystem';
import { TextClassificationResult } from '../../ai/TextClassificationResult';
import { NamedEntityRecognitionResult } from '../../ai/NamedEntityRecognitionResult';
import { TextSummaryResult } from '../../ai/TextSummaryResult';
import { TranslationResult } from '../../ai/TranslationResult';

/**
 * 文本分类节点
 * 对文本进行分类
 */
export class TextClassificationNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要分类的文本',
      defaultValue: ''
    });

    this.addInput({
      name: 'categories',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '可选的分类类别',
      defaultValue: []
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分类成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分类失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'category',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '分类结果'
    });

    this.addOutput({
      name: 'confidence',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '置信度'
    });

    this.addOutput({
      name: 'allCategories',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '所有分类结果及置信度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const text = this.getInputValue('text') as string;
    const categories = this.getInputValue('categories') as string[];

    // 检查输入值是否有效
    if (!text) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取NLP系统
    const nlpSystem = this.graph.getWorld().getSystem(AINLPSystem);
    if (!nlpSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 分类文本
      const result = await nlpSystem.classifyText(text, { categories });
      
      if (result) {
        // 设置输出值
        this.setOutputValue('category', result.category);
        this.setOutputValue('confidence', result.confidence);
        this.setOutputValue('allCategories', result.allCategories);
        
        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('文本分类失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 命名实体识别节点
 * 识别文本中的命名实体
 */
export class NamedEntityRecognitionNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要识别的文本',
      defaultValue: ''
    });

    this.addInput({
      name: 'entityTypes',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '要识别的实体类型',
      defaultValue: []
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '识别成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '识别失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'entities',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '识别到的实体'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const text = this.getInputValue('text') as string;
    const entityTypes = this.getInputValue('entityTypes') as string[];

    // 检查输入值是否有效
    if (!text) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取NLP系统
    const nlpSystem = this.graph.getWorld().getSystem(AINLPSystem);
    if (!nlpSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 识别命名实体
      const result = await nlpSystem.recognizeEntities(text, { entityTypes });
      
      if (result) {
        // 设置输出值
        this.setOutputValue('entities', result.entities);
        
        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('命名实体识别失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 文本摘要节点
 * 生成文本摘要
 */
export class TextSummaryNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要摘要的文本',
      defaultValue: ''
    });

    this.addInput({
      name: 'maxLength',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大摘要长度',
      defaultValue: 100
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '摘要成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '摘要失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'summary',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '生成的摘要'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const text = this.getInputValue('text') as string;
    const maxLength = this.getInputValue('maxLength') as number;

    // 检查输入值是否有效
    if (!text) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取NLP系统
    const nlpSystem = this.graph.getWorld().getSystem(AINLPSystem);
    if (!nlpSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 生成摘要
      const result = await nlpSystem.generateSummary(text, { maxLength });
      
      if (result) {
        // 设置输出值
        this.setOutputValue('summary', result.summary);
        
        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('生成摘要失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 注册AI自然语言处理节点
 * @param registry 节点注册表
 */
export function registerAINLPNodes(registry: NodeRegistry): void {
  // 注册文本分类节点
  registry.registerNodeType({
    type: 'ai/nlp/classifyText',
    category: NodeCategory.AI,
    constructor: TextClassificationNode,
    label: '文本分类',
    description: '对文本进行分类',
    icon: 'classify',
    color: '#673AB7',
    tags: ['ai', 'nlp', 'classification']
  });

  // 注册命名实体识别节点
  registry.registerNodeType({
    type: 'ai/nlp/recognizeEntities',
    category: NodeCategory.AI,
    constructor: NamedEntityRecognitionNode,
    label: '命名实体识别',
    description: '识别文本中的命名实体',
    icon: 'entity',
    color: '#673AB7',
    tags: ['ai', 'nlp', 'entity', 'recognition']
  });

  // 注册文本摘要节点
  registry.registerNodeType({
    type: 'ai/nlp/generateSummary',
    category: NodeCategory.AI,
    constructor: TextSummaryNode,
    label: '生成文本摘要',
    description: '生成文本摘要',
    icon: 'summary',
    color: '#673AB7',
    tags: ['ai', 'nlp', 'summary']
  });
}

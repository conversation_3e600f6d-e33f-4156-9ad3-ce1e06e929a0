"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuadtreePartitioning = exports.QuadtreeNode = void 0;
/**
 * 四叉树空间分区
 * 用于网络实体的高效空间查询和同步优化
 */
var THREE = require("three");
var Debug_1 = require("../../utils/Debug");
/**
 * 四叉树节点
 */
var QuadtreeNode = /** @class */ (function () {
    /**
     * 创建四叉树节点
     * @param minX 最小X坐标
     * @param minZ 最小Z坐标
     * @param maxX 最大X坐标
     * @param maxZ 最大Z坐标
     * @param depth 深度
     * @param parent 父节点
     */
    function QuadtreeNode(minX, minZ, maxX, maxZ, depth, parent) {
        if (depth === void 0) { depth = 0; }
        if (parent === void 0) { parent = null; }
        /** 子节点 */
        this.children = null;
        /** 实体列表 */
        this.entities = new Map();
        /** 父节点 */
        this.parent = null;
        /** 是否已分割 */
        this.divided = false;
        this.bounds = { minX: minX, minZ: minZ, maxX: maxX, maxZ: maxZ };
        this.depth = depth;
        this.parent = parent;
    }
    /**
     * 获取中心点
     * @returns 中心点
     */
    QuadtreeNode.prototype.getCenter = function () {
        var _a = this.bounds, minX = _a.minX, minZ = _a.minZ, maxX = _a.maxX, maxZ = _a.maxZ;
        return {
            x: (minX + maxX) / 2,
            z: (minZ + maxZ) / 2
        };
    };
    /**
     * 获取宽度
     * @returns 宽度
     */
    QuadtreeNode.prototype.getWidth = function () {
        return this.bounds.maxX - this.bounds.minX;
    };
    /**
     * 获取高度
     * @returns 高度
     */
    QuadtreeNode.prototype.getHeight = function () {
        return this.bounds.maxZ - this.bounds.minZ;
    };
    /**
     * 分割节点
     */
    QuadtreeNode.prototype.subdivide = function () {
        if (this.divided)
            return;
        var _a = this.bounds, minX = _a.minX, minZ = _a.minZ, maxX = _a.maxX, maxZ = _a.maxZ;
        var centerX = (minX + maxX) / 2;
        var centerZ = (minZ + maxZ) / 2;
        var nextDepth = this.depth + 1;
        // 创建四个子节点
        this.children = [
            // 左上
            new QuadtreeNode(minX, minZ, centerX, centerZ, nextDepth, this),
            // 右上
            new QuadtreeNode(centerX, minZ, maxX, centerZ, nextDepth, this),
            // 左下
            new QuadtreeNode(minX, centerZ, centerX, maxZ, nextDepth, this),
            // 右下
            new QuadtreeNode(centerX, centerZ, maxX, maxZ, nextDepth, this)
        ];
        this.divided = true;
        // 重新分配实体
        this.redistributeEntities();
    };
    /**
     * 重新分配实体
     */
    QuadtreeNode.prototype.redistributeEntities = function () {
        if (!this.children)
            return;
        // 遍历所有实体
        for (var _i = 0, _a = this.entities.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], entityId = _b[0], entity = _b[1];
            // 获取实体位置
            var position = this.getEntityPosition(entity);
            if (!position)
                continue;
            // 查找适合的子节点
            for (var _c = 0, _d = this.children; _c < _d.length; _c++) {
                var child = _d[_c];
                if (this.isPointInBounds(position.x, position.z, child.bounds)) {
                    // 添加到子节点
                    child.entities.set(entityId, entity);
                    break;
                }
            }
        }
    };
    /**
     * 检查点是否在边界内
     * @param x X坐标
     * @param z Z坐标
     * @param bounds 边界
     * @returns 是否在边界内
     */
    QuadtreeNode.prototype.isPointInBounds = function (x, z, bounds) {
        return (x >= bounds.minX &&
            x < bounds.maxX &&
            z >= bounds.minZ &&
            z < bounds.maxZ);
    };
    /**
     * 获取实体位置
     * @param entity 实体
     * @returns 位置
     */
    QuadtreeNode.prototype.getEntityPosition = function (entity) {
        var transform = entity.getComponent('Transform');
        if (!transform || !transform.position) {
            return null;
        }
        return transform.position;
    };
    return QuadtreeNode;
}());
exports.QuadtreeNode = QuadtreeNode;
/**
 * 四叉树空间分区
 */
var QuadtreePartitioning = /** @class */ (function () {
    /**
     * 创建四叉树空间分区
     * @param config 配置
     */
    function QuadtreePartitioning(config) {
        if (config === void 0) { config = {}; }
        /** 实体到节点的映射 */
        this.entityToNode = new Map();
        /** 实体列表 */
        this.entities = new Map();
        /** 调试网格 */
        this.debugMesh = null;
        // 默认配置
        this.config = __assign({ maxDepth: 8, maxEntities: 16, minNodeSize: 5, worldSize: 1000, worldCenter: new THREE.Vector3(0, 0, 0), enableDynamicAdjustment: true, enableLooseQuadtree: true, looseFactor: 1.5 }, config);
        // 创建根节点
        var halfSize = this.config.worldSize / 2;
        var center = this.config.worldCenter;
        this.root = new QuadtreeNode(center.x - halfSize, center.z - halfSize, center.x + halfSize, center.z + halfSize);
    }
    /**
     * 添加实体
     * @param entityId 实体ID
     * @param entity 实体
     */
    QuadtreePartitioning.prototype.addEntity = function (entityId, entity) {
        // 如果实体已存在，先移除
        if (this.entities.has(entityId)) {
            this.removeEntity(entityId);
        }
        // 添加到实体列表
        this.entities.set(entityId, entity);
        // 获取实体位置
        var position = this.getEntityPosition(entity);
        if (!position) {
            Debug_1.Debug.warn('QuadtreePartitioning', "\u5B9E\u4F53 ".concat(entityId, " \u6CA1\u6709\u4F4D\u7F6E\u4FE1\u606F"));
            return;
        }
        // 查找适合的节点
        var node = this.findNodeForPosition(position.x, position.z);
        if (!node) {
            Debug_1.Debug.warn('QuadtreePartitioning', "\u65E0\u6CD5\u4E3A\u5B9E\u4F53 ".concat(entityId, " \u627E\u5230\u5408\u9002\u7684\u8282\u70B9"));
            return;
        }
        // 添加到节点
        node.entities.set(entityId, entity);
        this.entityToNode.set(entityId, node);
        // 检查是否需要分割
        this.checkAndSubdivide(node);
    };
    /**
     * 移除实体
     * @param entityId 实体ID
     */
    QuadtreePartitioning.prototype.removeEntity = function (entityId) {
        // 获取实体所在节点
        var node = this.entityToNode.get(entityId);
        if (!node) {
            return;
        }
        // 从节点中移除
        node.entities.delete(entityId);
        // 从映射中移除
        this.entityToNode.delete(entityId);
        // 从实体列表中移除
        this.entities.delete(entityId);
        // 检查是否需要合并
        this.checkAndMerge(node);
    };
    /**
     * 更新实体
     * @param entityId 实体ID
     * @param entity 实体
     */
    QuadtreePartitioning.prototype.updateEntity = function (entityId, entity) {
        // 获取实体所在节点
        var node = this.entityToNode.get(entityId);
        if (!node) {
            // 如果实体不存在，则添加
            this.addEntity(entityId, entity);
            return;
        }
        // 获取实体位置
        var position = this.getEntityPosition(entity);
        if (!position) {
            return;
        }
        // 检查是否仍在同一节点
        if (this.isPointInBounds(position.x, position.z, node.bounds)) {
            // 更新实体
            this.entities.set(entityId, entity);
            node.entities.set(entityId, entity);
        }
        else {
            // 移除并重新添加
            this.removeEntity(entityId);
            this.addEntity(entityId, entity);
        }
    };
    /**
     * 查询区域内的实体
     * @param minX 最小X坐标
     * @param minZ 最小Z坐标
     * @param maxX 最大X坐标
     * @param maxZ 最大Z坐标
     * @returns 实体列表
     */
    QuadtreePartitioning.prototype.queryRegion = function (minX, minZ, maxX, maxZ) {
        var result = new Map();
        this.queryRegionRecursive(this.root, minX, minZ, maxX, maxZ, result);
        return result;
    };
    /**
     * 递归查询区域内的实体
     * @param node 节点
     * @param minX 最小X坐标
     * @param minZ 最小Z坐标
     * @param maxX 最大X坐标
     * @param maxZ 最大Z坐标
     * @param result 结果
     */
    QuadtreePartitioning.prototype.queryRegionRecursive = function (node, minX, minZ, maxX, maxZ, result) {
        // 检查是否与节点相交
        if (!this.isRegionOverlap(minX, minZ, maxX, maxZ, node.bounds)) {
            return;
        }
        // 添加节点中的实体
        for (var _i = 0, _a = node.entities.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], entityId = _b[0], entity = _b[1];
            var position = this.getEntityPosition(entity);
            if (!position)
                continue;
            if (position.x >= minX &&
                position.x <= maxX &&
                position.z >= minZ &&
                position.z <= maxZ) {
                result.set(entityId, entity);
            }
        }
        // 如果有子节点，递归查询
        if (node.children) {
            for (var _c = 0, _d = node.children; _c < _d.length; _c++) {
                var child = _d[_c];
                this.queryRegionRecursive(child, minX, minZ, maxX, maxZ, result);
            }
        }
    };
    /**
     * 查询距离内的实体
     * @param x 中心X坐标
     * @param z 中心Z坐标
     * @param radius 半径
     * @returns 实体列表
     */
    QuadtreePartitioning.prototype.queryRadius = function (x, z, radius) {
        // 创建包围盒
        var minX = x - radius;
        var minZ = z - radius;
        var maxX = x + radius;
        var maxZ = z + radius;
        // 先查询区域
        var regionResult = this.queryRegion(minX, minZ, maxX, maxZ);
        var result = new Map();
        // 过滤距离
        var radiusSq = radius * radius;
        for (var _i = 0, _a = regionResult.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], entityId = _b[0], entity = _b[1];
            var position = this.getEntityPosition(entity);
            if (!position)
                continue;
            var dx = position.x - x;
            var dz = position.z - z;
            var distSq = dx * dx + dz * dz;
            if (distSq <= radiusSq) {
                result.set(entityId, entity);
            }
        }
        return result;
    };
    /**
     * 查找位置所在的节点
     * @param x X坐标
     * @param z Z坐标
     * @returns 节点
     */
    QuadtreePartitioning.prototype.findNodeForPosition = function (x, z) {
        // 检查是否在根节点范围内
        if (!this.isPointInBounds(x, z, this.root.bounds)) {
            return null;
        }
        return this.findNodeForPositionRecursive(this.root, x, z);
    };
    /**
     * 递归查找位置所在的节点
     * @param node 节点
     * @param x X坐标
     * @param z Z坐标
     * @returns 节点
     */
    QuadtreePartitioning.prototype.findNodeForPositionRecursive = function (node, x, z) {
        // 如果没有子节点或达到最大深度，返回当前节点
        if (!node.children || node.depth >= this.config.maxDepth) {
            return node;
        }
        // 查找包含位置的子节点
        for (var _i = 0, _a = node.children; _i < _a.length; _i++) {
            var child = _a[_i];
            if (this.isPointInBounds(x, z, child.bounds)) {
                return this.findNodeForPositionRecursive(child, x, z);
            }
        }
        // 如果没有找到合适的子节点，返回当前节点
        return node;
    };
    /**
     * 检查并分割节点
     * @param node 节点
     */
    QuadtreePartitioning.prototype.checkAndSubdivide = function (node) {
        // 如果已达到最大深度或节点大小小于最小节点大小，不分割
        if (node.depth >= this.config.maxDepth ||
            node.getWidth() <= this.config.minNodeSize ||
            node.getHeight() <= this.config.minNodeSize) {
            return;
        }
        // 如果实体数量超过最大值，分割节点
        if (node.entities.size > this.config.maxEntities) {
            node.subdivide();
        }
    };
    /**
     * 检查并合并节点
     * @param node 节点
     */
    QuadtreePartitioning.prototype.checkAndMerge = function (node) {
        // 如果没有父节点，不合并
        if (!node.parent || !node.parent.children) {
            return;
        }
        // 计算所有子节点的实体总数
        var totalEntities = 0;
        for (var _i = 0, _a = node.parent.children; _i < _a.length; _i++) {
            var child = _a[_i];
            totalEntities += child.entities.size;
        }
        // 如果总实体数小于最大值的一半，合并节点
        if (totalEntities <= this.config.maxEntities / 2) {
            // 将所有子节点的实体移动到父节点
            for (var _b = 0, _c = node.parent.children; _b < _c.length; _b++) {
                var child = _c[_b];
                for (var _d = 0, _e = child.entities.entries(); _d < _e.length; _d++) {
                    var _f = _e[_d], entityId = _f[0], entity = _f[1];
                    node.parent.entities.set(entityId, entity);
                    this.entityToNode.set(entityId, node.parent);
                }
                child.entities.clear();
            }
            // 移除子节点
            node.parent.children = null;
            node.parent.divided = false;
        }
    };
    /**
     * 检查点是否在边界内
     * @param x X坐标
     * @param z Z坐标
     * @param bounds 边界
     * @returns 是否在边界内
     */
    QuadtreePartitioning.prototype.isPointInBounds = function (x, z, bounds) {
        return (x >= bounds.minX &&
            x < bounds.maxX &&
            z >= bounds.minZ &&
            z < bounds.maxZ);
    };
    /**
     * 检查区域是否与边界相交
     * @param minX 区域最小X
     * @param minZ 区域最小Z
     * @param maxX 区域最大X
     * @param maxZ 区域最大Z
     * @param bounds 边界
     * @returns 是否相交
     */
    QuadtreePartitioning.prototype.isRegionOverlap = function (minX, minZ, maxX, maxZ, bounds) {
        return (maxX >= bounds.minX &&
            minX <= bounds.maxX &&
            maxZ >= bounds.minZ &&
            minZ <= bounds.maxZ);
    };
    /**
     * 获取实体位置
     * @param entity 实体
     * @returns 位置
     */
    QuadtreePartitioning.prototype.getEntityPosition = function (entity) {
        var transform = entity.getComponent('Transform');
        if (!transform || !transform.position) {
            return null;
        }
        return transform.position;
    };
    /**
     * 清空四叉树
     */
    QuadtreePartitioning.prototype.clear = function () {
        this.entities.clear();
        this.entityToNode.clear();
        // 重新创建根节点
        var halfSize = this.config.worldSize / 2;
        var center = this.config.worldCenter;
        this.root = new QuadtreeNode(center.x - halfSize, center.z - halfSize, center.x + halfSize, center.z + halfSize);
    };
    /**
     * 获取实体数量
     * @returns 实体数量
     */
    QuadtreePartitioning.prototype.getEntityCount = function () {
        return this.entities.size;
    };
    /**
     * 获取所有实体
     * @returns 实体列表
     */
    QuadtreePartitioning.prototype.getAllEntities = function () {
        return new Map(this.entities);
    };
    return QuadtreePartitioning;
}());
exports.QuadtreePartitioning = QuadtreePartitioning;

/**
 * 场景加载系统
 * 支持智能预加载和资源管理
 */
import * as THREE from 'three';
import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import type { Camera   } from '../rendering/Camera';
import { Scene } from './Scene';
import type { Transform } from './Transform';
import { Debug } from '../utils/Debug';

/**
 * 资源类型枚举
 */
export enum ResourceType {
  /** 纹理 */
  TEXTURE = 'texture',
  /** 模型 */
  MODEL = 'model',
  /** 音频 */
  AUDIO = 'audio',
  /** 视频 */
  VIDEO = 'video',
  /** 材质 */
  MATERIAL = 'material',
  /** 着色器 */
  SHADER = 'shader',
  /** 字体 */
  FONT = 'font',
  /** 场景 */
  SCENE = 'scene',
  /** 其他 */
  OTHER = 'other'
}

/**
 * 资源优先级枚举
 */
export enum ResourcePriority {
  /** 非常高 */
  VERY_HIGH = 0,
  /** 高 */
  HIGH = 1,
  /** 中 */
  MEDIUM = 2,
  /** 低 */
  LOW = 3,
  /** 非常低 */
  VERY_LOW = 4
}

/**
 * 资源状态枚举
 */
export enum ResourceState {
  /** 未加载 */
  UNLOADED = 'unloaded',
  /** 加载中 */
  LOADING = 'loading',
  /** 已加载 */
  LOADED = 'loaded',
  /** 错误 */
  ERROR = 'error'
}

/**
 * 资源接口
 */
export interface Resource {
  /** 资源ID */
  id: string;
  /** 资源URL */
  url: string;
  /** 资源类型 */
  type: ResourceType;
  /** 资源优先级 */
  priority: ResourcePriority;
  /** 资源状态 */
  state: ResourceState;
  /** 资源数据 */
  data: any;
  /** 资源大小（字节） */
  size: number;
  /** 加载进度（0-1） */
  progress: number;
  /** 加载开始时间 */
  startTime: number;
  /** 加载结束时间 */
  endTime: number;
  /** 最后访问时间 */
  lastAccessTime: number;
  /** 引用计数 */
  referenceCount: number;
  /** 是否持久化 */
  persistent: boolean;
  /** 是否预加载 */
  preload: boolean;
  /** 依赖资源列表 */
  dependencies: string[];
  /** 用户数据 */
  userData: any;
}

/**
 * 场景区域接口
 */
export interface SceneRegion {
  /** 区域ID */
  id: string;
  /** 区域名称 */
  name: string;
  /** 区域包围盒 */
  boundingBox: THREE.Box3;
  /** 区域资源列表 */
  resources: string[];
  /** 区域优先级 */
  priority: ResourcePriority;
  /** 是否可见 */
  visible: boolean;
  /** 是否已加载 */
  loaded: boolean;
  /** 用户数据 */
  userData: any;
}

/**
 * 场景加载系统配置接口
 */
export interface SceneLoadingSystemOptions {
  /** 最大并发加载数 */
  maxConcurrentLoads?: number;
  /** 最大内存使用量（字节） */
  maxMemoryUsage?: number;
  /** 预加载距离 */
  preloadDistance?: number;
  /** 卸载距离 */
  unloadDistance?: number;
  /** 是否使用预测加载 */
  usePredictiveLoading?: boolean;
  /** 是否使用缓存 */
  useCache?: boolean;
  /** 是否使用压缩 */
  useCompression?: boolean;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
  /** 加载超时时间（毫秒） */
  loadTimeout?: number;
  /** 重试次数 */
  retryCount?: number;
  /** 重试延迟（毫秒） */
  retryDelay?: number;
}

/**
 * 加载任务接口
 */
export interface LoadTask {
  /** 任务ID */
  id: string;
  /** 资源ID */
  resourceId: string;
  /** 任务优先级 */
  priority: ResourcePriority;
  /** 任务状态 */
  state: ResourceState;
  /** 加载器 */
  loader: THREE.Loader;
  /** 加载进度 */
  progress: number;
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime: number;
  /** 重试次数 */
  retryCount: number;
  /** 错误信息 */
  error: Error | null;
  /** 取消函数 */
  cancel: () => void;
  /** 完成回调 */
  onComplete: (resource: Resource) => void;
  /** 进度回调 */
  onProgress: (progress: number) => void;
  /** 错误回调 */
  onError: (error: Error) => void;
}

/**
 * 场景加载系统类
 */
export class SceneLoadingSystem extends System {
  /** 系统类型 */
  private static readonly TYPE: string = 'SceneLoadingSystem';

  /** 最大并发加载数 */
  private maxConcurrentLoads: number;

  /** 最大内存使用量（字节） */
  private maxMemoryUsage: number;

  /** 预加载距离 */
  private preloadDistance: number;

  /** 卸载距离 */
  private unloadDistance: number;

  /** 是否使用预测加载 */
  private usePredictiveLoading: boolean;

  /** 是否使用缓存 */
  private useCache: boolean;

  /** 是否使用压缩 */
  private useCompression: boolean;

  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** 加载超时时间（毫秒） */
  private loadTimeout: number;

  /** 重试次数 */
  private retryCount: number;

  /** 重试延迟（毫秒） */
  private retryDelay: number;

  /** 资源列表 */
  private resources: Map<string, Resource> = new Map();

  /** 场景区域列表 */
  private regions: Map<string, SceneRegion> = new Map();

  /** 加载任务队列 */
  private loadQueue: LoadTask[] = [];

  /** 活动加载任务列表 */
  private activeLoadTasks: Map<string, LoadTask> = new Map();

  /** 当前内存使用量（字节） */
  private currentMemoryUsage: number = 0;

  /** 加载器映射 */
  private loaders: Map<ResourceType, THREE.Loader> = new Map();

  /** 缓存 */
  private cache: Map<string, any> = new Map();

  /** 调试可视化材质 */
  private debugMaterial: THREE.MeshBasicMaterial | null = null;

  /** 调试可视化网格 */
  private debugMeshes: THREE.Mesh[] = [];

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 任务计数器 */
  private taskCounter: number = 0;

  /** 资源计数器 */
  private resourceCounter: number = 0;

  /** 区域计数器 */
  private regionCounter: number = 0;

  /** 上一次相机位置 */
  private lastCameraPosition: THREE.Vector3 = new THREE.Vector3();

  /** 相机移动方向 */
  private cameraDirection: THREE.Vector3 = new THREE.Vector3();

  /** 相机移动速度 */
  private cameraSpeed: number = 0;

  /** 相机位置历史 */
  private cameraPositionHistory: THREE.Vector3[] = [];

  /** 相机位置历史最大长度 */
  private cameraPositionHistoryMaxLength: number = 10;

  /**
   * 创建场景加载系统
   * @param options 场景加载系统配置
   */
  constructor(options: SceneLoadingSystemOptions = {}) {
    super();

    this.maxConcurrentLoads = options.maxConcurrentLoads || 5;
    this.maxMemoryUsage = options.maxMemoryUsage || 1024 * 1024 * 1024; // 1GB
    this.preloadDistance = options.preloadDistance || 100;
    this.unloadDistance = options.unloadDistance || 200;
    this.usePredictiveLoading = options.usePredictiveLoading !== undefined ? options.usePredictiveLoading : true;
    this.useCache = options.useCache !== undefined ? options.useCache : true;
    this.useCompression = options.useCompression !== undefined ? options.useCompression : false;
    this.useDebugVisualization = options.useDebugVisualization !== undefined ? options.useDebugVisualization : false;
    this.loadTimeout = options.loadTimeout || 30000; // 30秒
    this.retryCount = options.retryCount || 3;
    this.retryDelay = options.retryDelay || 1000; // 1秒

    // 初始化加载器
    this.initializeLoaders();
  }

  /**
   * 初始化加载器
   */
  private initializeLoaders(): void {
    // 纹理加载器
    this.loaders.set(ResourceType.TEXTURE, new THREE.TextureLoader());

    // 模型加载器
    const modelLoader = new THREE.ObjectLoader();
    this.loaders.set(ResourceType.MODEL, modelLoader);

    // 音频加载器
    const audioLoader = new THREE.AudioLoader();
    this.loaders.set(ResourceType.AUDIO, audioLoader);

    // 其他加载器可以根据需要添加
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 初始化调试可视化
    if (this.useDebugVisualization) {
      this.initializeDebugVisualization();
    }

    this.initialized = true;
  }

  /**
   * 初始化调试可视化
   */
  private initializeDebugVisualization(): void {
    // 创建调试材质
    this.debugMaterial = new THREE.MeshBasicMaterial({
      wireframe: true,
      transparent: true,
      opacity: 0.5
    });
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 获取相机和场景
    const camera = this.getCamera();
    const scene = this.getScene();

    if (!camera || !scene) {
      return;
    }

    // 更新相机位置和方向
    this.updateCameraInfo(camera, deltaTime);

    // 更新场景区域
    this.updateRegions(camera);

    // 更新加载队列
    this.updateLoadQueue();

    // 处理加载任务
    this.processLoadTasks();

    // 管理资源
    this.manageResources(camera);

    // 更新调试可视化
    if (this.useDebugVisualization) {
      this.updateDebugVisualization();
    }
  }

  /**
   * 获取相机
   * @returns 相机
   */
  protected getCamera(): Camera | null {
    if (!this.world) {
      return null;
    }

    // 遍历所有实体查找相机组件
    for (const entity of this.world.getEntities().values()) {
      const camera = entity.getComponent('Camera') as Camera;
      if (camera) {
        return camera;
      }
    }

    return null;
  }

  /**
   * 获取场景
   * @returns 场景
   */
  protected getScene(): Scene | null {
    if (!this.world) {
      return null;
    }

    // 遍历所有实体查找场景组件
    for (const entity of this.world.getEntities().values()) {
      const scene = entity.getComponent('Scene');
      if (scene && scene instanceof Scene) {
        return scene;
      }
    }

    return null;
  }

  /**
   * 更新相机信息
   * @param camera 相机
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateCameraInfo(camera: Camera, deltaTime: number): void {
    // 获取相机位置
    const position = camera.getThreeCamera().position.clone();

    // 计算相机移动方向和速度
    if (this.lastCameraPosition.lengthSq() > 0) {
      // 计算移动方向
      this.cameraDirection.copy(position).sub(this.lastCameraPosition).normalize();

      // 计算移动速度
      const distance = position.distanceTo(this.lastCameraPosition);
      this.cameraSpeed = deltaTime > 0 ? distance / deltaTime : 0;
    }

    // 更新相机位置历史
    this.cameraPositionHistory.push(position.clone());
    if (this.cameraPositionHistory.length > this.cameraPositionHistoryMaxLength) {
      this.cameraPositionHistory.shift();
    }

    // 更新上一次相机位置
    this.lastCameraPosition.copy(position);
  }

  /**
   * 更新场景区域
   * @param camera 相机
   */
  private updateRegions(camera: Camera): void {
    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 遍历所有区域
    for (const region of this.regions.values()) {
      // 计算区域中心
      const regionCenter = new THREE.Vector3();
      region.boundingBox.getCenter(regionCenter);

      // 计算相机到区域中心的距离
      const distance = cameraPosition.distanceTo(regionCenter);

      // 如果距离小于预加载距离，则加载区域
      if (distance < this.preloadDistance) {
        this.loadRegion(region);
      }
      // 如果距离大于卸载距离，则卸载区域
      else if (distance > this.unloadDistance) {
        this.unloadRegion(region);
      }
    }

    // 如果使用预测加载，则预测相机移动并预加载区域
    if (this.usePredictiveLoading && this.cameraSpeed > 0) {
      this.predictAndPreload(camera);
    }
  }

  /**
   * 预测相机移动并预加载区域
   * @param camera 相机
   */
  private predictAndPreload(camera: Camera): void {
    // 如果相机位置历史不足，则返回
    if (this.cameraPositionHistory.length < 2) {
      return;
    }

    // 计算相机移动方向
    const direction = new THREE.Vector3();
    for (let i = 1; i < this.cameraPositionHistory.length; i++) {
      const prev = this.cameraPositionHistory[i - 1];
      const curr = this.cameraPositionHistory[i];
      direction.add(new THREE.Vector3().copy(curr).sub(prev));
    }
    direction.divideScalar(this.cameraPositionHistory.length - 1).normalize();

    // 预测相机位置
    const predictedPosition = new THREE.Vector3().copy(camera.getThreeCamera().position).add(
      direction.multiplyScalar(this.preloadDistance * 1.5)
    );

    // 查找预测位置附近的区域
    const nearbyRegions: { region: SceneRegion; distance: number }[] = [];
    for (const region of this.regions.values()) {
      // 计算区域中心
      const regionCenter = new THREE.Vector3();
      region.boundingBox.getCenter(regionCenter);

      // 计算预测位置到区域中心的距离
      const distance = predictedPosition.distanceTo(regionCenter);

      // 如果距离小于预加载距离，则添加到附近区域列表
      if (distance < this.preloadDistance * 1.5) {
        nearbyRegions.push({ region, distance });
      }
    }

    // 按距离排序
    nearbyRegions.sort((a, b) => a.distance - b.distance);

    // 预加载附近区域
    for (const { region } of nearbyRegions) {
      this.preloadRegion(region);
    }
  }

  /**
   * 加载区域
   * @param region 区域
   */
  private loadRegion(region: SceneRegion): void {
    // 如果区域已加载，则返回
    if (region.loaded) {
      return;
    }

    // 标记区域为已加载
    region.loaded = true;
    region.visible = true;

    // 加载区域资源
    for (const resourceId of region.resources) {
      this.loadResource(resourceId, region.priority);
    }

    Debug.log('SceneLoadingSystem', `加载区域：${region.name}`);
  }

  /**
   * 预加载区域
   * @param region 区域
   */
  private preloadRegion(region: SceneRegion): void {
    // 如果区域已加载，则返回
    if (region.loaded) {
      return;
    }

    // 预加载区域资源
    for (const resourceId of region.resources) {
      const resource = this.resources.get(resourceId);
      if (resource && resource.preload) {
        this.loadResource(resourceId, ResourcePriority.LOW);
      }
    }

    Debug.log('SceneLoadingSystem', `预加载区域：${region.name}`);
  }

  /**
   * 卸载区域
   * @param region 区域
   */
  private unloadRegion(region: SceneRegion): void {
    // 如果区域未加载，则返回
    if (!region.loaded) {
      return;
    }

    // 标记区域为未加载
    region.loaded = false;
    region.visible = false;

    // 卸载区域资源
    for (const resourceId of region.resources) {
      this.unloadResource(resourceId);
    }

    Debug.log('SceneLoadingSystem', `卸载区域：${region.name}`);
  }

  /**
   * 加载资源
   * @param resourceId 资源ID
   * @param priority 优先级
   * @param callbacks 可选的回调函数
   */
  protected loadResource(resourceId: string, priority: ResourcePriority, callbacks?: {
    onComplete?: () => void;
    onProgress?: (progress: number) => void;
    onError?: (error: Error) => void;
  }): void {
    // 获取资源
    const resource = this.resources.get(resourceId);
    if (!resource) {
      Debug.warn('SceneLoadingSystem', `资源不存在：${resourceId}`);
      return;
    }

    // 如果资源已加载，则增加引用计数
    if (resource.state === ResourceState.LOADED) {
      resource.referenceCount++;
      resource.lastAccessTime = Date.now();
      return;
    }

    // 如果资源正在加载，则更新优先级
    if (resource.state === ResourceState.LOADING) {
      // 查找加载任务
      for (const task of this.loadQueue) {
        if (task.resourceId === resourceId) {
          // 更新优先级
          task.priority = Math.min(task.priority, priority);
          break;
        }
      }
      return;
    }

    // 创建加载任务
    const taskId = `task_${this.taskCounter++}`;
    const task: LoadTask = {
      id: taskId,
      resourceId,
      priority,
      state: ResourceState.UNLOADED,
      loader: this.loaders.get(resource.type) || this.loaders.get(ResourceType.OTHER)!,
      progress: 0,
      startTime: 0,
      endTime: 0,
      retryCount: 0,
      error: null,
      cancel: () => {},
      onComplete: (loadedResource: Resource) => {
        // 更新资源状态
        resource.state = ResourceState.LOADED;
        resource.data = loadedResource.data;
        resource.size = loadedResource.size;
        resource.progress = 1;
        resource.endTime = Date.now();
        resource.lastAccessTime = Date.now();
        resource.referenceCount++;

        // 更新内存使用量
        this.currentMemoryUsage += resource.size;

        // 更新任务状态
        task.state = ResourceState.LOADED;
        task.endTime = Date.now();
        this.activeLoadTasks.delete(taskId);

        // 如果使用缓存，则缓存资源
        if (this.useCache) {
          this.cache.set(resourceId, resource.data);
        }

        Debug.log('SceneLoadingSystem', `资源加载完成：${resource.url}`);

        // 调用外部回调
        if (callbacks?.onComplete) {
          callbacks.onComplete();
        }
      },
      onProgress: (progress: number) => {
        // 更新资源进度
        resource.progress = progress;
        task.progress = progress;

        // 调用外部回调
        if (callbacks?.onProgress) {
          callbacks.onProgress(progress);
        }
      },
      onError: (error: Error) => {
        // 更新资源状态
        resource.state = ResourceState.ERROR;
        resource.progress = 0;
        resource.endTime = Date.now();

        // 更新任务状态
        task.state = ResourceState.ERROR;
        task.error = error;
        task.endTime = Date.now();
        this.activeLoadTasks.delete(taskId);

        // 如果重试次数未达到上限，则重试
        if (task.retryCount < this.retryCount) {
          task.retryCount++;
          task.state = ResourceState.UNLOADED;
          setTimeout(() => {
            this.loadQueue.push(task);
          }, this.retryDelay);
          Debug.warn('SceneLoadingSystem', `资源加载失败，重试（${task.retryCount}/${this.retryCount}）：${resource.url}`);
        } else {
          Debug.error('SceneLoadingSystem', `资源加载失败：${resource.url}`, error);

          // 调用外部回调
          if (callbacks?.onError) {
            callbacks.onError(error);
          }
        }
      }
    };

    // 更新资源状态
    resource.state = ResourceState.LOADING;
    resource.progress = 0;
    resource.startTime = Date.now();

    // 添加到加载队列
    this.loadQueue.push(task);

    Debug.log('SceneLoadingSystem', `添加资源到加载队列：${resource.url}`);
  }

  /**
   * 卸载资源
   * @param resourceId 资源ID
   */
  protected unloadResource(resourceId: string): void {
    // 获取资源
    const resource = this.resources.get(resourceId);
    if (!resource) {
      return;
    }

    // 减少引用计数
    resource.referenceCount--;

    // 如果资源是持久化的或者引用计数大于0，则不卸载
    if (resource.persistent || resource.referenceCount > 0) {
      return;
    }

    // 如果资源正在加载，则取消加载
    if (resource.state === ResourceState.LOADING) {
      // 查找加载任务
      for (const task of this.loadQueue) {
        if (task.resourceId === resourceId) {
          // 从加载队列中移除
          const index = this.loadQueue.indexOf(task);
          if (index !== -1) {
            this.loadQueue.splice(index, 1);
          }
          break;
        }
      }
      for (const [taskId, task] of this.activeLoadTasks.entries()) {
        if (task.resourceId === resourceId) {
          // 取消加载任务
          task.cancel();
          this.activeLoadTasks.delete(taskId);
          break;
        }
      }
    }

    // 如果资源已加载，则卸载
    if (resource.state === ResourceState.LOADED) {
      // 更新内存使用量
      this.currentMemoryUsage -= resource.size;

      // 释放资源
      if (resource.data) {
        if (resource.data.dispose) {
          (resource.data as any).dispose();
        }
        resource.data = null;
      }

      // 更新资源状态
      resource.state = ResourceState.UNLOADED;
      resource.progress = 0;
      resource.endTime = 0;

      Debug.log('SceneLoadingSystem', `卸载资源：${resource.url}`);
    }
  }

  /**
   * 更新加载队列
   */
  protected updateLoadQueue(): void {
    // 按优先级排序
    this.loadQueue.sort((a, b) => a.priority - b.priority);

    // 如果活动加载任务数量小于最大并发加载数，则启动新的加载任务
    while (this.activeLoadTasks.size < this.maxConcurrentLoads && this.loadQueue.length > 0) {
      const task = this.loadQueue.shift()!;
      this.startLoadTask(task);
    }
  }

  /**
   * 启动加载任务
   * @param task 加载任务
   */
  private startLoadTask(task: LoadTask): void {
    // 获取资源
    const resource = this.resources.get(task.resourceId);
    if (!resource) {
      return;
    }

    // 如果使用缓存且资源已缓存，则直接使用缓存
    if (this.useCache && this.cache.has(task.resourceId)) {
      resource.data = this.cache.get(task.resourceId);
      resource.state = ResourceState.LOADED;
      resource.progress = 1;
      resource.endTime = Date.now();
      resource.lastAccessTime = Date.now();
      resource.referenceCount++;

      // 更新内存使用量
      this.currentMemoryUsage += resource.size;

      Debug.log('SceneLoadingSystem', `从缓存加载资源：${resource.url}`);
      return;
    }

    // 更新任务状态
    task.state = ResourceState.LOADING;
    task.startTime = Date.now();

    // 添加到活动加载任务
    this.activeLoadTasks.set(task.id, task);

    // 启动加载
    const loader = task.loader;
    const url = resource.url;

    // 创建加载管理器
    const manager = new THREE.LoadingManager();
    manager.onProgress = (url, loaded, total) => {
      const progress = total > 0 ? loaded / total : 0;
      task.onProgress(progress);
    };

    // 加载资源
    try {
      const loadPromise = new Promise<any>((resolve, reject) => {
        // 根据资源类型选择加载方法
        switch (resource.type) {
          case ResourceType.TEXTURE:
            (loader as THREE.TextureLoader).load(url, resolve, undefined, reject);
            break;
          case ResourceType.MODEL:
            (loader as THREE.ObjectLoader).load(url, resolve, undefined, reject);
            break;
          case ResourceType.AUDIO:
            (loader as THREE.AudioLoader).load(url, resolve, undefined, reject);
            break;
          default:
            // 默认使用文件加载器
            new THREE.FileLoader().load(url, resolve, undefined, reject);
            break;
        }
      });

      // 设置超时
      const timeoutPromise = new Promise<any>((_, reject) => {
        const timeoutId = setTimeout(() => {
          reject(new Error(`加载超时：${url}`));
        }, this.loadTimeout);
        task.cancel = () => {
          clearTimeout(timeoutId);
          reject(new Error(`加载取消：${url}`));
        };
      });

      // 竞争加载和超时
      Promise.race([loadPromise, timeoutPromise])
        .then((data) => {
          // 计算资源大小
          let size = 0;
          if (data instanceof THREE.Texture) {
            const image = data.image;
            size = image.width * image.height * 4; // RGBA
          } else if (data instanceof ArrayBuffer) {
            size = data.byteLength;
          } else if (typeof data === 'string') {
            size = data.length * 2; // UTF-16
          } else if (data instanceof THREE.Object3D) {
            // 估算模型大小
            size = 1000000; // 1MB
          } else {
            // 默认大小
            size = 1000; // 1KB
          }

          // 更新资源
          const loadedResource: Resource = {
            ...resource,
            data,
            size,
            state: ResourceState.LOADED,
            progress: 1,
            endTime: Date.now()
          };

          task.onComplete(loadedResource);
        })
        .catch((error) => {
          task.onError(error);
        });
    } catch (error) {
      task.onError(error as Error);
    }

    Debug.log('SceneLoadingSystem', `开始加载资源：${resource.url}`);
  }

  /**
   * 处理加载任务
   */
  protected processLoadTasks(): void {
    // 检查活动加载任务是否超时
    const now = Date.now();
    for (const [taskId, task] of this.activeLoadTasks.entries()) {
      if (task.startTime > 0 && now - task.startTime > this.loadTimeout) {
        // 取消加载任务
        task.cancel();
        this.activeLoadTasks.delete(taskId);

        // 更新资源状态
        const resource = this.resources.get(task.resourceId);
        if (resource) {
          resource.state = ResourceState.ERROR;
          resource.progress = 0;
          resource.endTime = now;
        }

        Debug.warn('SceneLoadingSystem', `加载任务超时：${taskId}`);
      }
    }
  }

  /**
   * 管理资源
   * @param camera 相机
   */
  private manageResources(camera: Camera): void {
    // 如果内存使用量超过最大内存使用量，则释放资源
    if (this.currentMemoryUsage > this.maxMemoryUsage) {
      this.releaseResources();
    }
  }

  /**
   * 释放资源
   */
  private releaseResources(): void {
    // 收集可释放的资源
    const releasableResources: { resource: Resource; score: number }[] = [];
    for (const resource of this.resources.values()) {
      // 如果资源已加载且不是持久化的且引用计数为0，则可以释放
      if (resource.state === ResourceState.LOADED && !resource.persistent && resource.referenceCount === 0) {
        // 计算资源分数（越低越先释放）
        const score = resource.lastAccessTime + resource.priority * 1000000;
        releasableResources.push({ resource, score });
      }
    }

    // 按分数排序
    releasableResources.sort((a, b) => a.score - b.score);

    // 释放资源直到内存使用量低于最大内存使用量的80%
    const targetMemoryUsage = this.maxMemoryUsage * 0.8;
    for (const { resource } of releasableResources) {
      // 卸载资源
      this.unloadResource(resource.id);

      // 如果内存使用量已经低于目标，则停止释放
      if (this.currentMemoryUsage < targetMemoryUsage) {
        break;
      }
    }

    Debug.log('SceneLoadingSystem', `释放资源完成，当前内存使用量：${this.formatBytes(this.currentMemoryUsage)}`);
  }

  /**
   * 格式化字节数
   * @param bytes 字节数
   * @returns 格式化后的字符串
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 清除调试网格
    for (const mesh of this.debugMeshes) {
      mesh.parent?.remove(mesh);
    }
    this.debugMeshes = [];

    // 获取场景
    const scene = this.getScene();
    if (!scene) {
      return;
    }

    // 可视化场景区域
    for (const region of this.regions.values()) {
      // 创建区域调试网格
      const size = new THREE.Vector3();
      region.boundingBox.getSize(size);
      const center = new THREE.Vector3();
      region.boundingBox.getCenter(center);

      const geometry = new THREE.BoxGeometry(size.x, size.y, size.z);
      const material = this.debugMaterial!.clone();

      // 设置区域调试网格颜色
      if (region.loaded) {
        (material as THREE.MeshBasicMaterial).color.set(0x00ff00); // 绿色
      } else {
        (material as THREE.MeshBasicMaterial).color.set(0xff0000); // 红色
      }

      const mesh = new THREE.Mesh(geometry, material);
      mesh.position.copy(center);

      // 添加到场景
      scene.getThreeScene().add(mesh);
      this.debugMeshes.push(mesh);
    }
  }

  /**
   * 添加资源
   * @param resource 资源
   * @returns 资源ID
   */
  public addResource(resource: Partial<Resource>): string {
    // 生成资源ID
    const resourceId = resource.id || `resource_${this.resourceCounter++}`;

    // 创建资源
    const newResource: Resource = {
      id: resourceId,
      url: resource.url || '',
      type: resource.type || ResourceType.OTHER,
      priority: resource.priority || ResourcePriority.MEDIUM,
      state: ResourceState.UNLOADED,
      data: null,
      size: resource.size || 0,
      progress: 0,
      startTime: 0,
      endTime: 0,
      lastAccessTime: Date.now(),
      referenceCount: 0,
      persistent: resource.persistent || false,
      preload: resource.preload || false,
      dependencies: resource.dependencies || [],
      userData: resource.userData || {}
    };

    // 添加到资源列表
    this.resources.set(resourceId, newResource);

    return resourceId;
  }

  /**
   * 添加场景区域
   * @param region 场景区域
   * @returns 区域ID
   */
  public addRegion(region: Partial<SceneRegion>): string {
    // 生成区域ID
    const regionId = region.id || `region_${this.regionCounter++}`;

    // 创建区域
    const newRegion: SceneRegion = {
      id: regionId,
      name: region.name || `Region ${regionId}`,
      boundingBox: region.boundingBox || new THREE.Box3(),
      resources: region.resources || [],
      priority: region.priority || ResourcePriority.MEDIUM,
      visible: false,
      loaded: false,
      userData: region.userData || {}
    };

    // 添加到区域列表
    this.regions.set(regionId, newRegion);

    return regionId;
  }

  /**
   * 获取资源
   * @param resourceId 资源ID
   * @returns 资源
   */
  public getResource(resourceId: string): Resource | null {
    return this.resources.get(resourceId) || null;
  }

  /**
   * 获取区域
   * @param regionId 区域ID
   * @returns 区域
   */
  public getRegion(regionId: string): SceneRegion | null {
    return this.regions.get(regionId) || null;
  }

  /**
   * 获取加载状态
   * @returns 加载状态
   */
  public getLoadingStatus(): {
    totalResources: number;
    loadedResources: number;
    loadingResources: number;
    errorResources: number;
    totalRegions: number;
    loadedRegions: number;
    memoryUsage: number;
    maxMemoryUsage: number;
    activeLoadTasks: number;
    queuedLoadTasks: number;
  } {
    // 统计资源
    let loadedResources = 0;
    let loadingResources = 0;
    let errorResources = 0;
    for (const resource of this.resources.values()) {
      if (resource.state === ResourceState.LOADED) {
        loadedResources++;
      } else if (resource.state === ResourceState.LOADING) {
        loadingResources++;
      } else if (resource.state === ResourceState.ERROR) {
        errorResources++;
      }
    }

    // 统计区域
    let loadedRegions = 0;
    for (const region of this.regions.values()) {
      if (region.loaded) {
        loadedRegions++;
      }
    }

    return {
      totalResources: this.resources.size,
      loadedResources,
      loadingResources,
      errorResources,
      totalRegions: this.regions.size,
      loadedRegions,
      memoryUsage: this.currentMemoryUsage,
      maxMemoryUsage: this.maxMemoryUsage,
      activeLoadTasks: this.activeLoadTasks.size,
      queuedLoadTasks: this.loadQueue.length
    };
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    // 取消所有加载任务
    for (const task of this.activeLoadTasks.values()) {
      task.cancel();
    }
    this.activeLoadTasks.clear();
    this.loadQueue = [];

    // 卸载所有资源
    for (const resource of this.resources.values()) {
      if (resource.state === ResourceState.LOADED && resource.data) {
        if (resource.data.dispose) {
          (resource.data as any).dispose();
        }
      }
    }
    this.resources.clear();
    this.regions.clear();
    this.cache.clear();

    // 清除调试网格
    for (const mesh of this.debugMeshes) {
      mesh.parent?.remove(mesh);
      (mesh.geometry as any).dispose();
      (mesh.material as THREE.Material).dispose();
    }
    this.debugMeshes = [];

    // 清除调试材质
    if (this.debugMaterial) {
      (this.debugMaterial as any).dispose();
      this.debugMaterial = null;
    }

    this.initialized = false;
  }
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CharacterController = void 0;
/**
 * 角色控制器
 * 基于Cannon.js实现的角色控制器，提供类似于Rapier3D的角色控制器功能
 */
var THREE = require("three");
var CANNON = require("cannon-es");
/**
 * 角色控制器
 * 提供角色移动、碰撞检测、斜坡爬升、自动台阶等功能
 */
var CharacterController = /** @class */ (function () {
    /**
     * 创建角色控制器
     * @param entity 关联的实体
     * @param world 物理世界
     * @param options 控制器选项
     */
    function CharacterController(entity, world, options) {
        if (options === void 0) { options = {}; }
        var _a, _b, _c;
        /** 物理体 */
        this.body = null;
        /** 碰撞器 */
        this.collider = null;
        /** 计算出的移动向量 */
        this.computedMovement = new THREE.Vector3();
        /** 是否在地面上 */
        this.isGrounded = false;
        /** 上一次接触的地面法线 */
        this.groundNormal = new THREE.Vector3(0, 1, 0);
        /** 射线检测起点偏移 */
        this.raycastOffset = 0.05;
        /** 射线检测结果缓存 */
        this.raycastResults = [];
        this.entity = entity;
        this.world = world;
        // 设置选项
        this.offset = options.offset !== undefined ? options.offset : 0.01;
        this.maxSlopeClimbAngle = options.maxSlopeClimbAngle !== undefined ? options.maxSlopeClimbAngle : (60 * Math.PI) / 180;
        this.minSlopeSlideAngle = options.minSlopeSlideAngle !== undefined ? options.minSlopeSlideAngle : (30 * Math.PI) / 180;
        // 自动台阶设置
        this.autoStep = {
            enabled: !!options.autoStep,
            maxHeight: ((_a = options.autoStep) === null || _a === void 0 ? void 0 : _a.maxHeight) || 0.5,
            minWidth: ((_b = options.autoStep) === null || _b === void 0 ? void 0 : _b.minWidth) || 0.01,
            stepOverDynamic: ((_c = options.autoStep) === null || _c === void 0 ? void 0 : _c.stepOverDynamic) || true
        };
        // 地面吸附设置
        this.snapToGround = {
            enabled: options.enableSnapToGround !== false && options.enableSnapToGround !== undefined,
            distance: typeof options.enableSnapToGround === 'number' ? options.enableSnapToGround : 0.1
        };
        // 获取物理体和碰撞器
        this.body = entity.getComponent('PhysicsBody');
        this.collider = entity.getComponent('PhysicsCollider');
        if (!this.body) {
            console.warn('角色控制器需要PhysicsBody组件');
        }
        if (!this.collider) {
            console.warn('角色控制器需要PhysicsCollider组件');
        }
    }
    /**
     * 计算碰撞器移动
     * @param desiredTranslation 期望的移动向量
     * @param filterGroups 碰撞组过滤
     * @param filterPredicate 碰撞过滤谓词函数
     */
    CharacterController.prototype.computeColliderMovement = function (desiredTranslation, filterGroups, filterPredicate) {
        if (!this.body || !this.body.getCannonBody()) {
            return;
        }
        // 重置计算的移动向量
        this.computedMovement.copy(desiredTranslation);
        // 检查是否在地面上
        this.updateGroundedState();
        // 处理斜坡
        if (this.isGrounded) {
            this.handleSlopes(desiredTranslation);
        }
        // 处理自动台阶
        if (this.autoStep.enabled && this.isGrounded && desiredTranslation.y <= 0) {
            this.handleAutoStep(desiredTranslation, filterGroups, filterPredicate);
        }
        // 处理地面吸附
        if (this.snapToGround.enabled && this.isGrounded && desiredTranslation.y <= 0) {
            this.handleSnapToGround();
        }
        // 处理碰撞
        this.handleCollisions(this.computedMovement, filterGroups, filterPredicate);
    };
    /**
     * 获取计算出的移动向量
     * @returns 计算出的移动向量
     */
    CharacterController.prototype.getComputedMovement = function () {
        return this.computedMovement.clone();
    };
    /**
     * 获取偏移量
     * @returns 偏移量
     */
    CharacterController.prototype.getOffset = function () {
        return this.offset;
    };
    /**
     * 设置最大爬坡角度
     * @param angle 角度（弧度）
     */
    CharacterController.prototype.setMaxSlopeClimbAngle = function (angle) {
        this.maxSlopeClimbAngle = angle;
    };
    /**
     * 获取最大爬坡角度
     * @returns 角度（弧度）
     */
    CharacterController.prototype.getMaxSlopeClimbAngle = function () {
        return this.maxSlopeClimbAngle;
    };
    /**
     * 设置最小滑坡角度
     * @param angle 角度（弧度）
     */
    CharacterController.prototype.setMinSlopeSlideAngle = function (angle) {
        this.minSlopeSlideAngle = angle;
    };
    /**
     * 获取最小滑坡角度
     * @returns 角度（弧度）
     */
    CharacterController.prototype.getMinSlopeSlideAngle = function () {
        return this.minSlopeSlideAngle;
    };
    /**
     * 启用自动台阶
     * @param maxHeight 最大台阶高度
     * @param minWidth 最小台阶宽度
     * @param stepOverDynamic 是否可以踏上动态物体
     */
    CharacterController.prototype.enableAutoStep = function (maxHeight, minWidth, stepOverDynamic) {
        this.autoStep = {
            enabled: true,
            maxHeight: maxHeight,
            minWidth: minWidth,
            stepOverDynamic: stepOverDynamic
        };
    };
    /**
     * 禁用自动台阶
     */
    CharacterController.prototype.disableAutoStep = function () {
        this.autoStep.enabled = false;
    };
    /**
     * 启用地面吸附
     * @param distance 吸附距离
     */
    CharacterController.prototype.enableSnapToGround = function (distance) {
        this.snapToGround = {
            enabled: true,
            distance: distance
        };
    };
    /**
     * 禁用地面吸附
     */
    CharacterController.prototype.disableSnapToGround = function () {
        this.snapToGround.enabled = false;
    };
    /**
     * 是否启用自动台阶
     * @returns 是否启用
     */
    CharacterController.prototype.isAutoStepEnabled = function () {
        return this.autoStep.enabled;
    };
    /**
     * 是否启用地面吸附
     * @returns 是否启用
     */
    CharacterController.prototype.isSnapToGroundEnabled = function () {
        return this.snapToGround.enabled;
    };
    /**
     * 获取自动台阶最大高度
     * @returns 最大高度
     */
    CharacterController.prototype.getAutoStepMaxHeight = function () {
        return this.autoStep.maxHeight;
    };
    /**
     * 获取自动台阶最小宽度
     * @returns 最小宽度
     */
    CharacterController.prototype.getAutoStepMinWidth = function () {
        return this.autoStep.minWidth;
    };
    /**
     * 是否可以踏上动态物体
     * @returns 是否可以
     */
    CharacterController.prototype.canStepOverDynamic = function () {
        return this.autoStep.stepOverDynamic;
    };
    /**
     * 是否在地面上
     * @returns 是否在地面上
     */
    CharacterController.prototype.isOnGround = function () {
        return this.isGrounded;
    };
    /**
     * 获取地面法线
     * @returns 地面法线
     */
    CharacterController.prototype.getGroundNormal = function () {
        return this.groundNormal.clone();
    };
    /**
     * 更新是否在地面上的状态
     * @private
     */
    CharacterController.prototype.updateGroundedState = function () {
        var _this = this;
        if (!this.body || !this.body.getCannonBody()) {
            this.isGrounded = false;
            return;
        }
        var cannonBody = this.body.getCannonBody();
        var position = cannonBody.position;
        // 创建向下的射线
        var start = new CANNON.Vec3(position.x, position.y, position.z);
        var end = new CANNON.Vec3(position.x, position.y - (this.offset + this.raycastOffset), position.z);
        // 执行射线检测
        this.raycastResults.length = 0;
        this.world.raycastAll(start, end, {}, function (result) {
            // 排除自身
            if (result.body === cannonBody)
                return;
            _this.raycastResults.push(result);
        });
        // 检查是否有碰撞
        if (this.raycastResults.length > 0) {
            // 按距离排序
            this.raycastResults.sort(function (a, b) { return a.distance - b.distance; });
            // 获取最近的碰撞
            var result = this.raycastResults[0];
            // 更新状态
            this.isGrounded = true;
            this.groundNormal.set(result.hitNormalWorld.x, result.hitNormalWorld.y, result.hitNormalWorld.z);
        }
        else {
            this.isGrounded = false;
            this.groundNormal.set(0, 1, 0);
        }
    };
    /**
     * 处理斜坡
     * @param desiredTranslation 期望的移动向量
     * @private
     */
    CharacterController.prototype.handleSlopes = function (desiredTranslation) {
        // 计算斜坡角度
        var slopeAngle = Math.acos(this.groundNormal.dot(new THREE.Vector3(0, 1, 0)));
        // 如果斜坡角度大于最大爬坡角度，则滑下
        if (slopeAngle > this.maxSlopeClimbAngle) {
            // 计算滑下方向
            var slideDirection = new THREE.Vector3();
            slideDirection.copy(this.groundNormal);
            slideDirection.y = 0;
            slideDirection.normalize();
            // 应用滑下
            this.computedMovement.y = -Math.sin(slopeAngle) * desiredTranslation.length();
            return;
        }
        // 如果斜坡角度大于最小滑坡角度，则部分滑下
        if (slopeAngle > this.minSlopeSlideAngle) {
            // 计算滑下比例
            var slideRatio = (slopeAngle - this.minSlopeSlideAngle) / (this.maxSlopeClimbAngle - this.minSlopeSlideAngle);
            // 应用部分滑下
            this.computedMovement.y = -Math.sin(slopeAngle) * desiredTranslation.length() * slideRatio;
        }
        // 沿着斜坡方向移动
        if (desiredTranslation.y <= 0 && slopeAngle > 0.01) {
            // 计算沿斜坡方向的分量
            var slopeDirection = new THREE.Vector3();
            slopeDirection.copy(desiredTranslation);
            slopeDirection.y = 0;
            slopeDirection.normalize();
            // 计算沿斜坡向上的分量
            var upComponent = slopeDirection.dot(this.groundNormal) * Math.sin(slopeAngle);
            // 应用沿斜坡向上的分量
            this.computedMovement.y = Math.max(this.computedMovement.y, upComponent);
        }
    };
    /**
     * 处理自动台阶
     * @param desiredTranslation 期望的移动向量
     * @param filterGroups 碰撞组过滤
     * @param filterPredicate 碰撞过滤谓词函数
     * @private
     */
    CharacterController.prototype.handleAutoStep = function (desiredTranslation, filterGroups, filterPredicate) {
        var _this = this;
        if (!this.body || !this.body.getCannonBody()) {
            return;
        }
        var cannonBody = this.body.getCannonBody();
        var position = cannonBody.position;
        // 计算移动方向
        var moveDir = new THREE.Vector3(desiredTranslation.x, 0, desiredTranslation.z).normalize();
        // 如果没有水平移动，则不需要处理台阶
        if (moveDir.lengthSq() < 0.0001) {
            return;
        }
        // 创建向前的射线
        var forwardStart = new CANNON.Vec3(position.x, position.y + this.raycastOffset, position.z);
        var forwardEnd = new CANNON.Vec3(position.x + moveDir.x * this.autoStep.minWidth, position.y + this.raycastOffset, position.z + moveDir.z * this.autoStep.minWidth);
        // 执行向前的射线检测
        this.raycastResults.length = 0;
        this.world.raycastAll(forwardStart, forwardEnd, {}, function (result) {
            // 排除自身
            if (result.body === cannonBody)
                return;
            // 如果不能踏上动态物体，则排除动态物体
            if (!_this.autoStep.stepOverDynamic && result.body.type !== CANNON.BODY_TYPES.STATIC)
                return;
            _this.raycastResults.push(result);
        });
        // 如果没有碰撞，则不需要处理台阶
        if (this.raycastResults.length === 0) {
            return;
        }
        // 按距离排序
        this.raycastResults.sort(function (a, b) { return a.distance - b.distance; });
        // 获取最近的碰撞
        var forwardHit = this.raycastResults[0];
        // 创建向上的射线
        var upStart = new CANNON.Vec3(forwardHit.hitPointWorld.x, forwardHit.hitPointWorld.y, forwardHit.hitPointWorld.z);
        var upEnd = new CANNON.Vec3(forwardHit.hitPointWorld.x, forwardHit.hitPointWorld.y + this.autoStep.maxHeight, forwardHit.hitPointWorld.z);
        // 执行向上的射线检测
        this.raycastResults.length = 0;
        this.world.raycastAll(upStart, upEnd, {}, function (result) {
            // 排除自身
            if (result.body === cannonBody)
                return;
            _this.raycastResults.push(result);
        });
        // 如果向上有碰撞，则不能踏上台阶
        if (this.raycastResults.length > 0) {
            return;
        }
        // 创建向前向下的射线
        var downStart = new CANNON.Vec3(forwardHit.hitPointWorld.x + moveDir.x * this.autoStep.minWidth, forwardHit.hitPointWorld.y + this.autoStep.maxHeight, forwardHit.hitPointWorld.z + moveDir.z * this.autoStep.minWidth);
        var downEnd = new CANNON.Vec3(downStart.x, forwardHit.hitPointWorld.y - this.raycastOffset, downStart.z);
        // 执行向前向下的射线检测
        this.raycastResults.length = 0;
        this.world.raycastAll(downStart, downEnd, {}, function (result) {
            // 排除自身
            if (result.body === cannonBody)
                return;
            _this.raycastResults.push(result);
        });
        // 如果没有向下的碰撞，则不能踏上台阶
        if (this.raycastResults.length === 0) {
            return;
        }
        // 按距离排序
        this.raycastResults.sort(function (a, b) { return a.distance - b.distance; });
        // 获取最近的碰撞
        var downHit = this.raycastResults[0];
        // 计算台阶高度
        var stepHeight = downHit.hitPointWorld.y - position.y;
        // 如果台阶高度在范围内，则踏上台阶
        if (stepHeight > 0 && stepHeight <= this.autoStep.maxHeight) {
            this.computedMovement.y = stepHeight;
        }
    };
    /**
     * 处理地面吸附
     * @private
     */
    CharacterController.prototype.handleSnapToGround = function () {
        var _this = this;
        if (!this.body || !this.body.getCannonBody()) {
            return;
        }
        var cannonBody = this.body.getCannonBody();
        var position = cannonBody.position;
        // 创建向下的射线
        var start = new CANNON.Vec3(position.x, position.y, position.z);
        var end = new CANNON.Vec3(position.x, position.y - this.snapToGround.distance, position.z);
        // 执行射线检测
        this.raycastResults.length = 0;
        this.world.raycastAll(start, end, {}, function (result) {
            // 排除自身
            if (result.body === cannonBody)
                return;
            _this.raycastResults.push(result);
        });
        // 检查是否有碰撞
        if (this.raycastResults.length > 0) {
            // 按距离排序
            this.raycastResults.sort(function (a, b) { return a.distance - b.distance; });
            // 获取最近的碰撞
            var result = this.raycastResults[0];
            // 计算吸附距离
            var snapDistance = result.distance - this.offset;
            // 应用吸附
            if (snapDistance > 0 && snapDistance < this.snapToGround.distance) {
                this.computedMovement.y = -snapDistance;
            }
        }
    };
    /**
     * 处理碰撞
     * @param movement 移动向量
     * @param filterGroups 碰撞组过滤
     * @param filterPredicate 碰撞过滤谓词函数
     * @private
     */
    CharacterController.prototype.handleCollisions = function (movement, filterGroups, filterPredicate) {
        // 这里实现碰撞检测和响应
        // 由于Cannon.js没有内置的连续碰撞检测，我们需要手动实现
        // 这是一个简化的实现，实际应用中可能需要更复杂的算法
        var _this = this;
        if (!this.body || !this.body.getCannonBody()) {
            return;
        }
        var cannonBody = this.body.getCannonBody();
        var position = cannonBody.position;
        // 获取碰撞器的尺寸
        var radius = 0.5; // 默认半径
        if (this.collider) {
            var shapes = this.collider.getShapes();
            if (shapes.length > 0) {
                var shape = shapes[0];
                if (shape instanceof CANNON.Sphere) {
                    radius = shape.radius;
                }
                else if (shape instanceof CANNON.Box) {
                    // 使用最小的半宽作为半径
                    radius = Math.min(shape.halfExtents.x, shape.halfExtents.y, shape.halfExtents.z);
                }
            }
        }
        // 创建射线检测
        var start = new CANNON.Vec3(position.x, position.y, position.z);
        var end = new CANNON.Vec3(position.x + movement.x, position.y + movement.y, position.z + movement.z);
        // 执行射线检测
        this.raycastResults.length = 0;
        this.world.raycastAll(start, end, {}, function (result) {
            // 排除自身
            if (result.body === cannonBody)
                return;
            // 应用过滤器
            if (filterPredicate && !filterPredicate(result.body))
                return;
            _this.raycastResults.push(result);
        });
        // 如果没有碰撞，则不需要处理
        if (this.raycastResults.length === 0) {
            return;
        }
        // 按距离排序
        this.raycastResults.sort(function (a, b) { return a.distance - b.distance; });
        // 获取最近的碰撞
        var result = this.raycastResults[0];
        // 计算碰撞点到移动终点的距离
        var hitToEnd = new THREE.Vector3(end.x - result.hitPointWorld.x, end.y - result.hitPointWorld.y, end.z - result.hitPointWorld.z);
        // 计算碰撞法线
        var normal = new THREE.Vector3(result.hitNormalWorld.x, result.hitNormalWorld.y, result.hitNormalWorld.z);
        // 计算沿法线的分量
        var normalComponent = hitToEnd.dot(normal);
        // 如果沿法线的分量小于等于0，则不需要调整
        if (normalComponent <= 0) {
            return;
        }
        // 计算调整后的移动向量
        var adjustment = new THREE.Vector3();
        adjustment.copy(normal).multiplyScalar(normalComponent + this.offset);
        // 应用调整
        movement.sub(adjustment);
    };
    return CharacterController;
}());
exports.CharacterController = CharacterController;

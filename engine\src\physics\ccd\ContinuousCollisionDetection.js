"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContinuousCollisionDetection = void 0;
var CANNON = require("cannon-es");
/**
 * 连续碰撞检测
 */
var ContinuousCollisionDetection = /** @class */ (function () {
    /**
     * 创建连续碰撞检测
     * @param world 物理世界
     * @param options 选项
     */
    function ContinuousCollisionDetection(world, options) {
        if (options === void 0) { options = {}; }
        /** 启用CCD的物体 */
        this.enabledBodies = new Set();
        /** 物体的上一个位置 */
        this.previousPositions = new Map();
        /** 物体的上一个旋转 */
        this.previousQuaternions = new Map();
        this.world = world;
        this.maxSubSteps = options.maxSubSteps || 5;
        this.minSubStepTime = options.minSubStepTime || 1 / 240;
        this.velocityThreshold = options.velocityThreshold || 5;
        this.enableForAll = options.enableForAll || false;
        // 保存所有物体的初始位置和旋转
        this.updatePreviousStates();
    }
    /**
     * 更新所有物体的上一个状态
     */
    ContinuousCollisionDetection.prototype.updatePreviousStates = function () {
        for (var _i = 0, _a = this.world.bodies; _i < _a.length; _i++) {
            var body = _a[_i];
            this.previousPositions.set(body, body.position.clone());
            this.previousQuaternions.set(body, body.quaternion.clone());
        }
    };
    /**
     * 启用物体的CCD
     * @param body 物理体
     */
    ContinuousCollisionDetection.prototype.enableBodyCCD = function (body) {
        this.enabledBodies.add(body);
    };
    /**
     * 禁用物体的CCD
     * @param body 物理体
     */
    ContinuousCollisionDetection.prototype.disableBodyCCD = function (body) {
        this.enabledBodies.delete(body);
    };
    /**
     * 检查物体是否启用CCD
     * @param body 物理体
     * @returns 是否启用CCD
     */
    ContinuousCollisionDetection.prototype.isBodyCCDEnabled = function (body) {
        return this.enabledBodies.has(body);
    };
    /**
     * 启用实体的CCD
     * @param entity 实体
     */
    ContinuousCollisionDetection.prototype.enableEntityCCD = function (entity) {
        var physicsBody = entity.getComponent(PhysicsBody.type);
        if (physicsBody) {
            var cannonBody = physicsBody.getCannonBody();
            if (cannonBody) {
                this.enableBodyCCD(cannonBody);
            }
        }
    };
    /**
     * 禁用实体的CCD
     * @param entity 实体
     */
    ContinuousCollisionDetection.prototype.disableEntityCCD = function (entity) {
        var physicsBody = entity.getComponent(PhysicsBody.type);
        if (physicsBody) {
            var cannonBody = physicsBody.getCannonBody();
            if (cannonBody) {
                this.disableBodyCCD(cannonBody);
            }
        }
    };
    /**
     * 检查实体是否启用CCD
     * @param entity 实体
     * @returns 是否启用CCD
     */
    ContinuousCollisionDetection.prototype.isEntityCCDEnabled = function (entity) {
        var physicsBody = entity.getComponent(PhysicsBody.type);
        if (physicsBody) {
            var cannonBody = physicsBody.getCannonBody();
            if (cannonBody) {
                return this.isBodyCCDEnabled(cannonBody);
            }
        }
        return false;
    };
    /**
     * 更新连续碰撞检测
     * @param deltaTime 时间步长
     */
    ContinuousCollisionDetection.prototype.update = function (deltaTime) {
        // 检查是否需要进行子步进
        var needsSubStepping = this.checkNeedsSubStepping();
        if (needsSubStepping) {
            // 计算子步数
            var numSubSteps = Math.min(Math.ceil(deltaTime / this.minSubStepTime), this.maxSubSteps);
            var subStepTime = deltaTime / numSubSteps;
            // 保存当前状态
            var originalPositions = new Map();
            var originalQuaternions = new Map();
            for (var _i = 0, _a = this.world.bodies; _i < _a.length; _i++) {
                var body = _a[_i];
                originalPositions.set(body, body.position.clone());
                originalQuaternions.set(body, body.quaternion.clone());
            }
            // 执行子步进
            for (var i = 0; i < numSubSteps; i++) {
                // 更新物体位置
                this.updateBodyPositions(subStepTime, i, numSubSteps, originalPositions, originalQuaternions);
                // 执行物理步进
                this.world.step(subStepTime);
                // 更新上一个状态
                this.updatePreviousStates();
            }
        }
        else {
            // 正常步进
            this.world.step(deltaTime);
            // 更新上一个状态
            this.updatePreviousStates();
        }
    };
    /**
     * 检查是否需要进行子步进
     * @returns 是否需要子步进
     */
    ContinuousCollisionDetection.prototype.checkNeedsSubStepping = function () {
        // 如果对所有物体启用CCD，则始终进行子步进
        if (this.enableForAll) {
            return true;
        }
        // 检查是否有启用CCD的物体速度超过阈值
        for (var _i = 0, _a = this.enabledBodies; _i < _a.length; _i++) {
            var body = _a[_i];
            var velocity = body.velocity.length();
            if (velocity > this.velocityThreshold) {
                return true;
            }
        }
        return false;
    };
    /**
     * 更新物体位置
     * @param subStepTime 子步时间
     * @param currentStep 当前子步
     * @param totalSteps 总子步数
     * @param originalPositions 原始位置
     * @param originalQuaternions 原始旋转
     */
    ContinuousCollisionDetection.prototype.updateBodyPositions = function (subStepTime, currentStep, totalSteps, originalPositions, originalQuaternions) {
        var t = (currentStep + 1) / totalSteps;
        for (var _i = 0, _a = this.world.bodies; _i < _a.length; _i++) {
            var body = _a[_i];
            if (body.type === CANNON.BODY_TYPES.DYNAMIC) {
                var originalPosition = originalPositions.get(body);
                var originalQuaternion = originalQuaternions.get(body);
                if (originalPosition && originalQuaternion) {
                    // 线性插值位置和旋转
                    body.getPosition().x = originalPosition.x + (body.velocity.x * subStepTime * (currentStep + 1));
                    body.getPosition().y = originalPosition.y + (body.velocity.y * subStepTime * (currentStep + 1));
                    body.getPosition().z = originalPosition.z + (body.velocity.z * subStepTime * (currentStep + 1));
                    // 简单的角速度积分（CANNON.js的slerp方法参数不同）
                    var angularDisplacement = new CANNON.Vec3();
                    angularDisplacement.copy(body.angularVelocity);
                    angularDisplacement.scale(subStepTime * (currentStep + 1), angularDisplacement);
                    // 创建旋转四元数
                    var rotationQuat = new CANNON.Quaternion();
                    rotationQuat.setFromAxisAngle(angularDisplacement, angularDisplacement.length());
                    // 应用旋转
                    body.quaternion.mult(rotationQuat, body.quaternion);
                }
            }
        }
    };
    return ContinuousCollisionDetection;
}());
exports.ContinuousCollisionDetection = ContinuousCollisionDetection;

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkUserComponent = void 0;
/**
 * 网络用户组件
 * 用于标识和管理网络用户
 */
var Component_1 = require("../../core/Component");
var NetworkUser_1 = require("../NetworkUser");
/**
 * 网络用户组件
 * 用于标识和管理网络用户
 */
var NetworkUserComponent = exports.NetworkUserComponent = /** @class */ (function (_super) {
    __extends(NetworkUserComponent, _super);
    /**
     * 创建网络用户组件
     * @param entity 实体
     * @param props 组件属性
     */
    function NetworkUserComponent(entity, props) {
        var _this = _super.call(this, NetworkUserComponent.type) || this;
        /** 是否有待同步的更改 */
        _this.hasPendingChanges = false;
        /** 待同步的属性 */
        _this.pendingProperties = new Set();
        // 设置实体引用
        _this.setEntity(entity);
        _this.userId = props.userId;
        _this.username = props.username;
        _this.displayName = props.displayName || props.username;
        _this.avatarUrl = props.avatarUrl || '';
        _this.state = props.state || NetworkUser_1.NetworkUserState.ONLINE;
        _this.role = props.role || NetworkUser_1.NetworkUserRole.USER;
        _this.isLocal = props.isLocal || false;
        _this.customData = props.customData || {};
        _this.joinTime = Date.now();
        _this.lastActiveTime = Date.now();
        return _this;
    }
    /**
     * 初始化组件
     */
    NetworkUserComponent.prototype.initialize = function () {
        // 初始化逻辑
    };
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    NetworkUserComponent.prototype.update = function (deltaTime) {
        // 更新逻辑
    };
    /**
     * 设置用户状态
     * @param state 用户状态
     */
    NetworkUserComponent.prototype.setState = function (state) {
        var _a;
        if (this.state === state) {
            return;
        }
        this.state = state;
        this.lastActiveTime = Date.now();
        // 标记属性为待同步
        this.markPropertyDirty('state');
        // 触发状态变更事件
        (_a = this.entity) === null || _a === void 0 ? void 0 : _a.emit('userStateChanged', {
            userId: this.userId,
            state: state,
        });
    };
    /**
     * 设置用户角色
     * @param role 用户角色
     */
    NetworkUserComponent.prototype.setRole = function (role) {
        var _a;
        if (this.role === role) {
            return;
        }
        this.role = role;
        // 标记属性为待同步
        this.markPropertyDirty('role');
        // 触发角色变更事件
        (_a = this.entity) === null || _a === void 0 ? void 0 : _a.emit('userRoleChanged', {
            userId: this.userId,
            role: role,
        });
    };
    /**
     * 设置自定义数据
     * @param key 键
     * @param value 值
     */
    NetworkUserComponent.prototype.setCustomData = function (key, value) {
        var _a;
        this.customData[key] = value;
        // 标记属性为待同步
        this.markPropertyDirty('customData');
        // 触发自定义数据变更事件
        (_a = this.entity) === null || _a === void 0 ? void 0 : _a.emit('userCustomDataChanged', {
            userId: this.userId,
            key: key,
            value: value,
        });
    };
    /**
     * 获取自定义数据
     * @param key 键
     * @returns 值
     */
    NetworkUserComponent.prototype.getCustomData = function (key) {
        return this.customData[key];
    };
    /**
     * 更新活动时间
     */
    NetworkUserComponent.prototype.updateActivityTime = function () {
        this.lastActiveTime = Date.now();
    };
    /**
     * 获取用户数据
     * @returns 用户数据
     */
    NetworkUserComponent.prototype.getUserData = function () {
        return {
            userId: this.userId,
            username: this.username,
            displayName: this.displayName,
            avatarUrl: this.avatarUrl,
            state: this.state,
            role: this.role,
            isLocal: this.isLocal,
            customData: this.customData,
            joinTime: this.joinTime,
            lastActiveTime: this.lastActiveTime,
        };
    };
    /**
     * 应用用户数据
     * @param data 用户数据
     */
    NetworkUserComponent.prototype.applyUserData = function (data) {
        if (data.username) {
            this.username = data.username;
        }
        if (data.displayName) {
            this.displayName = data.displayName;
        }
        if (data.avatarUrl) {
            this.avatarUrl = data.avatarUrl;
        }
        if (data.state) {
            this.state = data.state;
        }
        if (data.role) {
            this.role = data.role;
        }
        if (data.customData) {
            this.customData = __assign(__assign({}, this.customData), data.customData);
        }
        if (data.lastActiveTime) {
            this.lastActiveTime = data.lastActiveTime;
        }
    };
    /**
     * 标记属性为待同步
     * @param property 属性名
     */
    NetworkUserComponent.prototype.markPropertyDirty = function (property) {
        this.hasPendingChanges = true;
        this.pendingProperties.add(property);
    };
    /**
     * 标记所有属性为待同步
     */
    NetworkUserComponent.prototype.markAllPropertiesDirty = function () {
        this.hasPendingChanges = true;
    };
    /**
     * 同步用户数据
     */
    NetworkUserComponent.prototype.sync = function () {
        var _a;
        if (!this.hasPendingChanges) {
            return;
        }
        // 获取需要同步的数据
        var syncData = this.getUserData();
        // 触发同步事件
        (_a = this.entity) === null || _a === void 0 ? void 0 : _a.emit('userSync', syncData);
        // 清除待同步标记
        this.hasPendingChanges = false;
        this.pendingProperties.clear();
    };
    /**
     * 销毁组件
     */
    NetworkUserComponent.prototype.dispose = function () {
        // 销毁逻辑
    };
    /** 组件类型 */
    NetworkUserComponent.type = 'NetworkUser';
    return NetworkUserComponent;
}(Component_1.Component));

/**
 * 动作控制系统
 * 用于管理角色的动作和动画
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { EventEmitter, type EventCallback } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import { AdvancedCharacterController } from './AdvancedCharacterController';
import { AnimationSystem } from '../../animation/AnimationSystem';
import { PhysicsSystem } from '../../physics/PhysicsSystem';
import { ActionRecorder, ActionRecording, ActionPlayback } from '../recording';

/**
 * 动作类型
 */
export enum ActionType {
  /** 基础动作 */
  BASIC = 'basic',
  /** 组合动作 */
  COMBO = 'combo',
  /** 环境交互动作 */
  ENVIRONMENT = 'environment',
  /** 物品使用动作 */
  ITEM = 'item',
  /** 战斗动作 */
  COMBAT = 'combat',
  /** 社交动作 */
  SOCIAL = 'social',
  /** 情感动作 */
  EMOTE = 'emote',
  /** 自定义动作 */
  CUSTOM = 'custom',
  /** 物理交互动作 */
  PHYSICS_INTERACTION = 'physics_interaction',
  /** 环境响应动作 */
  ENVIRONMENT_RESPONSE = 'environment_response',
  /** 姿势动作 */
  POSE = 'pose',
  /** 手势动作 */
  GESTURE = 'gesture',
  /** 面部表情动作 */
  FACIAL = 'facial',
  /** 对话动作 */
  DIALOGUE = 'dialogue',
  /** 移动动作 */
  LOCOMOTION = 'locomotion'
}

/**
 * 动作优先级
 */
export enum ActionPriority {
  /** 低优先级 */
  LOW = 0,
  /** 中优先级 */
  MEDIUM = 1,
  /** 高优先级 */
  HIGH = 2,
  /** 最高优先级 */
  CRITICAL = 3
}

/**
 * 动作数据
 */
export interface ActionData {
  /** 动作ID */
  id: string;
  /** 动作名称 */
  name: string;
  /** 动作类型 */
  type: ActionType;
  /** 动作优先级 */
  priority: ActionPriority;
  /** 动画名称 */
  animationName: string;
  /** 是否可中断 */
  interruptible: boolean;
  /** 是否循环 */
  loop: boolean;
  /** 过渡时间 */
  transitionTime: number;
  /** 动作持续时间（秒，0表示无限） */
  duration: number;
  /** 动作参数 */
  params?: Record<string, any>;
  /** 动作事件 */
  events?: ActionEvent[];
}

/**
 * 动作事件
 */
export interface ActionEvent {
  /** 事件名称 */
  name: string;
  /** 事件时间（秒） */
  time: number;
  /** 事件参数 */
  params?: Record<string, any>;
}

/**
 * 动作实例
 */
export interface ActionInstance {
  /** 动作数据 */
  data: ActionData;
  /** 实体 */
  entity: Entity;
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime: number;
  /** 是否正在播放 */
  isPlaying: boolean;
  /** 是否已完成 */
  isCompleted: boolean;
  /** 已触发的事件 */
  triggeredEvents: Set<string>;
}

/**
 * 动作控制系统配置
 */
export interface ActionControlSystemConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 最大同时动作数 */
  maxConcurrentActions?: number;
  /** 是否启用动作混合 */
  enableActionBlending?: boolean;
  /** 是否启用动作队列 */
  enableActionQueue?: boolean;
  /** 是否启用动作事件 */
  enableActionEvents?: boolean;
  /** 是否启用物理驱动动作 */
  enablePhysicsDrivenActions?: boolean;
  /** 是否启用动作录制 */
  enableActionRecording?: boolean;
  /** 是否启用动作回放 */
  enableActionPlayback?: boolean;
}

/**
 * 动作控制系统
 */
export class ActionControlSystem extends System {
  /** 系统类型 */
  public static readonly type: string = 'ActionControlSystem';

  /** 配置 */
  private config: ActionControlSystemConfig;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: ActionControlSystemConfig = {
    debug: false,
    maxConcurrentActions: 5,
    enableActionBlending: true,
    enableActionQueue: true,
    enableActionEvents: true,
    enablePhysicsDrivenActions: true,
    enableActionRecording: true,
    enableActionPlayback: true
  };

  /** 动画系统 */
  private animationSystem: AnimationSystem | null = null;

  /** 物理系统 */
  private physicsSystem: PhysicsSystem | null = null;

  /** 动作库 */
  private actionLibrary: Map<string, ActionData> = new Map();

  /** 活动动作 */
  private activeActions: Map<Entity, ActionInstance[]> = new Map();

  /** 动作队列 */
  private actionQueues: Map<Entity, ActionData[]> = new Map();

  /** 角色控制器映射 */
  private characterControllers: Map<Entity, AdvancedCharacterController> = new Map();

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 当前时间 */
  private currentTime: number = 0;

  /** 动作录制器映射 */
  private actionRecorders: Map<Entity, ActionRecorder> = new Map();

  /** 动作回放器映射 */
  private actionPlaybacks: Map<Entity, ActionPlayback> = new Map();

  /** 录制的动作 */
  private recordings: Map<string, ActionRecording> = new Map();

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: Partial<ActionControlSystemConfig> = {}) {
    super(100); // 设置优先级
    this.config = { ...ActionControlSystem.DEFAULT_CONFIG, ...config };

    if (this.config.debug) {
      Debug.log('动作控制系统初始化', this);
    }
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();

    // 获取系统
    if (this.world) {
      this.animationSystem = this.world.getSystem(AnimationSystem);
      this.physicsSystem = this.world.getSystem(PhysicsSystem);
    }
  }

  /**
   * 注册角色控制器
   * @param entity 实体
   * @param controller 控制器
   */
  public registerController(entity: Entity, controller: AdvancedCharacterController): void {
    this.characterControllers.set(entity, controller);

    // 初始化实体的动作队列
    if (this.config.enableActionQueue && !this.actionQueues.has(entity)) {
      this.actionQueues.set(entity, []);
    }

    // 初始化实体的活动动作
    if (!this.activeActions.has(entity)) {
      this.activeActions.set(entity, []);
    }

    if (this.config.debug) {
      Debug.log(`注册角色控制器: ${entity.id}`, controller);
    }
  }

  /**
   * 注册动作
   * @param action 动作数据
   */
  public registerAction(action: ActionData): void {
    this.actionLibrary.set(action.id, action);

    if (this.config.debug) {
      Debug.log(`注册动作: ${action.id}`, action);
    }
  }

  /**
   * 播放动作
   * @param entity 实体
   * @param actionId 动作ID
   * @param params 动作参数
   * @returns 是否成功播放
   */
  public playAction(entity: Entity, actionId: string, params?: Record<string, any>): boolean {
    // 获取动作数据
    const actionData = this.actionLibrary.get(actionId);
    if (!actionData) {
      if (this.config.debug) {
        Debug.warn(`动作不存在: ${actionId}`);
      }
      return false;
    }

    // 合并参数
    const mergedParams = { ...actionData.params, ...params };
    const actionWithParams = { ...actionData, params: mergedParams };

    // 检查是否有活动动作
    const activeActions = this.activeActions.get(entity) || [];

    // 如果启用动作队列，且有不可中断的高优先级动作正在播放，则将动作加入队列
    if (this.config.enableActionQueue) {
      const hasHigherPriorityAction = activeActions.some(
        action => action.isPlaying &&
                 action.data.priority > actionWithParams.priority &&
                 !action.data.interruptible
      );

      if (hasHigherPriorityAction) {
        const queue = this.actionQueues.get(entity) || [];
        queue.push(actionWithParams);
        this.actionQueues.set(entity, queue);

        if (this.config.debug) {
          Debug.log(`动作加入队列: ${actionId}`, { entity, action: actionWithParams });
        }

        return true;
      }
    }

    // 停止可中断的低优先级动作
    for (const activeAction of activeActions) {
      if (activeAction.isPlaying &&
          activeAction.data.priority <= actionWithParams.priority &&
          activeAction.data.interruptible) {
        this.stopAction(entity, activeAction.data.id);
      }
    }

    // 创建动作实例
    const actionInstance: ActionInstance = {
      data: actionWithParams,
      entity,
      startTime: this.currentTime,
      endTime: actionWithParams.duration > 0 ? this.currentTime + actionWithParams.duration : Infinity,
      isPlaying: true,
      isCompleted: false,
      triggeredEvents: new Set()
    };

    // 添加到活动动作
    activeActions.push(actionInstance);
    this.activeActions.set(entity, activeActions);

    // 播放动画
    if (this.animationSystem) {
      // 这里应该调用动画系统播放动画
    }

    // 发出动作开始事件
    this.eventEmitter.emit('actionStart', { entity, action: actionWithParams });

    if (this.config.debug) {
      Debug.log(`播放动作: ${actionId}`, { entity, action: actionWithParams });
    }

    return true;
  }

  /**
   * 停止动作
   * @param entity 实体
   * @param actionId 动作ID
   * @returns 是否成功停止
   */
  public stopAction(entity: Entity, actionId: string): boolean {
    // 实现停止动作的逻辑
    return true;
  }

  /**
   * 更新系统
   * @param deltaTime 时间增量（秒）
   */
  public update(deltaTime: number): void {
    // 更新当前时间
    this.currentTime += deltaTime;

    // 更新所有实体的动作
    for (const [entity, actions] of this.activeActions.entries()) {
      this.updateEntityActions(entity, actions, deltaTime);
    }

    // 处理动作队列
    if (this.config.enableActionQueue) {
      this.processActionQueues();
    }
  }

  /**
   * 更新实体动作
   * @param entity 实体
   * @param actions 动作实例数组
   * @param deltaTime 时间增量（秒）
   */
  private updateEntityActions(entity: Entity, actions: ActionInstance[], deltaTime: number): void {
    // 实现更新实体动作的逻辑
  }

  /**
   * 处理动作队列
   */
  private processActionQueues(): void {
    // 实现处理动作队列的逻辑
  }

  /**
   * 创建动作录制器
   * @param entity 实体
   * @param config 录制器配置
   * @returns 动作录制器
   */
  public createActionRecorder(entity: Entity, config?: any): ActionRecorder {
    if (this.actionRecorders.has(entity)) {
      return this.actionRecorders.get(entity)!;
    }

    const recorder = new ActionRecorder(entity, this, config);
    this.actionRecorders.set(entity, recorder);

    if (this.config.debug) {
      Debug.log(`创建动作录制器: ${entity.id}`);
    }

    return recorder;
  }

  /**
   * 创建动作回放器
   * @param entity 实体
   * @param config 回放器配置
   * @returns 动作回放器
   */
  public createActionPlayback(entity: Entity, config?: any): ActionPlayback {
    if (this.actionPlaybacks.has(entity)) {
      return this.actionPlaybacks.get(entity)!;
    }

    const playback = new ActionPlayback(entity, this, config);
    this.actionPlaybacks.set(entity, playback);

    if (this.config.debug) {
      Debug.log(`创建动作回放器: ${entity.id}`);
    }

    return playback;
  }

  /**
   * 开始录制动作
   * @param entity 实体
   * @param name 录制名称
   * @returns 是否成功开始录制
   */
  public startRecording(entity: Entity, name?: string): boolean {
    if (!this.config.enableActionRecording) {
      if (this.config.debug) {
        Debug.warn('动作录制功能未启用');
      }
      return false;
    }

    let recorder = this.actionRecorders.get(entity);
    if (!recorder) {
      recorder = this.createActionRecorder(entity);
    }

    return recorder.startRecording(name);
  }

  /**
   * 停止录制动作
   * @param entity 实体
   * @returns 录制数据
   */
  public stopRecording(entity: Entity): ActionRecording | null {
    const recorder = this.actionRecorders.get(entity);
    if (!recorder) {
      if (this.config.debug) {
        Debug.warn(`实体没有动作录制器: ${entity.id}`);
      }
      return null;
    }

    const recording = recorder.stopRecording();
    if (recording) {
      this.recordings.set(recording.id, recording);
    }

    return recording;
  }

  /**
   * 播放录制的动作
   * @param entity 实体
   * @param recordingId 录制ID
   * @param speed 播放速度
   * @returns 是否成功开始播放
   */
  public playRecording(entity: Entity, recordingId: string, speed: number = 1.0): boolean {
    if (!this.config.enableActionPlayback) {
      if (this.config.debug) {
        Debug.warn('动作回放功能未启用');
      }
      return false;
    }

    const recording = this.recordings.get(recordingId);
    if (!recording) {
      if (this.config.debug) {
        Debug.warn(`录制不存在: ${recordingId}`);
      }
      return false;
    }

    let playback = this.actionPlaybacks.get(entity);
    if (!playback) {
      playback = this.createActionPlayback(entity);
    }

    // 设置播放速度
    playback.setPlaybackSpeed(speed);

    // 加载录制
    playback.loadRecording(recording);

    // 开始播放
    return playback.startPlayback();
  }

  /**
   * 暂停播放
   * @param entity 实体
   * @returns 是否成功暂停
   */
  public pausePlayback(entity: Entity): boolean {
    const playback = this.actionPlaybacks.get(entity);
    if (!playback) {
      if (this.config.debug) {
        Debug.warn(`实体没有动作回放器: ${entity.id}`);
      }
      return false;
    }

    return playback.pausePlayback();
  }

  /**
   * 继续播放
   * @param entity 实体
   * @returns 是否成功继续
   */
  public resumePlayback(entity: Entity): boolean {
    const playback = this.actionPlaybacks.get(entity);
    if (!playback) {
      if (this.config.debug) {
        Debug.warn(`实体没有动作回放器: ${entity.id}`);
      }
      return false;
    }

    return playback.startPlayback();
  }

  /**
   * 停止播放
   * @param entity 实体
   * @returns 是否成功停止
   */
  public stopPlayback(entity: Entity): boolean {
    const playback = this.actionPlaybacks.get(entity);
    if (!playback) {
      if (this.config.debug) {
        Debug.warn(`实体没有动作回放器: ${entity.id}`);
      }
      return false;
    }

    return playback.stopPlayback();
  }

  /**
   * 获取录制
   * @param recordingId 录制ID
   * @returns 录制数据
   */
  public getRecording(recordingId: string): ActionRecording | undefined {
    return this.recordings.get(recordingId);
  }

  /**
   * 获取所有录制
   * @returns 录制数据数组
   */
  public getAllRecordings(): ActionRecording[] {
    return Array.from(this.recordings.values());
  }

  /**
   * 添加录制
   * @param recording 录制数据
   */
  public addRecording(recording: ActionRecording): void {
    this.recordings.set(recording.id, recording);

    if (this.config.debug) {
      Debug.log(`添加录制: ${recording.id}`);
    }
  }

  /**
   * 删除录制
   * @param recordingId 录制ID
   * @returns 是否成功删除
   */
  public removeRecording(recordingId: string): boolean {
    const result = this.recordings.delete(recordingId);

    if (result && this.config.debug) {
      Debug.log(`删除录制: ${recordingId}`);
    }

    return result;
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public on(event: string, callback: EventCallback): this {
    this.eventEmitter.on(event, callback);
    return this;
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public off(event: string, callback?: EventCallback): this {
    this.eventEmitter.off(event, callback);
    return this;
  }
}

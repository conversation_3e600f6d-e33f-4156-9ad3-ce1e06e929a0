/**
 * 视觉脚本调试节点
 * 提供调试、性能分析和监控功能
 */
import type { Entity } from '../../core/Entity';
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';
import { VisualScriptDebugger } from '../debug/VisualScriptDebugger';
import { BreakpointType } from '../debug/BreakpointManager';
import { StepType } from '../debug/StepType';
import { PerformanceAnalyzer } from '../debug/PerformanceAnalyzer';

/**
 * 断点节点
 * 在执行到该节点时暂停执行
 */
export class BreakpointNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });
    
    // 添加输入数据插槽
    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '断点条件',
      defaultValue: true
    });
    
    this.addInput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '断点消息',
      defaultValue: ''
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });
  }
  
  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const condition = this.getInputValue('condition') as boolean;
    const message = this.getInputValue('message') as string;
    
    // 如果条件为真，触发断点
    if (condition) {
      // 获取调试器
      const debugger_ = this.graph.getDebugger();
      
      if (debugger_) {
        // 记录日志
        if (message) {
          console.log(`[断点] ${message}`);
        }
        
        // 暂停执行
        debugger_.pause();
      }
    }
    
    // 触发输出流程
    this.triggerFlow('flow');
    
    return null;
  }
}

/**
 * 日志节点
 * 输出日志信息
 */
export class LogNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });
    
    // 添加输入数据插槽
    this.addInput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '日志消息'
    });
    
    this.addInput({
      name: 'level',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '日志级别',
      defaultValue: 'log'
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });
  }
  
  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const message = this.getInputValue('message');
    const level = this.getInputValue('level') as string;
    
    // 根据日志级别输出日志
    switch (level) {
      case 'error':
        console.error(`[视觉脚本] ${message}`);
        break;
      case 'warn':
        console.warn(`[视觉脚本] ${message}`);
        break;
      case 'info':
        console.info(`[视觉脚本] ${message}`);
        break;
      case 'debug':
        console.debug(`[视觉脚本] ${message}`);
        break;
      default:
        console.log(`[视觉脚本] ${message}`);
        break;
    }
    
    // 触发输出流程
    this.triggerFlow('flow');
    
    return null;
  }
}

/**
 * 性能计时节点
 * 测量代码执行时间
 */
export class PerformanceTimerNode extends FlowNode {
  /** 开始时间 */
  private startTime: number = 0;
  
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'start',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '开始计时'
    });
    
    this.addInput({
      name: 'end',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '结束计时'
    });
    
    // 添加输入数据插槽
    this.addInput({
      name: 'label',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '计时标签',
      defaultValue: '性能计时'
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'startFlow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '开始计时后'
    });
    
    this.addOutput({
      name: 'endFlow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '结束计时后'
    });
    
    // 添加输出数据插槽
    this.addOutput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '持续时间（毫秒）'
    });
  }
  
  /**
   * 执行节点
   * @param inputName 输入名称
   * @returns 执行结果
   */
  public execute(inputName: string): any {
    // 获取输入值
    const label = this.getInputValue('label') as string;
    
    // 根据输入名称执行不同操作
    if (inputName === 'start') {
      // 开始计时
      this.startTime = performance.now();
      
      // 触发开始流程
      this.triggerFlow('startFlow');
      
      return null;
    } else if (inputName === 'end') {
      // 结束计时
      const endTime = performance.now();
      const duration = endTime - this.startTime;
      
      // 输出计时结果
      console.log(`[性能计时] ${label}: ${duration.toFixed(2)}ms`);
      
      // 设置输出值
      this.setOutputValue('duration', duration);
      
      // 触发结束流程
      this.triggerFlow('endFlow');
      
      return duration;
    }
    
    return null;
  }
}

/**
 * 变量监视节点
 * 监视变量的变化
 */
export class VariableWatchNode extends FlowNode {
  /** 上一个值 */
  private previousValue: any = undefined;
  
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });
    
    // 添加输入数据插槽
    this.addInput({
      name: 'variable',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '要监视的变量'
    });
    
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '变量名称',
      defaultValue: '变量'
    });
    
    this.addInput({
      name: 'logChanges',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否记录变化',
      defaultValue: true
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });
    
    this.addOutput({
      name: 'changed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '变量变化时'
    });
    
    // 添加输出数据插槽
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '变量值'
    });
    
    this.addOutput({
      name: 'previousValue',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '上一个值'
    });
    
    this.addOutput({
      name: 'hasChanged',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否变化'
    });
  }
  
  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const variable = this.getInputValue('variable');
    const name = this.getInputValue('name') as string;
    const logChanges = this.getInputValue('logChanges') as boolean;
    
    // 检查变量是否变化
    const hasChanged = !this.areValuesEqual(variable, this.previousValue);
    
    // 设置输出值
    this.setOutputValue('value', variable);
    this.setOutputValue('previousValue', this.previousValue);
    this.setOutputValue('hasChanged', hasChanged);
    
    // 如果变量变化且需要记录变化，输出日志
    if (hasChanged && logChanges) {
      console.log(`[变量监视] ${name} 变化:`, {
        from: this.previousValue,
        to: variable
      });
      
      // 触发变化流程
      this.triggerFlow('changed');
    }
    
    // 更新上一个值
    this.previousValue = this.cloneValue(variable);
    
    // 触发输出流程
    this.triggerFlow('flow');
    
    return variable;
  }
  
  /**
   * 比较两个值是否相等
   * @param a 第一个值
   * @param b 第二个值
   * @returns 是否相等
   */
  private areValuesEqual(a: any, b: any): boolean {
    // 如果两个值都是对象或数组，使用JSON.stringify比较
    if (typeof a === 'object' && a !== null && typeof b === 'object' && b !== null) {
      try {
        return JSON.stringify(a) === JSON.stringify(b);
      } catch (error) {
        // 如果JSON.stringify失败，使用简单比较
        return a === b;
      }
    }
    
    // 否则使用简单比较
    return a === b;
  }
  
  /**
   * 克隆值
   * @param value 要克隆的值
   * @returns 克隆后的值
   */
  private cloneValue(value: any): any {
    if (typeof value === 'object' && value !== null) {
      try {
        return JSON.parse(JSON.stringify(value));
      } catch (error) {
        // 如果JSON操作失败，返回原值
        return value;
      }
    }
    
    return value;
  }
}

/**
 * 断言节点
 * 验证条件是否为真
 */
export class AssertNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });
    
    // 添加输入数据插槽
    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '断言条件'
    });
    
    this.addInput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '断言消息',
      defaultValue: '断言失败'
    });
    
    this.addInput({
      name: 'throwError',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否抛出错误',
      defaultValue: false
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '断言成功'
    });
    
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '断言失败'
    });
    
    // 添加输出数据插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '断言结果'
    });
  }
  
  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const condition = this.getInputValue('condition') as boolean;
    const message = this.getInputValue('message') as string;
    const throwError = this.getInputValue('throwError') as boolean;
    
    // 设置输出值
    this.setOutputValue('result', condition);
    
    // 如果条件为真，断言成功
    if (condition) {
      // 触发成功流程
      this.triggerFlow('success');
    } else {
      // 断言失败
      console.error(`[断言] ${message}`);
      
      // 如果需要抛出错误，抛出错误
      if (throwError) {
        throw new Error(`断言失败: ${message}`);
      }
      
      // 触发失败流程
      this.triggerFlow('fail');
    }
    
    return condition;
  }
}

/**
 * 注册调试节点
 * @param registry 节点注册表
 */
export function registerDebugNodes(registry: NodeRegistry): void {
  // 注册断点节点
  registry.registerNodeType({
    type: 'debug/breakpoint',
    category: NodeCategory.DEBUG,
    constructor: BreakpointNode,
    label: '断点',
    description: '在执行到该节点时暂停执行',
    icon: 'breakpoint',
    color: '#F44336',
    tags: ['debug', 'breakpoint', 'pause']
  });
  
  // 注册日志节点
  registry.registerNodeType({
    type: 'debug/log',
    category: NodeCategory.DEBUG,
    constructor: LogNode,
    label: '日志',
    description: '输出日志信息',
    icon: 'log',
    color: '#F44336',
    tags: ['debug', 'log', 'console']
  });
  
  // 注册性能计时节点
  registry.registerNodeType({
    type: 'debug/performanceTimer',
    category: NodeCategory.DEBUG,
    constructor: PerformanceTimerNode,
    label: '性能计时',
    description: '测量代码执行时间',
    icon: 'timer',
    color: '#F44336',
    tags: ['debug', 'performance', 'timer']
  });
  
  // 注册变量监视节点
  registry.registerNodeType({
    type: 'debug/variableWatch',
    category: NodeCategory.DEBUG,
    constructor: VariableWatchNode,
    label: '变量监视',
    description: '监视变量的变化',
    icon: 'watch',
    color: '#F44336',
    tags: ['debug', 'variable', 'watch']
  });
  
  // 注册断言节点
  registry.registerNodeType({
    type: 'debug/assert',
    category: NodeCategory.DEBUG,
    constructor: AssertNode,
    label: '断言',
    description: '验证条件是否为真',
    icon: 'assert',
    color: '#F44336',
    tags: ['debug', 'assert', 'condition']
  });
}

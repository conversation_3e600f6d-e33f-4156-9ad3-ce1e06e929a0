/**
 * 视觉脚本网络节点
 * 提供网络通信和多用户交互相关的节点
 */
import type { Entity } from '../../core/Entity';
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { EventNode } from '../nodes/EventNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';
import { NetworkSystem } from '../../network/NetworkSystem';
import { NetworkManager } from '../../network/NetworkManager';
import { NetworkMessage } from '../../network/NetworkMessage';
import { NetworkUser } from '../../network/NetworkUser';
import type { NetworkEntity } from '../../network/NetworkEntity';
import { MessageType } from '../../network/MessageType';
import { WebRTCConnection } from '../../network/WebRTCConnection';
import { UserRole } from '../../network/UserRole';

// 导入其他网络节点模块
import { registerNetworkProtocolNodes } from './NetworkProtocolNodes';
import { registerWebRTCNodes } from './WebRTCNodes';
import { registerNetworkSecurityNodes } from './NetworkSecurityNodes';

/**
 * 网络连接节点
 * 连接到网络服务器
 */
export class ConnectToServerNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'serverUrl',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '服务器URL',
      defaultValue: 'ws://localhost:8080'
    });

    this.addInput({
      name: 'roomId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '房间ID',
      defaultValue: '',
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'connected',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否已连接'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const serverUrl = this.getInputValue('serverUrl') as string;
    const roomId = this.getInputValue('roomId') as string;

    // 获取网络系统
    const networkSystem = this.graph.getWorld().getSystem(NetworkSystem);
    if (!networkSystem) {
      this.setOutputValue('connected', false);
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 连接到服务器
      networkSystem.connect(serverUrl, roomId || undefined);

      // 监听连接事件
      networkSystem.once('connected', () => {
        this.setOutputValue('connected', true);
        this.triggerFlow('success');
      });

      networkSystem.once('error', () => {
        this.setOutputValue('connected', false);
        this.triggerFlow('fail');
      });

      return true;
    } catch (error) {
      this.setOutputValue('connected', false);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 发送网络消息节点
 * 向其他用户发送网络消息
 */
export class SendNetworkMessageNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '消息内容'
    });

    this.addInput({
      name: 'targetUserId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '目标用户ID（为空则发送给所有用户）',
      defaultValue: '',
      optional: true
    });

    this.addInput({
      name: 'reliable',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否可靠传输',
      defaultValue: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否发送成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const message = this.getInputValue('message');
    const targetUserId = this.getInputValue('targetUserId') as string;
    const reliable = this.getInputValue('reliable') as boolean;

    // 获取网络系统
    const networkSystem = this.graph.getWorld().getSystem(NetworkSystem);
    if (!networkSystem) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 创建网络消息
      const networkMessage = new NetworkMessage({
        type: MessageType.CUSTOM,
        data: message,
        reliable
      });

      // 发送消息
      if (targetUserId) {
        // 发送给特定用户
        networkSystem.sendMessageToUser(targetUserId, networkMessage);
      } else {
        // 发送给所有用户
        networkSystem.broadcastMessage(networkMessage);
      }

      this.setOutputValue('success', true);
    } catch (error) {
      this.setOutputValue('success', false);
    }

    // 触发输出流程
    this.triggerFlow('flow');
    return true;
  }
}

/**
 * 网络消息接收事件节点
 * 当接收到网络消息时触发
 */
export class OnNetworkMessageNode extends EventNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '消息内容'
    });

    this.addOutput({
      name: 'senderId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '发送者ID'
    });
  }

  /**
   * 初始化事件
   */
  public initialize(): void {
    // 获取网络系统
    const networkSystem = this.graph.getWorld().getSystem(NetworkSystem);
    if (!networkSystem) {
      return;
    }

    // 监听网络消息事件
    networkSystem.on('message', this.onNetworkMessage.bind(this));
  }

  /**
   * 网络消息处理
   * @param message 网络消息
   * @param senderId 发送者ID
   */
  private onNetworkMessage(message: NetworkMessage, senderId: string): void {
    // 设置输出值
    this.setOutputValue('message', message.data);
    this.setOutputValue('senderId', senderId);

    // 触发输出流程
    this.triggerFlow('flow');
  }

  /**
   * 清理事件
   */
  public cleanup(): void {
    // 获取网络系统
    const networkSystem = this.graph.getWorld().getSystem(NetworkSystem);
    if (!networkSystem) {
      return;
    }

    // 移除事件监听
    networkSystem.off('message', this.onNetworkMessage.bind(this));
  }
}

/**
 * 注册网络节点
 * @param registry 节点注册表
 */
export function registerNetworkNodes(registry: NodeRegistry): void {
  // 注册连接到服务器节点
  registry.registerNodeType({
    type: 'network/connectToServer',
    category: NodeCategory.NETWORK,
    constructor: ConnectToServerNode,
    label: '连接到服务器',
    description: '连接到网络服务器',
    icon: 'connect',
    color: '#00BCD4',
    tags: ['network', 'connect', 'server']
  });

  // 注册发送网络消息节点
  registry.registerNodeType({
    type: 'network/sendMessage',
    category: NodeCategory.NETWORK,
    constructor: SendNetworkMessageNode,
    label: '发送网络消息',
    description: '向其他用户发送网络消息',
    icon: 'send',
    color: '#00BCD4',
    tags: ['network', 'message', 'send']
  });

  // 注册网络消息接收事件节点
  registry.registerNodeType({
    type: 'network/events/onMessage',
    category: NodeCategory.NETWORK,
    constructor: OnNetworkMessageNode,
    label: '接收网络消息',
    description: '当接收到网络消息时触发',
    icon: 'receive',
    color: '#00BCD4',
    tags: ['network', 'message', 'receive', 'event']
  });

  // 注册网络协议节点
  registerNetworkProtocolNodes(registry);

  // 注册WebRTC节点
  registerWebRTCNodes(registry);

  // 注册网络安全节点
  registerNetworkSecurityNodes(registry);

  console.log('已注册所有网络节点类型');
}

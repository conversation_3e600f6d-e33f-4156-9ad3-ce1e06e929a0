"use strict";
/**
 * 动画系统
 * 管理和更新场景中的所有动画
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnimationSystem = void 0;
var THREE = require("three");
var System_1 = require("../core/System");
var AnimationClip_1 = require("./AnimationClip");
var Animator_1 = require("./Animator");
var EventEmitter_1 = require("../utils/EventEmitter");
var Time_1 = require("../utils/Time");
/**
 * 动画系统
 * 管理和更新场景中的所有动画
 */
var AnimationSystem = exports.AnimationSystem = /** @class */ (function (_super) {
    __extends(AnimationSystem, _super);
    /**
     * 构造函数
     * @param config 配置
     */
    function AnimationSystem(config) {
        if (config === void 0) { config = {}; }
        var _this = _super.call(this, 200) || this;
        /** 动画控制器映射 */
        _this.animators = new Map();
        /** 动画混合器映射 */
        _this.mixers = new Map();
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 上次更新时间 */
        _this.lastUpdateTime = 0;
        /** 动画缓存 */
        _this.clipCache = new Map();
        /** 对象池 */
        _this.objectPool = {
            keyframes: [],
            clips: [],
            mixers: []
        };
        /** 性能统计 */
        _this.stats = {
            updateTime: 0,
            activeAnimators: 0,
            activeMixers: 0,
            cacheHits: 0,
            cacheMisses: 0,
            objectPoolHits: 0,
            objectPoolMisses: 0
        };
        // 设置配置
        _this.config = {
            enabled: config.enabled !== undefined ? config.enabled : true,
            debug: config.debug || false,
            updateFrequency: config.updateFrequency || 16,
            useCache: config.useCache !== undefined ? config.useCache : true,
            cacheSize: config.cacheSize || 100,
            useObjectPool: config.useObjectPool !== undefined ? config.useObjectPool : true,
            objectPoolSize: config.objectPoolSize || 50,
            useBatchProcessing: config.useBatchProcessing !== undefined ? config.useBatchProcessing : true,
            batchSize: config.batchSize || 10,
            useGPUAcceleration: config.useGPUAcceleration !== undefined ? config.useGPUAcceleration : true,
            useWorker: config.useWorker !== undefined ? config.useWorker : false
        };
        // 优先级已在super()中设置
        // 初始化对象池
        if (_this.config.useObjectPool) {
            _this.initObjectPool();
        }
        return _this;
    }
    /**
     * 初始化对象池
     */
    AnimationSystem.prototype.initObjectPool = function () {
        var size = this.config.objectPoolSize || 50;
        for (var i = 0; i < size; i++) {
            this.objectPool.clips.push(new AnimationClip_1.AnimationClip());
            this.objectPool.mixers.push(new THREE.AnimationMixer(new THREE.Object3D()));
        }
    };
    /**
     * 创建动画控制器
     * @param entity 实体
     * @param clips 动画片段
     * @returns 动画控制器
     */
    AnimationSystem.prototype.createAnimator = function (entity, clips) {
        if (clips === void 0) { clips = []; }
        // 检查是否已存在
        if (this.animators.has(entity)) {
            return this.animators.get(entity);
        }
        // 创建动画控制器
        var animator = new Animator_1.Animator();
        // 添加动画片段
        for (var _i = 0, clips_1 = clips; _i < clips_1.length; _i++) {
            var clip = clips_1[_i];
            animator.addClip(clip);
        }
        // 添加到映射
        this.animators.set(entity, animator);
        // 创建混合器
        var transformComponent = entity.getComponent('Transform');
        var object3D = (transformComponent === null || transformComponent === void 0 ? void 0 : transformComponent.getObject3D()) || new THREE.Object3D();
        var mixer = new THREE.AnimationMixer(object3D);
        this.mixers.set(entity, mixer);
        return animator;
    };
    /**
     * 获取动画控制器
     * @param entity 实体
     * @returns 动画控制器
     */
    AnimationSystem.prototype.getAnimator = function (entity) {
        return this.animators.get(entity) || null;
    };
    /**
     * 移除动画控制器
     * @param entity 实体
     */
    AnimationSystem.prototype.removeAnimator = function (entity) {
        this.animators.delete(entity);
        this.mixers.delete(entity);
    };
    /**
     * 更新系统
     * @param deltaTime 时间增量
     */
    AnimationSystem.prototype.update = function (deltaTime) {
        if (!this.config.enabled)
            return;
        // 检查是否需要更新
        var currentTime = Time_1.Time.now();
        if (currentTime - this.lastUpdateTime < this.config.updateFrequency) {
            return;
        }
        this.lastUpdateTime = currentTime;
        // 更新性能统计
        this.stats.activeAnimators = this.animators.size;
        this.stats.activeMixers = this.mixers.size;
        // 更新所有混合器
        this.mixers.forEach(function (mixer, entity) {
            mixer.update(deltaTime);
        });
        // 更新所有动画控制器
        this.animators.forEach(function (animator, entity) {
            animator.update(deltaTime);
        });
        // 发出更新事件
        this.eventEmitter.emit('update', deltaTime);
    };
    /**
     * 获取性能统计
     * @returns 性能统计
     */
    AnimationSystem.prototype.getStats = function () {
        return this.stats;
    };
    /**
     * 清除缓存
     */
    AnimationSystem.prototype.clearCache = function () {
        this.clipCache.clear();
        this.stats.cacheHits = 0;
        this.stats.cacheMisses = 0;
    };
    /**
     * 重置对象池
     */
    AnimationSystem.prototype.resetObjectPool = function () {
        this.objectPool.keyframes = [];
        this.objectPool.clips = [];
        this.objectPool.mixers = [];
        this.initObjectPool();
        this.stats.objectPoolHits = 0;
        this.stats.objectPoolMisses = 0;
    };
    /** 系统名称 */
    AnimationSystem.systemName = 'AnimationSystem';
    return AnimationSystem;
}(System_1.System));

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollisionEvent = exports.CollisionEventType = void 0;
/**
 * 碰撞事件类
 * 用于表示物理碰撞事件
 */
var THREE = require("three");
/**
 * 碰撞事件类型
 */
var CollisionEventType;
(function (CollisionEventType) {
    /** 碰撞开始 */
    CollisionEventType["BEGIN"] = "collisionBegin";
    /** 碰撞持续 */
    CollisionEventType["STAY"] = "collisionStay";
    /** 碰撞结束 */
    CollisionEventType["END"] = "collisionEnd";
    /** 触发器进入 */
    CollisionEventType["TRIGGER_ENTER"] = "triggerEnter";
    /** 触发器停留 */
    CollisionEventType["TRIGGER_STAY"] = "triggerStay";
    /** 触发器离开 */
    CollisionEventType["TRIGGER_EXIT"] = "triggerExit";
})(CollisionEventType || (exports.CollisionEventType = CollisionEventType = {}));
/**
 * 碰撞事件
 */
var CollisionEvent = /** @class */ (function () {
    /**
     * 创建碰撞事件
     * @param data 碰撞事件数据
     */
    function CollisionEvent(data) {
        this.type = data.type;
        this.entityA = data.entityA;
        this.entityB = data.entityB;
        this.contactPoint = data.contactPoint;
        this.contactNormal = data.contactNormal;
        this.impulse = data.impulse;
        this.relativeVelocity = data.relativeVelocity;
        this.time = data.time;
        this.cannonEvent = data.cannonEvent;
    }
    /**
     * 获取碰撞的另一个实体
     * @param entity 当前实体
     * @returns 另一个实体
     */
    CollisionEvent.prototype.getOtherEntity = function (entity) {
        return this.entityA === entity ? this.entityB : this.entityA;
    };
    /**
     * 从CANNON碰撞事件创建碰撞事件
     * @param type 碰撞类型
     * @param event CANNON碰撞事件
     * @param entityA 实体A
     * @param entityB 实体B
     * @param time 碰撞时间
     * @returns 碰撞事件
     */
    CollisionEvent.fromCannonEvent = function (type, event, entityA, entityB, time) {
        // 获取第一个接触点
        var contact = event.contact;
        // 创建碰撞事件数据
        var data = {
            type: type,
            entityA: entityA,
            entityB: entityB,
            contactPoint: contact ? new THREE.Vector3(contact.bi.getPosition().x + contact.ri.x, contact.bi.getPosition().y + contact.ri.y, contact.bi.getPosition().z + contact.ri.z) : new THREE.Vector3(),
            contactNormal: contact ? new THREE.Vector3(contact.ni.x, contact.ni.y, contact.ni.z) : new THREE.Vector3(),
            impulse: contact ? (contact.impulse || 0) : 0,
            relativeVelocity: new THREE.Vector3(),
            time: time,
            cannonEvent: event
        };
        return new CollisionEvent(data);
    };
    return CollisionEvent;
}());
exports.CollisionEvent = CollisionEvent;

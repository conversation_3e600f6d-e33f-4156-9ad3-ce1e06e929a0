"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkAdaptiveController = exports.AdaptiveStrategy = void 0;
/**
 * 网络自适应控制器
 * 用于根据网络质量自动调整网络参数
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var Debug_1 = require("../utils/Debug");
var NetworkQualityMonitor_1 = require("./NetworkQualityMonitor");
/**
 * 网络自适应策略
 */
var AdaptiveStrategy;
(function (AdaptiveStrategy) {
    /** 保守策略 - 优先保证稳定性 */
    AdaptiveStrategy["CONSERVATIVE"] = "conservative";
    /** 平衡策略 - 平衡稳定性和性能 */
    AdaptiveStrategy["BALANCED"] = "balanced";
    /** 激进策略 - 优先保证性能 */
    AdaptiveStrategy["AGGRESSIVE"] = "aggressive";
    /** 自动策略 - 根据网络状况自动选择 */
    AdaptiveStrategy["AUTO"] = "auto";
})(AdaptiveStrategy || (exports.AdaptiveStrategy = AdaptiveStrategy = {}));
/**
 * 网络自适应控制器
 * 用于根据网络质量自动调整网络参数
 */
var NetworkAdaptiveController = /** @class */ (function (_super) {
    __extends(NetworkAdaptiveController, _super);
    /**
     * 创建网络自适应控制器
     * @param initialParams 初始网络参数
     * @param config 配置
     */
    function NetworkAdaptiveController(initialParams, config) {
        if (config === void 0) { config = {}; }
        var _this = _super.call(this) || this;
        /** 最近的网络质量数据 */
        _this.latestNetworkQuality = null;
        /** 带宽控制器 */
        _this.bandwidthController = null;
        /** 实体同步管理器 */
        _this.entitySyncManager = null;
        /** 调整定时器ID */
        _this.adjustTimerId = null;
        /** 历史记录 */
        _this.history = [];
        /** 网络质量历史 */
        _this.qualityHistory = [];
        /** 网络质量趋势 */
        _this.qualityTrend = 'stable';
        /** 上次调整时间 */
        _this.lastAdjustTime = 0;
        /** 调整计数器 */
        _this.adjustCounter = 0;
        // 默认配置
        _this.config = __assign({ strategy: AdaptiveStrategy.BALANCED, adjustInterval: 5000, enableAutoAdjust: true, enableHistory: true, historySize: 20, enableDebugLog: false, enableSmoothTransition: true, smoothFactor: 0.3, enablePredictiveAdjustment: true, predictiveWindowSize: 10000 }, config);
        // 初始化当前参数
        _this.currentParams = __assign({}, initialParams);
        // 如果启用自动调整，则启动调整定时器
        if (_this.config.enableAutoAdjust) {
            _this.startAutoAdjust();
        }
        return _this;
    }
    /**
     * 设置带宽控制器
     * @param controller 带宽控制器
     */
    NetworkAdaptiveController.prototype.setBandwidthController = function (controller) {
        this.bandwidthController = controller;
    };
    /**
     * 设置实体同步管理器
     * @param manager 实体同步管理器
     */
    NetworkAdaptiveController.prototype.setEntitySyncManager = function (manager) {
        this.entitySyncManager = manager;
    };
    /**
     * 设置网络质量数据
     * @param quality 网络质量数据
     */
    NetworkAdaptiveController.prototype.setNetworkQuality = function (quality) {
        this.latestNetworkQuality = quality;
        // 添加到历史记录
        if (this.config.enableHistory) {
            this.qualityHistory.unshift(quality);
            // 限制历史记录大小
            if (this.qualityHistory.length > this.config.historySize) {
                this.qualityHistory.pop();
            }
            // 更新网络质量趋势
            this.updateQualityTrend();
        }
        // 如果启用预测性调整，则检查是否需要立即调整
        if (this.config.enablePredictiveAdjustment && this.shouldAdjustImmediately()) {
            this.adjustNetworkParams();
        }
    };
    /**
     * 启动自动调整
     */
    NetworkAdaptiveController.prototype.startAutoAdjust = function () {
        var _this = this;
        if (this.adjustTimerId !== null) {
            return;
        }
        this.adjustTimerId = window.setInterval(function () {
            _this.adjustNetworkParams();
        }, this.config.adjustInterval);
    };
    /**
     * 停止自动调整
     */
    NetworkAdaptiveController.prototype.stopAutoAdjust = function () {
        if (this.adjustTimerId !== null) {
            clearInterval(this.adjustTimerId);
            this.adjustTimerId = null;
        }
    };
    /**
     * 调整网络参数
     */
    NetworkAdaptiveController.prototype.adjustNetworkParams = function () {
        // 如果没有网络质量数据，则不调整
        if (!this.latestNetworkQuality) {
            return;
        }
        // 记录调整前的参数
        var beforeParams = __assign({}, this.currentParams);
        // 根据策略调整参数
        var reason = '';
        switch (this.config.strategy) {
            case AdaptiveStrategy.CONSERVATIVE:
                reason = this.adjustConservative();
                break;
            case AdaptiveStrategy.BALANCED:
                reason = this.adjustBalanced();
                break;
            case AdaptiveStrategy.AGGRESSIVE:
                reason = this.adjustAggressive();
                break;
            case AdaptiveStrategy.AUTO:
                reason = this.adjustAuto();
                break;
        }
        // 应用参数
        this.applyNetworkParams();
        // 记录调整后的参数
        var afterParams = __assign({}, this.currentParams);
        // 添加到历史记录
        if (this.config.enableHistory) {
            var adjustment = {
                timestamp: Date.now(),
                networkQuality: __assign({}, this.latestNetworkQuality),
                beforeParams: beforeParams,
                afterParams: afterParams,
                reason: reason,
            };
            this.history.unshift(adjustment);
            // 限制历史记录大小
            if (this.history.length > this.config.historySize) {
                this.history.pop();
            }
        }
        // 更新调整计数器和时间
        this.adjustCounter++;
        this.lastAdjustTime = Date.now();
        // 触发调整事件
        this.emit('paramsAdjusted', {
            params: afterParams,
            quality: this.latestNetworkQuality,
            reason: reason,
        });
        // 输出调试日志
        if (this.config.enableDebugLog) {
            Debug_1.Debug.log('NetworkAdaptiveController', "\u8C03\u6574\u7F51\u7EDC\u53C2\u6570: ".concat(reason));
            Debug_1.Debug.log('NetworkAdaptiveController', "\u7F51\u7EDC\u8D28\u91CF: ".concat(this.latestNetworkQuality.level));
            Debug_1.Debug.log('NetworkAdaptiveController', "\u540C\u6B65\u95F4\u9694: ".concat(beforeParams.syncInterval, " -> ").concat(afterParams.syncInterval));
        }
    };
    /**
     * 应用网络参数
     */
    NetworkAdaptiveController.prototype.applyNetworkParams = function () {
        // 应用带宽控制
        if (this.bandwidthController) {
            this.bandwidthController.setMaxUploadBandwidth(this.currentParams.maxUploadBandwidth);
            this.bandwidthController.setMaxDownloadBandwidth(this.currentParams.maxDownloadBandwidth);
        }
        // 应用实体同步参数
        if (this.entitySyncManager) {
            // 由于 EntitySyncManager 没有 setConfig 方法，我们需要通过其他方式应用配置
            // 这里我们可以触发一个事件来通知配置变更
            this.emit('networkParamsChanged', {
                syncInterval: this.currentParams.syncInterval,
                useCompression: this.currentParams.compressionLevel > 0,
                compressionLevel: this.currentParams.compressionLevel,
                useDeltaSync: this.currentParams.useDeltaSync,
                usePrioritySync: this.currentParams.usePrioritySync,
                useSpatialPartitioning: this.currentParams.useSpatialPartitioning,
                useInterpolation: this.currentParams.useInterpolation,
                interpolationFactor: this.currentParams.interpolationFactor,
                useExtrapolation: this.currentParams.usePrediction,
                extrapolationTime: this.currentParams.predictionTime,
            });
        }
    };
    /**
     * 保守策略调整
     * @returns 调整原因
     */
    NetworkAdaptiveController.prototype.adjustConservative = function () {
        var quality = this.latestNetworkQuality;
        // 根据网络质量调整参数
        switch (quality.level) {
            case NetworkQualityMonitor_1.NetworkQualityLevel.EXCELLENT:
                // 网络质量极好，适度提高性能
                this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 150);
                this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 5);
                this.currentParams.useDeltaSync = true;
                this.currentParams.usePrediction = true;
                this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 100);
                this.currentParams.useInterpolation = true;
                this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.5);
                this.currentParams.usePrioritySync = true;
                this.currentParams.useSpatialPartitioning = true;
                this.currentParams.useJitterBuffer = false;
                return '网络质量极好，适度提高性能';
            case NetworkQualityMonitor_1.NetworkQualityLevel.GOOD:
                // 网络质量良好，保持平衡
                this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 200);
                this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 6);
                this.currentParams.useDeltaSync = true;
                this.currentParams.usePrediction = true;
                this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 150);
                this.currentParams.useInterpolation = true;
                this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.6);
                this.currentParams.usePrioritySync = true;
                this.currentParams.useSpatialPartitioning = true;
                this.currentParams.useJitterBuffer = false;
                return '网络质量良好，保持平衡';
            case NetworkQualityMonitor_1.NetworkQualityLevel.MEDIUM:
                // 网络质量一般，增加稳定性
                this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 250);
                this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 7);
                this.currentParams.useDeltaSync = true;
                this.currentParams.usePrediction = true;
                this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 200);
                this.currentParams.useInterpolation = true;
                this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.7);
                this.currentParams.usePrioritySync = true;
                this.currentParams.useSpatialPartitioning = true;
                this.currentParams.useJitterBuffer = true;
                this.currentParams.jitterBufferSize = this.smoothAdjust(this.currentParams.jitterBufferSize, 100);
                return '网络质量一般，增加稳定性';
            case NetworkQualityMonitor_1.NetworkQualityLevel.BAD:
                // 网络质量差，大幅增加稳定性
                this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 350);
                this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 8);
                this.currentParams.useDeltaSync = true;
                this.currentParams.usePrediction = true;
                this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 300);
                this.currentParams.useInterpolation = true;
                this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.8);
                this.currentParams.usePrioritySync = true;
                this.currentParams.useSpatialPartitioning = true;
                this.currentParams.useJitterBuffer = true;
                this.currentParams.jitterBufferSize = this.smoothAdjust(this.currentParams.jitterBufferSize, 150);
                return '网络质量差，大幅增加稳定性';
            case NetworkQualityMonitor_1.NetworkQualityLevel.VERY_BAD:
                // 网络质量极差，最大化稳定性
                this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 500);
                this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 9);
                this.currentParams.useDeltaSync = true;
                this.currentParams.usePrediction = true;
                this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 400);
                this.currentParams.useInterpolation = true;
                this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.9);
                this.currentParams.usePrioritySync = true;
                this.currentParams.useSpatialPartitioning = true;
                this.currentParams.useJitterBuffer = true;
                this.currentParams.jitterBufferSize = this.smoothAdjust(this.currentParams.jitterBufferSize, 200);
                return '网络质量极差，最大化稳定性';
            default:
                return '未知网络质量，保持当前设置';
        }
    };
    /**
     * 平衡策略调整
     * @returns 调整原因
     */
    NetworkAdaptiveController.prototype.adjustBalanced = function () {
        var quality = this.latestNetworkQuality;
        // 根据网络质量调整参数
        switch (quality.level) {
            case NetworkQualityMonitor_1.NetworkQualityLevel.EXCELLENT:
                // 网络质量极好，提高性能
                this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 100);
                this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 4);
                this.currentParams.useDeltaSync = true;
                this.currentParams.usePrediction = true;
                this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 80);
                this.currentParams.useInterpolation = true;
                this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.4);
                this.currentParams.usePrioritySync = true;
                this.currentParams.useSpatialPartitioning = true;
                this.currentParams.useJitterBuffer = false;
                return '网络质量极好，提高性能';
            case NetworkQualityMonitor_1.NetworkQualityLevel.GOOD:
                // 网络质量良好，适度提高性能
                this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 150);
                this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 5);
                this.currentParams.useDeltaSync = true;
                this.currentParams.usePrediction = true;
                this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 120);
                this.currentParams.useInterpolation = true;
                this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.5);
                this.currentParams.usePrioritySync = true;
                this.currentParams.useSpatialPartitioning = true;
                this.currentParams.useJitterBuffer = false;
                return '网络质量良好，适度提高性能';
            case NetworkQualityMonitor_1.NetworkQualityLevel.MEDIUM:
                // 网络质量一般，保持平衡
                this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 200);
                this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 6);
                this.currentParams.useDeltaSync = true;
                this.currentParams.usePrediction = true;
                this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 150);
                this.currentParams.useInterpolation = true;
                this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.6);
                this.currentParams.usePrioritySync = true;
                this.currentParams.useSpatialPartitioning = true;
                this.currentParams.useJitterBuffer = true;
                this.currentParams.jitterBufferSize = this.smoothAdjust(this.currentParams.jitterBufferSize, 80);
                return '网络质量一般，保持平衡';
            case NetworkQualityMonitor_1.NetworkQualityLevel.BAD:
                // 网络质量差，增加稳定性
                this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 300);
                this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 7);
                this.currentParams.useDeltaSync = true;
                this.currentParams.usePrediction = true;
                this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 250);
                this.currentParams.useInterpolation = true;
                this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.7);
                this.currentParams.usePrioritySync = true;
                this.currentParams.useSpatialPartitioning = true;
                this.currentParams.useJitterBuffer = true;
                this.currentParams.jitterBufferSize = this.smoothAdjust(this.currentParams.jitterBufferSize, 120);
                return '网络质量差，增加稳定性';
            case NetworkQualityMonitor_1.NetworkQualityLevel.VERY_BAD:
                // 网络质量极差，大幅增加稳定性
                this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 400);
                this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 8);
                this.currentParams.useDeltaSync = true;
                this.currentParams.usePrediction = true;
                this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 350);
                this.currentParams.useInterpolation = true;
                this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.8);
                this.currentParams.usePrioritySync = true;
                this.currentParams.useSpatialPartitioning = true;
                this.currentParams.useJitterBuffer = true;
                this.currentParams.jitterBufferSize = this.smoothAdjust(this.currentParams.jitterBufferSize, 150);
                return '网络质量极差，大幅增加稳定性';
            default:
                return '未知网络质量，保持当前设置';
        }
    };
    /**
     * 激进策略调整
     * @returns 调整原因
     */
    NetworkAdaptiveController.prototype.adjustAggressive = function () {
        var quality = this.latestNetworkQuality;
        // 根据网络质量调整参数
        switch (quality.level) {
            case NetworkQualityMonitor_1.NetworkQualityLevel.EXCELLENT:
                // 网络质量极好，最大化性能
                this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 50);
                this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 3);
                this.currentParams.useDeltaSync = true;
                this.currentParams.usePrediction = true;
                this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 50);
                this.currentParams.useInterpolation = true;
                this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.3);
                this.currentParams.usePrioritySync = true;
                this.currentParams.useSpatialPartitioning = true;
                this.currentParams.useJitterBuffer = false;
                return '网络质量极好，最大化性能';
            case NetworkQualityMonitor_1.NetworkQualityLevel.GOOD:
                // 网络质量良好，大幅提高性能
                this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 100);
                this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 4);
                this.currentParams.useDeltaSync = true;
                this.currentParams.usePrediction = true;
                this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 80);
                this.currentParams.useInterpolation = true;
                this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.4);
                this.currentParams.usePrioritySync = true;
                this.currentParams.useSpatialPartitioning = true;
                this.currentParams.useJitterBuffer = false;
                return '网络质量良好，大幅提高性能';
            case NetworkQualityMonitor_1.NetworkQualityLevel.MEDIUM:
                // 网络质量一般，适度提高性能
                this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 150);
                this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 5);
                this.currentParams.useDeltaSync = true;
                this.currentParams.usePrediction = true;
                this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 120);
                this.currentParams.useInterpolation = true;
                this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.5);
                this.currentParams.usePrioritySync = true;
                this.currentParams.useSpatialPartitioning = true;
                this.currentParams.useJitterBuffer = false;
                return '网络质量一般，适度提高性能';
            case NetworkQualityMonitor_1.NetworkQualityLevel.BAD:
                // 网络质量差，保持平衡
                this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 250);
                this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 6);
                this.currentParams.useDeltaSync = true;
                this.currentParams.usePrediction = true;
                this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 200);
                this.currentParams.useInterpolation = true;
                this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.6);
                this.currentParams.usePrioritySync = true;
                this.currentParams.useSpatialPartitioning = true;
                this.currentParams.useJitterBuffer = true;
                this.currentParams.jitterBufferSize = this.smoothAdjust(this.currentParams.jitterBufferSize, 100);
                return '网络质量差，保持平衡';
            case NetworkQualityMonitor_1.NetworkQualityLevel.VERY_BAD:
                // 网络质量极差，增加稳定性
                this.currentParams.syncInterval = this.smoothAdjust(this.currentParams.syncInterval, 350);
                this.currentParams.compressionLevel = this.smoothAdjust(this.currentParams.compressionLevel, 7);
                this.currentParams.useDeltaSync = true;
                this.currentParams.usePrediction = true;
                this.currentParams.predictionTime = this.smoothAdjust(this.currentParams.predictionTime, 300);
                this.currentParams.useInterpolation = true;
                this.currentParams.interpolationFactor = this.smoothAdjust(this.currentParams.interpolationFactor, 0.7);
                this.currentParams.usePrioritySync = true;
                this.currentParams.useSpatialPartitioning = true;
                this.currentParams.useJitterBuffer = true;
                this.currentParams.jitterBufferSize = this.smoothAdjust(this.currentParams.jitterBufferSize, 120);
                return '网络质量极差，增加稳定性';
            default:
                return '未知网络质量，保持当前设置';
        }
    };
    /**
     * 自动策略调整
     * @returns 调整原因
     */
    NetworkAdaptiveController.prototype.adjustAuto = function () {
        // 根据网络质量趋势选择策略
        if (this.qualityTrend === 'improving') {
            // 网络质量正在改善，使用更激进的策略
            return this.adjustAggressive();
        }
        else if (this.qualityTrend === 'degrading') {
            // 网络质量正在恶化，使用更保守的策略
            return this.adjustConservative();
        }
        else {
            // 网络质量稳定，使用平衡策略
            return this.adjustBalanced();
        }
    };
    /**
     * 平滑调整数值
     * @param currentValue 当前值
     * @param targetValue 目标值
     * @returns 调整后的值
     */
    NetworkAdaptiveController.prototype.smoothAdjust = function (currentValue, targetValue) {
        if (!this.config.enableSmoothTransition) {
            return targetValue;
        }
        var factor = this.config.smoothFactor;
        return currentValue * (1 - factor) + targetValue * factor;
    };
    /**
     * 更新网络质量趋势
     */
    NetworkAdaptiveController.prototype.updateQualityTrend = function () {
        if (this.qualityHistory.length < 3) {
            this.qualityTrend = 'stable';
            return;
        }
        // 计算最近几次质量变化
        var latest = this.qualityHistory[0];
        var previous = this.qualityHistory[1];
        var older = this.qualityHistory[2];
        // 根据RTT和丢包率判断趋势
        var latestScore = this.calculateQualityScore(latest);
        var previousScore = this.calculateQualityScore(previous);
        var olderScore = this.calculateQualityScore(older);
        if (latestScore > previousScore && previousScore > olderScore) {
            this.qualityTrend = 'improving';
        }
        else if (latestScore < previousScore && previousScore < olderScore) {
            this.qualityTrend = 'degrading';
        }
        else {
            this.qualityTrend = 'stable';
        }
    };
    /**
     * 计算网络质量分数
     * @param quality 网络质量数据
     * @returns 质量分数（越高越好）
     */
    NetworkAdaptiveController.prototype.calculateQualityScore = function (quality) {
        // RTT越低越好，丢包率越低越好
        var rttScore = 1000 / (quality.rtt + 10); // 防止除以0
        var packetLossScore = 1 - quality.packetLoss;
        // 综合分数，权重可以调整
        return rttScore * 0.6 + packetLossScore * 0.4;
    };
    /**
     * 检查是否需要立即调整
     * @returns 是否需要立即调整
     */
    NetworkAdaptiveController.prototype.shouldAdjustImmediately = function () {
        // 如果刚刚调整过，则不立即调整
        if (Date.now() - this.lastAdjustTime < 1000) {
            return false;
        }
        // 如果网络质量突然变差，立即调整
        if (this.qualityHistory.length >= 2) {
            var latest = this.qualityHistory[0];
            var previous = this.qualityHistory[1];
            // 如果RTT突然增加50%或丢包率突然增加10%，立即调整
            if (latest.rtt > previous.rtt * 1.5 || latest.packetLoss > previous.packetLoss + 0.1) {
                return true;
            }
        }
        return false;
    };
    /**
     * 获取当前网络参数
     * @returns 当前网络参数
     */
    NetworkAdaptiveController.prototype.getCurrentParams = function () {
        return __assign({}, this.currentParams);
    };
    /**
     * 获取调整历史记录
     * @returns 调整历史记录
     */
    NetworkAdaptiveController.prototype.getAdjustmentHistory = function () {
        return __spreadArray([], this.history, true);
    };
    /**
     * 获取网络质量趋势
     * @returns 网络质量趋势
     */
    NetworkAdaptiveController.prototype.getQualityTrend = function () {
        return this.qualityTrend;
    };
    /**
     * 设置自适应策略
     * @param strategy 自适应策略
     */
    NetworkAdaptiveController.prototype.setStrategy = function (strategy) {
        this.config.strategy = strategy;
        // 立即调整参数
        this.adjustNetworkParams();
    };
    /**
     * 手动设置网络参数
     * @param params 网络参数
     */
    NetworkAdaptiveController.prototype.setNetworkParams = function (params) {
        // 更新参数
        this.currentParams = __assign(__assign({}, this.currentParams), params);
        // 应用参数
        this.applyNetworkParams();
        // 触发事件
        this.emit('paramsManuallySet', {
            params: this.currentParams,
        });
    };
    /**
     * 重置为默认参数
     */
    NetworkAdaptiveController.prototype.resetToDefaults = function () {
        // 根据当前网络质量调整为默认参数
        this.adjustNetworkParams();
    };
    /**
     * 销毁控制器
     */
    NetworkAdaptiveController.prototype.dispose = function () {
        this.stopAutoAdjust();
        this.removeAllListeners();
        this.history = [];
        this.qualityHistory = [];
    };
    return NetworkAdaptiveController;
}(EventEmitter_1.EventEmitter));
exports.NetworkAdaptiveController = NetworkAdaptiveController;

/**
 * 视觉脚本异步节点
 * 异步节点用于执行异步操作，如定时器、网络请求等
 */
import { FlowNode, FlowNodeOptions } from './FlowNode';
import { NodeCategory, NodeType, SocketType } from './Node';

/**
 * 异步节点选项
 */
export interface AsyncNodeOptions extends FlowNodeOptions {
  /** 超时时间（毫秒） */
  timeout?: number;
}

/**
 * 异步节点状态
 */
export enum AsyncNodeState {
  /** 空闲 */
  IDLE = 'idle',
  /** 运行中 */
  RUNNING = 'running',
  /** 已完成 */
  COMPLETED = 'completed',
  /** 已取消 */
  CANCELED = 'canceled',
  /** 出错 */
  ERROR = 'error'
}

/**
 * 异步节点基类
 */
export class AsyncNode extends FlowNode {
  /** 节点类型 */
  public readonly nodeType: NodeType = NodeType.ASYNC;
  
  /** 节点类别 */
  public readonly category: NodeCategory = NodeCategory.FLOW;
  
  /** 超时时间（毫秒） */
  protected timeout: number;
  
  /** 当前状态 */
  protected state: AsyncNodeState = AsyncNodeState.IDLE;
  
  /** 开始时间 */
  protected startTime: number = 0;
  
  /** 完成回调 */
  protected completeCallback: (() => void) | null = null;
  
  /** 错误回调 */
  protected errorCallback: ((error: any) => void) | null = null;
  
  /** 取消回调 */
  protected cancelCallback: (() => void) | null = null;
  
  /** 超时定时器ID */
  protected timeoutId: any = null;
  
  /**
   * 创建异步节点
   * @param options 节点选项
   */
  constructor(options: AsyncNodeOptions) {
    super(options);
    
    this.timeout = options.timeout || 0;
    
    // 添加完成和错误输出流程
    if (!this.outputFlowNames.includes('complete')) {
      this.outputFlowNames.push('complete');
    }
    
    if (!this.outputFlowNames.includes('error')) {
      this.outputFlowNames.push('error');
    }
  }
  
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();
    
    // 添加状态输出
    this.addOutput({
      name: 'state',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '当前状态'
    });
    
    // 添加错误输出
    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '错误信息'
    });
  }
  
  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 如果已经在运行，不重复执行
    if (this.state === AsyncNodeState.RUNNING) {
      return null;
    }
    
    // 获取所有输入值
    const inputs: Record<string, any> = {};
    
    for (const [name, socket] of this.inputs.entries()) {
      if (socket.type === SocketType.DATA) {
        inputs[name] = this.getInputValue(name);
      }
    }
    
    // 开始异步操作
    this.start(inputs);
    
    // 不触发任何输出流程，等待异步操作完成
    return null;
  }
  
  /**
   * 开始异步操作
   * @param inputs 输入值
   */
  protected start(inputs: Record<string, any>): void {
    // 设置状态为运行中
    this.state = AsyncNodeState.RUNNING;
    this.setOutputValue('state', this.state);
    
    // 记录开始时间
    this.startTime = Date.now();
    
    // 设置超时定时器
    if (this.timeout > 0) {
      this.timeoutId = setTimeout(() => {
        this.handleError(new Error('操作超时'));
      }, this.timeout);
    }
    
    // 执行异步操作
    try {
      this.executeAsync(inputs)
        .then(result => this.handleComplete(result))
        .catch(error => this.handleError(error));
    } catch (error) {
      this.handleError(error);
    }
  }
  
  /**
   * 执行异步操作
   * @param inputs 输入值
   * @returns Promise对象
   */
  protected async executeAsync(inputs: Record<string, any>): Promise<any> {
    // 子类实现
    return null;
  }
  
  /**
   * 处理完成
   * @param result 结果
   */
  protected handleComplete(result: any): void {
    // 清除超时定时器
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    
    // 设置状态为已完成
    this.state = AsyncNodeState.COMPLETED;
    this.setOutputValue('state', this.state);
    
    // 设置输出值
    if (result !== null && result !== undefined) {
      // 如果结果是对象，分别设置各个输出
      if (typeof result === 'object' && !Array.isArray(result)) {
        for (const [key, value] of Object.entries(result)) {
          if (this.outputs.has(key)) {
            this.setOutputValue(key, value);
          }
        }
      } 
      // 如果只有一个数据输出，直接设置
      else {
        const dataOutputs = Array.from(this.outputs.entries())
          .filter(([name, socket]) => socket.type === SocketType.DATA && name !== 'state' && name !== 'error');
        
        if (dataOutputs.length === 1) {
          this.setOutputValue(dataOutputs[0][0], result);
        }
      }
    }
    
    // 触发完成流程
    this.triggerFlow('complete');
    
    // 调用完成回调
    if (this.completeCallback) {
      this.completeCallback();
      this.completeCallback = null;
    }
  }
  
  /**
   * 处理错误
   * @param error 错误
   */
  protected handleError(error: any): void {
    // 清除超时定时器
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    
    // 设置状态为出错
    this.state = AsyncNodeState.ERROR;
    this.setOutputValue('state', this.state);
    
    // 设置错误输出
    this.setOutputValue('error', error);
    
    // 触发错误流程
    this.triggerFlow('error');
    
    // 调用错误回调
    if (this.errorCallback) {
      this.errorCallback(error);
      this.errorCallback = null;
    }
  }
  
  /**
   * 取消操作
   */
  public cancel(): void {
    // 如果不在运行状态，不需要取消
    if (this.state !== AsyncNodeState.RUNNING) {
      return;
    }
    
    // 清除超时定时器
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    
    // 设置状态为已取消
    this.state = AsyncNodeState.CANCELED;
    this.setOutputValue('state', this.state);
    
    // 调用取消回调
    if (this.cancelCallback) {
      this.cancelCallback();
      this.cancelCallback = null;
    }
  }
  
  /**
   * 重置状态
   */
  public reset(): void {
    // 如果在运行状态，先取消
    if (this.state === AsyncNodeState.RUNNING) {
      this.cancel();
    }
    
    // 重置状态
    this.state = AsyncNodeState.IDLE;
    this.setOutputValue('state', this.state);
    this.setOutputValue('error', null);
    
    // 清除回调
    this.completeCallback = null;
    this.errorCallback = null;
    this.cancelCallback = null;
  }
  
  /**
   * 设置完成回调
   * @param callback 回调函数
   */
  public onComplete(callback: () => void): void {
    this.completeCallback = callback;
  }
  
  /**
   * 设置错误回调
   * @param callback 回调函数
   */
  public onError(callback: (error: any) => void): void {
    this.errorCallback = callback;
  }
  
  /**
   * 设置取消回调
   * @param callback 回调函数
   */
  public onCancel(callback: () => void): void {
    this.cancelCallback = callback;
  }
  
  /**
   * 获取当前状态
   * @returns 当前状态
   */
  public getState(): AsyncNodeState {
    return this.state;
  }
  
  /**
   * 获取运行时间（毫秒）
   * @returns 运行时间
   */
  public getRunningTime(): number {
    if (this.state !== AsyncNodeState.RUNNING || this.startTime === 0) {
      return 0;
    }
    
    return Date.now() - this.startTime;
  }
}

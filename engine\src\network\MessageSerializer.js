"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageSerializer = exports.CompressionLevel = void 0;
var Debug_1 = require("../utils/Debug");
/**
 * 压缩级别
 */
var CompressionLevel;
(function (CompressionLevel) {
    /** 不压缩 */
    CompressionLevel[CompressionLevel["NONE"] = 0] = "NONE";
    /** 快速压缩 */
    CompressionLevel[CompressionLevel["FAST"] = 1] = "FAST";
    /** 标准压缩 */
    CompressionLevel[CompressionLevel["STANDARD"] = 2] = "STANDARD";
    /** 最大压缩 */
    CompressionLevel[CompressionLevel["MAX"] = 3] = "MAX";
})(CompressionLevel || (exports.CompressionLevel = CompressionLevel = {}));
/**
 * 消息序列化器
 * 负责序列化和反序列化网络消息
 */
var MessageSerializer = /** @class */ (function () {
    /**
     * 创建消息序列化器
     * @param useCompression 是否使用压缩
     * @param compressionLevel 压缩级别
     * @param enableCache 是否启用缓存
     */
    function MessageSerializer(useCompression, compressionLevel, enableCache) {
        if (useCompression === void 0) { useCompression = true; }
        if (compressionLevel === void 0) { compressionLevel = CompressionLevel.STANDARD; }
        if (enableCache === void 0) { enableCache = true; }
        /** 消息缓存 */
        this.messageCache = new Map();
        /** 缓存大小限制 */
        this.cacheSizeLimit = 100;
        /** 是否启用缓存 */
        this.enableCache = true;
        this.useCompression = useCompression;
        this.compressionLevel = compressionLevel;
        this.enableCache = enableCache;
        this.encoder = new TextEncoder();
        this.decoder = new TextDecoder();
    }
    /**
     * 序列化消息
     * @param message 消息对象
     * @returns 序列化后的数据
     */
    MessageSerializer.prototype.serialize = function (message) {
        return __awaiter(this, void 0, void 0, function () {
            var json, cacheKey, data, compressed, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 4, , 5]);
                        json = JSON.stringify(message);
                        cacheKey = this.enableCache ? this.calculateCacheKey(message) : '';
                        // 检查缓存
                        if (this.enableCache && cacheKey && this.messageCache.has(cacheKey)) {
                            return [2 /*return*/, this.messageCache.get(cacheKey)];
                        }
                        if (!this.useCompression) return [3 /*break*/, 2];
                        data = this.encoder.encode(json);
                        return [4 /*yield*/, this.compress(data)];
                    case 1:
                        compressed = _a.sent();
                        // 添加到缓存
                        if (this.enableCache && cacheKey) {
                            this.addToCache(cacheKey, compressed);
                        }
                        return [2 /*return*/, compressed];
                    case 2: 
                    // 不使用压缩，直接返回JSON字符串
                    return [2 /*return*/, json];
                    case 3: return [3 /*break*/, 5];
                    case 4:
                        error_1 = _a.sent();
                        Debug_1.Debug.error('MessageSerializer', 'Failed to serialize message:', error_1);
                        throw error_1;
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 反序列化消息
     * @param data 序列化后的数据
     * @returns 消息对象
     */
    MessageSerializer.prototype.deserialize = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            var json, decompressed, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 4, , 5]);
                        json = void 0;
                        if (!(data instanceof ArrayBuffer)) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.decompress(data)];
                    case 1:
                        decompressed = _a.sent();
                        // 将Uint8Array转换为JSON字符串
                        json = this.decoder.decode(decompressed);
                        return [3 /*break*/, 3];
                    case 2:
                        // 如果是字符串，则直接使用
                        json = data;
                        _a.label = 3;
                    case 3: 
                    // 将JSON字符串转换为消息对象
                    return [2 /*return*/, JSON.parse(json)];
                    case 4:
                        error_2 = _a.sent();
                        Debug_1.Debug.error('MessageSerializer', 'Failed to deserialize message:', error_2);
                        throw error_2;
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 压缩数据
     * @param data 原始数据
     * @returns 压缩后的数据
     */
    MessageSerializer.prototype.compress = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            var cs, writer, reader, chunks, _a, done, value, totalLength, result, offset, _i, chunks_1, chunk, error_3;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 8, , 9]);
                        if (!(typeof CompressionStream !== 'undefined')) return [3 /*break*/, 6];
                        cs = new CompressionStream('deflate');
                        writer = cs.writable.getWriter();
                        reader = cs.readable.getReader();
                        // 写入数据
                        return [4 /*yield*/, writer.write(data)];
                    case 1:
                        // 写入数据
                        _b.sent();
                        return [4 /*yield*/, writer.close()];
                    case 2:
                        _b.sent();
                        chunks = [];
                        _b.label = 3;
                    case 3:
                        if (!true) return [3 /*break*/, 5];
                        return [4 /*yield*/, reader.read()];
                    case 4:
                        _a = _b.sent(), done = _a.done, value = _a.value;
                        if (done)
                            return [3 /*break*/, 5];
                        chunks.push(value);
                        return [3 /*break*/, 3];
                    case 5:
                        totalLength = chunks.reduce(function (acc, chunk) { return acc + chunk.length; }, 0);
                        result = new Uint8Array(totalLength);
                        offset = 0;
                        for (_i = 0, chunks_1 = chunks; _i < chunks_1.length; _i++) {
                            chunk = chunks_1[_i];
                            result.set(chunk, offset);
                            offset += chunk.length;
                        }
                        return [2 /*return*/, result.buffer];
                    case 6: 
                    // 如果浏览器不支持CompressionStream，则使用简单的RLE压缩
                    return [2 /*return*/, this.compressRLE(data)];
                    case 7: return [3 /*break*/, 9];
                    case 8:
                        error_3 = _b.sent();
                        Debug_1.Debug.warn('MessageSerializer', 'Compression failed, using uncompressed data:', error_3);
                        return [2 /*return*/, data.buffer];
                    case 9: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 使用RLE算法压缩数据
     * @param data 原始数据
     * @returns 压缩后的数据
     */
    MessageSerializer.prototype.compressRLE = function (data) {
        if (data.length === 0) {
            return new ArrayBuffer(0);
        }
        var result = [];
        var currentByte = data[0];
        var count = 1;
        for (var i = 1; i < data.length; i++) {
            if (data[i] === currentByte && count < 255) {
                count++;
            }
            else {
                result.push(count);
                result.push(currentByte);
                currentByte = data[i];
                count = 1;
            }
        }
        result.push(count);
        result.push(currentByte);
        var compressed = new Uint8Array(result);
        // 只有当压缩后的大小小于原始大小时才使用压缩数据
        if (compressed.length < data.length) {
            // 添加标记以表示这是RLE压缩数据
            var output = new Uint8Array(compressed.length + 1);
            output[0] = 1; // 1表示RLE压缩
            output.set(compressed, 1);
            return output.buffer;
        }
        else {
            // 否则使用原始数据
            var output = new Uint8Array(data.length + 1);
            output[0] = 0; // 0表示未压缩
            output.set(data, 1);
            return output.buffer;
        }
    };
    /**
     * 解压缩数据
     * @param data 压缩后的数据
     * @returns 原始数据
     */
    MessageSerializer.prototype.decompress = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            var dataView, compressionType, ds, writer, reader, chunks, _a, done, value, totalLength, result, offset, _i, chunks_2, chunk, error_4;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 10, , 11]);
                        dataView = new Uint8Array(data);
                        compressionType = dataView[0];
                        if (!(compressionType === 0)) return [3 /*break*/, 1];
                        // 未压缩数据
                        return [2 /*return*/, dataView.slice(1)];
                    case 1:
                        if (!(compressionType === 1)) return [3 /*break*/, 2];
                        // RLE压缩数据
                        return [2 /*return*/, this.decompressRLE(dataView.slice(1))];
                    case 2:
                        if (!(typeof DecompressionStream !== 'undefined')) return [3 /*break*/, 8];
                        ds = new DecompressionStream('deflate');
                        writer = ds.writable.getWriter();
                        reader = ds.readable.getReader();
                        // 写入数据
                        return [4 /*yield*/, writer.write(dataView)];
                    case 3:
                        // 写入数据
                        _b.sent();
                        return [4 /*yield*/, writer.close()];
                    case 4:
                        _b.sent();
                        chunks = [];
                        _b.label = 5;
                    case 5:
                        if (!true) return [3 /*break*/, 7];
                        return [4 /*yield*/, reader.read()];
                    case 6:
                        _a = _b.sent(), done = _a.done, value = _a.value;
                        if (done)
                            return [3 /*break*/, 7];
                        chunks.push(value);
                        return [3 /*break*/, 5];
                    case 7:
                        totalLength = chunks.reduce(function (acc, chunk) { return acc + chunk.length; }, 0);
                        result = new Uint8Array(totalLength);
                        offset = 0;
                        for (_i = 0, chunks_2 = chunks; _i < chunks_2.length; _i++) {
                            chunk = chunks_2[_i];
                            result.set(chunk, offset);
                            offset += chunk.length;
                        }
                        return [2 /*return*/, result];
                    case 8: 
                    // 如果无法识别压缩类型或浏览器不支持，则返回原始数据
                    return [2 /*return*/, dataView];
                    case 9: return [3 /*break*/, 11];
                    case 10:
                        error_4 = _b.sent();
                        Debug_1.Debug.warn('MessageSerializer', 'Decompression failed, using raw data:', error_4);
                        return [2 /*return*/, new Uint8Array(data)];
                    case 11: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 使用RLE算法解压缩数据
     * @param data 压缩后的数据
     * @returns 原始数据
     */
    MessageSerializer.prototype.decompressRLE = function (data) {
        if (data.length === 0) {
            return new Uint8Array(0);
        }
        var result = [];
        for (var i = 0; i < data.length; i += 2) {
            var count = data[i];
            var value = data[i + 1];
            for (var j = 0; j < count; j++) {
                result.push(value);
            }
        }
        return new Uint8Array(result);
    };
    /**
     * 计算缓存键
     * @param message 消息对象
     * @returns 缓存键
     */
    MessageSerializer.prototype.calculateCacheKey = function (message) {
        // 对于心跳消息，不使用缓存
        if (message.type === 'heartbeat') {
            return '';
        }
        // 对于包含时间戳的消息，移除时间戳再计算缓存键
        var timestamp = message.timestamp, rest = __rest(message, ["timestamp"]);
        // 计算缓存键
        return message.type + '_' + JSON.stringify(rest);
    };
    /**
     * 添加到缓存
     * @param key 缓存键
     * @param value 缓存值
     */
    MessageSerializer.prototype.addToCache = function (key, value) {
        // 如果缓存已满，则移除最早添加的项
        if (this.messageCache.size >= this.cacheSizeLimit) {
            var firstKey = this.messageCache.keys().next().value;
            this.messageCache.delete(firstKey);
        }
        // 添加到缓存
        this.messageCache.set(key, value);
    };
    /**
     * 清除缓存
     */
    MessageSerializer.prototype.clearCache = function () {
        this.messageCache.clear();
    };
    /**
     * 设置是否使用压缩
     * @param useCompression 是否使用压缩
     */
    MessageSerializer.prototype.setUseCompression = function (useCompression) {
        this.useCompression = useCompression;
    };
    /**
     * 是否使用压缩
     * @returns 是否使用压缩
     */
    MessageSerializer.prototype.isUsingCompression = function () {
        return this.useCompression;
    };
    /**
     * 设置压缩级别
     * @param level 压缩级别
     */
    MessageSerializer.prototype.setCompressionLevel = function (level) {
        this.compressionLevel = level;
    };
    /**
     * 获取压缩级别
     * @returns 压缩级别
     */
    MessageSerializer.prototype.getCompressionLevel = function () {
        return this.compressionLevel;
    };
    /**
     * 设置是否启用缓存
     * @param enable 是否启用缓存
     */
    MessageSerializer.prototype.setEnableCache = function (enable) {
        this.enableCache = enable;
    };
    /**
     * 是否启用缓存
     * @returns 是否启用缓存
     */
    MessageSerializer.prototype.isEnableCache = function () {
        return this.enableCache;
    };
    /**
     * 设置缓存大小限制
     * @param limit 缓存大小限制
     */
    MessageSerializer.prototype.setCacheSizeLimit = function (limit) {
        this.cacheSizeLimit = limit;
    };
    /**
     * 获取缓存大小限制
     * @returns 缓存大小限制
     */
    MessageSerializer.prototype.getCacheSizeLimit = function () {
        return this.cacheSizeLimit;
    };
    return MessageSerializer;
}());
exports.MessageSerializer = MessageSerializer;

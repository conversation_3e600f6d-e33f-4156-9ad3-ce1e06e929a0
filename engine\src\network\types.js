"use strict";
/**
 * 网络模块类型定义文件
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompressionLevel = exports.CompressionAlgorithm = exports.MediaStreamQuality = exports.MediaStreamType = exports.SyncAreaType = exports.NetworkQualityLevel = exports.DataPriority = exports.BandwidthControlStrategy = exports.UserPermission = exports.UserRole = exports.NetworkEntitySyncMode = exports.WebRTCConnectionState = exports.NetworkState = void 0;
/**
 * 网络状态枚举
 */
var NetworkState;
(function (NetworkState) {
    /** 未连接 */
    NetworkState["DISCONNECTED"] = "disconnected";
    /** 正在连接 */
    NetworkState["CONNECTING"] = "connecting";
    /** 已连接 */
    NetworkState["CONNECTED"] = "connected";
    /** 正在断开连接 */
    NetworkState["DISCONNECTING"] = "disconnecting";
    /** 错误 */
    NetworkState["ERROR"] = "error";
})(NetworkState || (exports.NetworkState = NetworkState = {}));
/**
 * WebRTC连接状态枚举
 */
var WebRTCConnectionState;
(function (WebRTCConnectionState) {
    /** 新建 */
    WebRTCConnectionState["NEW"] = "new";
    /** 正在连接 */
    WebRTCConnectionState["CONNECTING"] = "connecting";
    /** 已连接 */
    WebRTCConnectionState["CONNECTED"] = "connected";
    /** 正在断开连接 */
    WebRTCConnectionState["DISCONNECTING"] = "disconnecting";
    /** 已断开连接 */
    WebRTCConnectionState["DISCONNECTED"] = "disconnected";
    /** 失败 */
    WebRTCConnectionState["FAILED"] = "failed";
    /** 关闭 */
    WebRTCConnectionState["CLOSED"] = "closed";
})(WebRTCConnectionState || (exports.WebRTCConnectionState = WebRTCConnectionState = {}));
/**
 * 网络实体同步模式枚举
 */
var NetworkEntitySyncMode;
(function (NetworkEntitySyncMode) {
    /** 变换同步 */
    NetworkEntitySyncMode["TRANSFORM"] = "transform";
    /** 属性同步 */
    NetworkEntitySyncMode["PROPERTIES"] = "properties";
    /** 完全同步 */
    NetworkEntitySyncMode["FULL"] = "full";
    /** 自定义同步 */
    NetworkEntitySyncMode["CUSTOM"] = "custom";
})(NetworkEntitySyncMode || (exports.NetworkEntitySyncMode = NetworkEntitySyncMode = {}));
/**
 * 用户角色枚举
 */
var UserRole;
(function (UserRole) {
    /** 访客 */
    UserRole["GUEST"] = "guest";
    /** 用户 */
    UserRole["USER"] = "user";
    /** 管理员 */
    UserRole["ADMIN"] = "admin";
    /** 超级管理员 */
    UserRole["SUPER_ADMIN"] = "super_admin";
})(UserRole || (exports.UserRole = UserRole = {}));
/**
 * 用户权限枚举
 */
var UserPermission;
(function (UserPermission) {
    /** 查看 */
    UserPermission["VIEW"] = "view";
    /** 编辑 */
    UserPermission["EDIT"] = "edit";
    /** 创建 */
    UserPermission["CREATE"] = "create";
    /** 删除 */
    UserPermission["DELETE"] = "delete";
    /** 管理用户 */
    UserPermission["MANAGE_USERS"] = "manage_users";
    /** 管理权限 */
    UserPermission["MANAGE_PERMISSIONS"] = "manage_permissions";
    /** 管理系统 */
    UserPermission["MANAGE_SYSTEM"] = "manage_system";
})(UserPermission || (exports.UserPermission = UserPermission = {}));
/**
 * 带宽控制策略枚举
 */
var BandwidthControlStrategy;
(function (BandwidthControlStrategy) {
    /** 固定 */
    BandwidthControlStrategy["FIXED"] = "fixed";
    /** 自适应 */
    BandwidthControlStrategy["ADAPTIVE"] = "adaptive";
    /** 优先级 */
    BandwidthControlStrategy["PRIORITY"] = "priority";
})(BandwidthControlStrategy || (exports.BandwidthControlStrategy = BandwidthControlStrategy = {}));
/**
 * 数据优先级枚举
 */
var DataPriority;
(function (DataPriority) {
    /** 最高 */
    DataPriority[DataPriority["HIGHEST"] = 0] = "HIGHEST";
    /** 高 */
    DataPriority[DataPriority["HIGH"] = 1] = "HIGH";
    /** 中 */
    DataPriority[DataPriority["MEDIUM"] = 2] = "MEDIUM";
    /** 低 */
    DataPriority[DataPriority["LOW"] = 3] = "LOW";
    /** 最低 */
    DataPriority[DataPriority["LOWEST"] = 4] = "LOWEST";
})(DataPriority || (exports.DataPriority = DataPriority = {}));
/**
 * 网络质量级别枚举
 */
var NetworkQualityLevel;
(function (NetworkQualityLevel) {
    /** 未知 */
    NetworkQualityLevel["UNKNOWN"] = "unknown";
    /** 极好 */
    NetworkQualityLevel["EXCELLENT"] = "excellent";
    /** 良好 */
    NetworkQualityLevel["GOOD"] = "good";
    /** 中等 */
    NetworkQualityLevel["MEDIUM"] = "medium";
    /** 差 */
    NetworkQualityLevel["BAD"] = "bad";
    /** 极差 */
    NetworkQualityLevel["POOR"] = "poor";
})(NetworkQualityLevel || (exports.NetworkQualityLevel = NetworkQualityLevel = {}));
/**
 * 同步区域类型枚举
 */
var SyncAreaType;
(function (SyncAreaType) {
    /** 全局 */
    SyncAreaType["GLOBAL"] = "global";
    /** 区域 */
    SyncAreaType["AREA"] = "area";
    /** 网格 */
    SyncAreaType["GRID"] = "grid";
    /** 自定义 */
    SyncAreaType["CUSTOM"] = "custom";
})(SyncAreaType || (exports.SyncAreaType = SyncAreaType = {}));
/**
 * 媒体流类型枚举
 */
var MediaStreamType;
(function (MediaStreamType) {
    /** 音频 */
    MediaStreamType["AUDIO"] = "audio";
    /** 视频 */
    MediaStreamType["VIDEO"] = "video";
    /** 屏幕共享 */
    MediaStreamType["SCREEN_SHARE"] = "screen_share";
})(MediaStreamType || (exports.MediaStreamType = MediaStreamType = {}));
/**
 * 媒体流质量枚举
 */
var MediaStreamQuality;
(function (MediaStreamQuality) {
    /** 低 */
    MediaStreamQuality["LOW"] = "low";
    /** 中 */
    MediaStreamQuality["MEDIUM"] = "medium";
    /** 高 */
    MediaStreamQuality["HIGH"] = "high";
    /** 超高 */
    MediaStreamQuality["ULTRA"] = "ultra";
})(MediaStreamQuality || (exports.MediaStreamQuality = MediaStreamQuality = {}));
/**
 * 压缩算法枚举
 */
var CompressionAlgorithm;
(function (CompressionAlgorithm) {
    /** 无压缩 */
    CompressionAlgorithm["NONE"] = "none";
    /** LZ字符串 */
    CompressionAlgorithm["LZ_STRING"] = "lz_string";
    /** MessagePack */
    CompressionAlgorithm["MSGPACK"] = "msgpack";
    /** 自定义 */
    CompressionAlgorithm["CUSTOM"] = "custom";
})(CompressionAlgorithm || (exports.CompressionAlgorithm = CompressionAlgorithm = {}));
/**
 * 压缩级别枚举
 */
var CompressionLevel;
(function (CompressionLevel) {
    /** 无压缩 */
    CompressionLevel["NONE"] = "none";
    /** 低压缩 */
    CompressionLevel["LOW"] = "low";
    /** 中压缩 */
    CompressionLevel["MEDIUM"] = "medium";
    /** 高压缩 */
    CompressionLevel["HIGH"] = "high";
})(CompressionLevel || (exports.CompressionLevel = CompressionLevel = {}));

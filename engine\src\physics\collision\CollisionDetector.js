"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollisionDetector = void 0;
var CollisionEvent_1 = require("./CollisionEvent");
var EventEmitter_1 = require("../../utils/EventEmitter");
/**
 * 碰撞检测器
 */
var CollisionDetector = /** @class */ (function (_super) {
    __extends(CollisionDetector, _super);
    /**
     * 创建碰撞检测器
     * @param world 物理世界
     */
    function CollisionDetector(world) {
        var _this = _super.call(this) || this;
        /** 活跃碰撞对 */
        _this.activePairs = new Map();
        /** 实体到物理体的映射 */
        _this.entityToBody = new Map();
        /** 物理体到实体的映射 */
        _this.bodyToEntity = new Map();
        /** 实体到碰撞器的映射 */
        _this.entityToCollider = new Map();
        _this.world = world;
        // 添加碰撞事件监听
        _this.world.addEventListener('beginContact', _this.handleBeginContact.bind(_this));
        _this.world.addEventListener('endContact', _this.handleEndContact.bind(_this));
        return _this;
    }
    /**
     * 注册实体和物理体
     * @param entity 实体
     * @param body 物理体
     */
    CollisionDetector.prototype.registerBody = function (entity, body) {
        this.entityToBody.set(entity.id, body);
        this.bodyToEntity.set(body.getCannonBody(), entity);
    };
    /**
     * 注销实体和物理体
     * @param entity 实体
     */
    CollisionDetector.prototype.unregisterBody = function (entity) {
        var body = this.entityToBody.get(entity.id);
        if (body) {
            this.bodyToEntity.delete(body.getCannonBody());
            this.entityToBody.delete(entity.id);
        }
    };
    /**
     * 注册实体和碰撞器
     * @param entity 实体
     * @param collider 碰撞器
     */
    CollisionDetector.prototype.registerCollider = function (entity, collider) {
        this.entityToCollider.set(entity.id, collider);
    };
    /**
     * 注销实体和碰撞器
     * @param entity 实体
     */
    CollisionDetector.prototype.unregisterCollider = function (entity) {
        this.entityToCollider.delete(entity.id);
    };
    /**
     * 处理碰撞开始事件
     * @param event CANNON碰撞事件
     */
    CollisionDetector.prototype.handleBeginContact = function (event) {
        // 获取碰撞的物理体
        var bodyA = event.bodyA;
        var bodyB = event.bodyB;
        // 获取对应的实体
        var entityA = this.bodyToEntity.get(bodyA);
        var entityB = this.bodyToEntity.get(bodyB);
        // 如果任一实体不存在，则忽略
        if (!entityA || !entityB) {
            return;
        }
        // 获取碰撞器
        var colliderA = this.entityToCollider.get(entityA.id);
        var colliderB = this.entityToCollider.get(entityB.id);
        // 检查是否为触发器碰撞
        var isTrigger = (colliderA && colliderA.isTrigger()) || (colliderB && colliderB.isTrigger());
        // 创建碰撞对ID
        var pairId = this.createPairId(entityA.id, entityB.id);
        // 当前时间
        var time = performance.now();
        // 创建碰撞对
        var pair = {
            entityA: entityA,
            entityB: entityB,
            startTime: time,
            lastUpdateTime: time,
            isTrigger: isTrigger
        };
        // 添加到活跃碰撞对
        this.activePairs.set(pairId, pair);
        // 创建碰撞事件
        var collisionType = isTrigger ? CollisionEvent_1.CollisionEventType.TRIGGER_ENTER : CollisionEvent_1.CollisionEventType.BEGIN;
        var collisionEvent = CollisionEvent_1.CollisionEvent.fromCannonEvent(collisionType, event, entityA, entityB, time);
        // 发出碰撞事件
        this.emit(collisionType, collisionEvent);
        // 通知实体
        this.notifyEntity(entityA, collisionEvent);
        this.notifyEntity(entityB, collisionEvent);
    };
    /**
     * 处理碰撞结束事件
     * @param event CANNON碰撞事件
     */
    CollisionDetector.prototype.handleEndContact = function (event) {
        // 获取碰撞的物理体
        var bodyA = event.bodyA;
        var bodyB = event.bodyB;
        // 获取对应的实体
        var entityA = this.bodyToEntity.get(bodyA);
        var entityB = this.bodyToEntity.get(bodyB);
        // 如果任一实体不存在，则忽略
        if (!entityA || !entityB) {
            return;
        }
        // 创建碰撞对ID
        var pairId = this.createPairId(entityA.id, entityB.id);
        // 获取碰撞对
        var pair = this.activePairs.get(pairId);
        if (!pair) {
            return;
        }
        // 当前时间
        var time = performance.now();
        // 创建碰撞事件
        var collisionType = pair.isTrigger ? CollisionEvent_1.CollisionEventType.TRIGGER_EXIT : CollisionEvent_1.CollisionEventType.END;
        var collisionEvent = CollisionEvent_1.CollisionEvent.fromCannonEvent(collisionType, event, entityA, entityB, time);
        // 发出碰撞事件
        this.emit(collisionType, collisionEvent);
        // 通知实体
        this.notifyEntity(entityA, collisionEvent);
        this.notifyEntity(entityB, collisionEvent);
        // 从活跃碰撞对中移除
        this.activePairs.delete(pairId);
    };
    /**
     * 更新碰撞检测器
     * @param deltaTime 时间增量
     */
    CollisionDetector.prototype.update = function (deltaTime) {
        // 当前时间
        var time = performance.now();
        // 更新所有活跃碰撞对
        for (var _i = 0, _a = this.activePairs.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], pairId = _b[0], pair = _b[1];
            // 创建碰撞事件
            var collisionType = pair.isTrigger ? CollisionEvent_1.CollisionEventType.TRIGGER_STAY : CollisionEvent_1.CollisionEventType.STAY;
            // 创建碰撞事件数据
            var collisionEvent = new CollisionEvent_1.CollisionEvent({
                type: collisionType,
                entityA: pair.entityA,
                entityB: pair.entityB,
                contactPoint: null,
                contactNormal: null,
                impulse: 0,
                relativeVelocity: null,
                time: time
            });
            // 发出碰撞事件
            this.emit(collisionType, collisionEvent);
            // 通知实体
            this.notifyEntity(pair.entityA, collisionEvent);
            this.notifyEntity(pair.entityB, collisionEvent);
            // 更新最后更新时间
            pair.lastUpdateTime = time;
        }
    };
    /**
     * 通知实体碰撞事件
     * @param entity 实体
     * @param event 碰撞事件
     */
    CollisionDetector.prototype.notifyEntity = function (entity, event) {
        // 检查实体是否有onCollision方法
        if (typeof entity.onCollision === 'function') {
            entity['onCollision'](event);
        }
    };
    /**
     * 创建碰撞对ID
     * @param idA 实体A的ID
     * @param idB 实体B的ID
     * @returns 碰撞对ID
     */
    CollisionDetector.prototype.createPairId = function (idA, idB) {
        // 确保ID的顺序一致，以便相同的碰撞对总是有相同的ID
        return idA < idB ? "".concat(idA, "_").concat(idB) : "".concat(idB, "_").concat(idA);
    };
    /**
     * 清除所有碰撞对
     */
    CollisionDetector.prototype.clear = function () {
        this.activePairs.clear();
        this.entityToBody.clear();
        this.bodyToEntity.clear();
        this.entityToCollider.clear();
    };
    /**
     * 销毁检测器
     */
    CollisionDetector.prototype.dispose = function () {
        // 移除事件监听
        this.world.removeEventListener('beginContact', this.handleBeginContact.bind(this));
        this.world.removeEventListener('endContact', this.handleEndContact.bind(this));
        // 清除所有碰撞对
        this.clear();
        // 移除所有事件监听器
        this.removeAllListeners();
    };
    return CollisionDetector;
}(EventEmitter_1.EventEmitter));
exports.CollisionDetector = CollisionDetector;

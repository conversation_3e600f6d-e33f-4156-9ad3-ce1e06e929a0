"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlendSpace2D = void 0;
/**
 * 二维混合空间
 * 用于在二维参数空间中混合多个动画
 */
var THREE = require("three");
/**
 * 二维混合空间
 */
var BlendSpace2D = /** @class */ (function () {
    /**
     * 构造函数
     * @param config 混合空间配置
     */
    function BlendSpace2D(config) {
        /** 节点列表 */
        this.nodes = [];
        /** 当前位置 */
        this.position = new THREE.Vector2();
        /** 临时向量 */
        this.tempVec2 = new THREE.Vector2();
        /** 三角形列表（用于三角形混合） */
        this.triangles = [];
        /** 是否已三角化 */
        this.triangulated = false;
        this.config = {
            minX: config.minX,
            maxX: config.maxX,
            minY: config.minY,
            maxY: config.maxY,
            normalizeInput: config.normalizeInput !== undefined ? config.normalizeInput : true,
            useTriangulation: config.useTriangulation !== undefined ? config.useTriangulation : true
        };
    }
    /**
     * 添加节点
     * @param clip 动画片段
     * @param position 位置
     * @param userData 用户数据
     * @returns 添加的节点
     */
    BlendSpace2D.prototype.addNode = function (clip, position, userData) {
        // 检查位置是否在范围内
        if (position.x < this.config.minX || position.x > this.config.maxX ||
            position.y < this.config.minY || position.y > this.config.maxY) {
            console.warn('BlendSpace2D: 节点位置超出范围', position);
        }
        var node = {
            clip: clip,
            position: position.clone(),
            weight: 0,
            userData: userData
        };
        this.nodes.push(node);
        this.triangulated = false;
        return node;
    };
    /**
     * 移除节点
     * @param node 要移除的节点
     * @returns 是否成功移除
     */
    BlendSpace2D.prototype.removeNode = function (node) {
        var index = this.nodes.indexOf(node);
        if (index === -1)
            return false;
        this.nodes.splice(index, 1);
        this.triangulated = false;
        return true;
    };
    /**
     * 设置当前位置
     * @param x X坐标
     * @param y Y坐标
     */
    BlendSpace2D.prototype.setPosition = function (x, y) {
        // 限制在范围内
        var clampedX = Math.max(this.config.minX, Math.min(this.config.maxX, x));
        var clampedY = Math.max(this.config.minY, Math.min(this.config.maxY, y));
        this.setPosition(clampedX, clampedY);
    };
    /**
     * 获取当前位置
     * @returns 当前位置
     */
    BlendSpace2D.prototype.getPosition = function () {
        return this.position.clone();
    };
    /**
     * 更新混合权重
     */
    BlendSpace2D.prototype.update = function () {
        // 如果没有节点，则不更新
        if (this.nodes.length === 0)
            return;
        // 如果只有一个节点，则权重为1
        if (this.nodes.length === 1) {
            this.nodes[0].weight = 1;
            return;
        }
        // 如果使用三角形混合且有足够的节点
        if (this.config.useTriangulation && this.nodes.length >= 3) {
            this.updateTriangulationWeights();
        }
        else {
            this.updateDistanceWeights();
        }
    };
    /**
     * 使用三角形混合更新权重
     */
    BlendSpace2D.prototype.updateTriangulationWeights = function () {
        // 如果还没有三角化，则进行三角化
        if (!this.triangulated) {
            this.triangulate();
        }
        // 重置所有节点的权重
        for (var _i = 0, _a = this.nodes; _i < _a.length; _i++) {
            var node = _a[_i];
            node.weight = 0;
        }
        // 找到包含当前位置的三角形
        for (var _b = 0, _c = this.triangles; _b < _c.length; _b++) {
            var triangle = _c[_b];
            var a = this.nodes[triangle[0]].position;
            var b = this.nodes[triangle[1]].position;
            var c = this.nodes[triangle[2]].position;
            // 检查点是否在三角形内
            if (this.pointInTriangle(this.position, a, b, c)) {
                // 计算重心坐标
                var weights = this.calculateBarycentricWeights(this.position, a, b, c);
                // 设置节点权重
                this.nodes[triangle[0]].weight = weights.x;
                this.nodes[triangle[1]].weight = weights.y;
                this.nodes[triangle[2]].weight = weights.z;
                break;
            }
        }
    };
    /**
     * 使用距离加权更新权重
     */
    BlendSpace2D.prototype.updateDistanceWeights = function () {
        // 计算到每个节点的距离
        var distances = [];
        var totalWeight = 0;
        for (var _i = 0, _a = this.nodes; _i < _a.length; _i++) {
            var node = _a[_i];
            var distance = this.position.distanceTo(node.position);
            // 使用距离的倒数作为权重
            var weight = distance < 0.0001 ? 1000000 : 1 / distance;
            distances.push(weight);
            totalWeight += weight;
        }
        // 归一化权重
        for (var i = 0; i < this.nodes.length; i++) {
            this.nodes[i].weight = distances[i] / totalWeight;
        }
    };
    /**
     * 三角化节点
     */
    BlendSpace2D.prototype.triangulate = function () {
        // 简单的三角化算法
        // 注意：这是一个简化的实现，实际应用中可能需要更复杂的算法
        this.triangles = [];
        // 如果节点数量少于3，无法三角化
        if (this.nodes.length < 3)
            return;
        // 找到一个初始三角形
        this.triangles.push([0, 1, 2]);
        // 对于剩余的点，尝试添加到三角形网格中
        for (var i = 3; i < this.nodes.length; i++) {
            var newTriangles = [];
            // 检查每个现有三角形
            for (var _i = 0, _a = this.triangles; _i < _a.length; _i++) {
                var triangle = _a[_i];
                var a = this.nodes[triangle[0]].position;
                var b = this.nodes[triangle[1]].position;
                var c = this.nodes[triangle[2]].position;
                // 如果点在三角形内部，则分割三角形
                if (this.pointInTriangle(this.nodes[i].position, a, b, c)) {
                    newTriangles.push([triangle[0], triangle[1], i]);
                    newTriangles.push([triangle[1], triangle[2], i]);
                    newTriangles.push([triangle[2], triangle[0], i]);
                }
                else {
                    // 否则保留原三角形
                    newTriangles.push(triangle);
                }
            }
            this.triangles = newTriangles;
        }
        this.triangulated = true;
    };
    /**
     * 检查点是否在三角形内
     * @param p 点
     * @param a 三角形顶点A
     * @param b 三角形顶点B
     * @param c 三角形顶点C
     * @returns 是否在三角形内
     */
    BlendSpace2D.prototype.pointInTriangle = function (p, a, b, c) {
        var v0 = new THREE.Vector2().subVectors(c, a);
        var v1 = new THREE.Vector2().subVectors(b, a);
        var v2 = new THREE.Vector2().subVectors(p, a);
        var dot00 = v0.dot(v0);
        var dot01 = v0.dot(v1);
        var dot02 = v0.dot(v2);
        var dot11 = v1.dot(v1);
        var dot12 = v1.dot(v2);
        var invDenom = 1 / (dot00 * dot11 - dot01 * dot01);
        var u = (dot11 * dot02 - dot01 * dot12) * invDenom;
        var v = (dot00 * dot12 - dot01 * dot02) * invDenom;
        return (u >= 0) && (v >= 0) && (u + v <= 1);
    };
    /**
     * 计算重心坐标
     * @param p 点
     * @param a 三角形顶点A
     * @param b 三角形顶点B
     * @param c 三角形顶点C
     * @returns 重心坐标 (u, v, w)
     */
    BlendSpace2D.prototype.calculateBarycentricWeights = function (p, a, b, c) {
        var v0 = new THREE.Vector2().subVectors(b, a);
        var v1 = new THREE.Vector2().subVectors(c, a);
        var v2 = new THREE.Vector2().subVectors(p, a);
        var d00 = v0.dot(v0);
        var d01 = v0.dot(v1);
        var d11 = v1.dot(v1);
        var d20 = v2.dot(v0);
        var d21 = v2.dot(v1);
        var denom = d00 * d11 - d01 * d01;
        var v = (d11 * d20 - d01 * d21) / denom;
        var w = (d00 * d21 - d01 * d20) / denom;
        var u = 1.0 - v - w;
        return new THREE.Vector3(u, v, w);
    };
    /**
     * 获取所有节点
     * @returns 节点列表
     */
    BlendSpace2D.prototype.getNodes = function () {
        return this.nodes;
    };
    /**
     * 获取活跃节点（权重大于0的节点）
     * @returns 活跃节点列表
     */
    BlendSpace2D.prototype.getActiveNodes = function () {
        return this.nodes.filter(function (node) { return node.weight > 0; });
    };
    return BlendSpace2D;
}());
exports.BlendSpace2D = BlendSpace2D;

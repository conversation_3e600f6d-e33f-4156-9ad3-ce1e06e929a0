"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MaterialSystem = void 0;
var System_1 = require("../../core/System");
var EventEmitter_1 = require("../../utils/EventEmitter");
var MaterialFactory_1 = require("./MaterialFactory");
var MaterialOptimizer_1 = require("./MaterialOptimizer");
var MaterialConverter_1 = require("./MaterialConverter");
var DeviceCapabilities_1 = require("../../utils/DeviceCapabilities");
/**
 * 材质系统类
 */
var MaterialSystem = exports.MaterialSystem = /** @class */ (function (_super) {
    __extends(MaterialSystem, _super);
    /**
     * 创建材质系统
     * @param options 材质系统配置
     */
    function MaterialSystem(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, 100) || this;
        /** 材质映射表 */
        _this.materials = new Map();
        /** 材质使用计数 */
        _this.materialUsageCount = new Map();
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        _this.autoOptimize = options.autoOptimize !== undefined ? options.autoOptimize : true;
        _this.autoDowngrade = options.autoDowngrade !== undefined ? options.autoDowngrade : true;
        _this.maxMaterials = options.maxMaterials || 1000;
        _this.enableCache = options.enableCache !== undefined ? options.enableCache : true;
        // 创建设备能力检测
        _this.deviceCapabilities = new DeviceCapabilities_1.DeviceCapabilities();
        // 创建材质工厂
        _this.factory = new MaterialFactory_1.MaterialFactory();
        // 创建材质优化器
        _this.optimizer = new MaterialOptimizer_1.MaterialOptimizer({
            deviceCapabilities: _this.deviceCapabilities
        });
        // 创建材质转换器
        _this.converter = new MaterialConverter_1.MaterialConverter({
            deviceCapabilities: _this.deviceCapabilities
        });
        return _this;
    }
    /**
     * 创建材质
     * @param type 材质类型
     * @param params 材质参数
     * @returns 材质
     */
    MaterialSystem.prototype.createMaterial = function (type, params) {
        if (params === void 0) { params = {}; }
        // 生成材质ID
        var materialId = this.generateMaterialId(type, params);
        // 如果启用缓存且材质已存在，则返回现有材质
        if (this.enableCache && this.materials.has(materialId)) {
            var material_1 = this.materials.get(materialId);
            this.incrementUsageCount(materialId);
            return material_1;
        }
        // 创建材质
        var material = this.factory.createMaterial(type, params);
        // 如果启用自动优化，则优化材质
        if (this.autoOptimize) {
            material = this.optimizer.optimizeMaterial(material);
        }
        // 如果启用自动降级且设备性能较低，则降级材质
        if (this.autoDowngrade && this.deviceCapabilities.isLowPerformanceDevice()) {
            material = this.converter.downgrade(material);
        }
        // 存储材质
        if (this.enableCache) {
            this.materials.set(materialId, material);
            this.materialUsageCount.set(materialId, 1);
        }
        // 检查材质数量是否超过限制
        this.checkMaterialLimit();
        // 触发材质创建事件
        this.eventEmitter.emit('materialCreated', material);
        return material;
    };
    /**
     * 获取材质
     * @param id 材质ID
     * @returns 材质
     */
    MaterialSystem.prototype.getMaterial = function (id) {
        if (!this.materials.has(id)) {
            return null;
        }
        var material = this.materials.get(id);
        this.incrementUsageCount(id);
        return material;
    };
    /**
     * 释放材质
     * @param material 材质
     */
    MaterialSystem.prototype.releaseMaterial = function (material) {
        var id = this.getMaterialId(material);
        if (!id)
            return;
        this.decrementUsageCount(id);
    };
    /**
     * 优化材质
     * @param material 材质
     * @returns 优化后的材质
     */
    MaterialSystem.prototype.optimizeMaterial = function (material) {
        return this.optimizer.optimizeMaterial(material);
    };
    /**
     * 转换材质类型
     * @param material 材质
     * @param targetType 目标类型
     * @returns 转换后的材质
     */
    MaterialSystem.prototype.convertMaterial = function (material, targetType) {
        return this.converter.convert(material, targetType);
    };
    /**
     * 降级材质
     * @param material 材质
     * @returns 降级后的材质
     */
    MaterialSystem.prototype.downgradeMaterial = function (material) {
        return this.converter.downgrade(material);
    };
    /**
     * 升级材质
     * @param material 材质
     * @returns 升级后的材质
     */
    MaterialSystem.prototype.upgradeMaterial = function (material) {
        return this.converter.upgrade(material);
    };
    /**
     * 设置是否启用自动优化
     * @param enabled 是否启用
     */
    MaterialSystem.prototype.setAutoOptimize = function (enabled) {
        this.autoOptimize = enabled;
    };
    /**
     * 设置是否启用自动降级
     * @param enabled 是否启用
     */
    MaterialSystem.prototype.setAutoDowngrade = function (enabled) {
        this.autoDowngrade = enabled;
    };
    /**
     * 设置最大材质数量
     * @param maxMaterials 最大材质数量
     */
    MaterialSystem.prototype.setMaxMaterials = function (maxMaterials) {
        this.maxMaterials = maxMaterials;
    };
    /**
     * 设置是否启用材质缓存
     * @param enabled 是否启用
     */
    MaterialSystem.prototype.setEnableCache = function (enabled) {
        this.enableCache = enabled;
    };
    /**
     * 清理未使用的材质
     */
    MaterialSystem.prototype.cleanupUnusedMaterials = function () {
        for (var _i = 0, _a = this.materialUsageCount.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], id = _b[0], count = _b[1];
            if (count <= 0) {
                var material = this.materials.get(id);
                if (material) {
                    material.dispose();
                    this.materials.delete(id);
                    this.materialUsageCount.delete(id);
                    this.eventEmitter.emit('materialDisposed', id);
                }
            }
        }
    };
    /**
     * 清理所有材质
     */
    MaterialSystem.prototype.clearAllMaterials = function () {
        for (var _i = 0, _a = this.materials.values(); _i < _a.length; _i++) {
            var material = _a[_i];
            material.dispose();
        }
        this.materials.clear();
        this.materialUsageCount.clear();
        this.eventEmitter.emit('allMaterialsCleared');
    };
    /**
     * 获取材质数量
     * @returns 材质数量
     */
    MaterialSystem.prototype.getMaterialCount = function () {
        return this.materials.size;
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param listener 监听器
     */
    MaterialSystem.prototype.on = function (event, listener) {
        this.eventEmitter.on(event, listener);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param listener 监听器
     */
    MaterialSystem.prototype.off = function (event, listener) {
        this.eventEmitter.off(event, listener);
    };
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    MaterialSystem.prototype.update = function (deltaTime) {
        // 定期清理未使用的材质
        if (this.enableCache && Math.random() < 0.01) { // 约每100帧清理一次
            this.cleanupUnusedMaterials();
        }
    };
    /**
     * 销毁系统
     */
    MaterialSystem.prototype.dispose = function () {
        this.clearAllMaterials();
        this.eventEmitter.removeAllListeners();
        _super.prototype.dispose.call(this);
    };
    /**
     * 生成材质ID
     * @param type 材质类型
     * @param params 材质参数
     * @returns 材质ID
     */
    MaterialSystem.prototype.generateMaterialId = function (type, params) {
        // 简单的材质ID生成方法，可以根据需要改进
        return "".concat(type, "_").concat(JSON.stringify(params));
    };
    /**
     * 获取材质ID
     * @param material 材质
     * @returns 材质ID
     */
    MaterialSystem.prototype.getMaterialId = function (material) {
        for (var _i = 0, _a = this.materials.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], id = _b[0], mat = _b[1];
            if (mat === material) {
                return id;
            }
        }
        return null;
    };
    /**
     * 增加材质使用计数
     * @param id 材质ID
     */
    MaterialSystem.prototype.incrementUsageCount = function (id) {
        var count = this.materialUsageCount.get(id) || 0;
        this.materialUsageCount.set(id, count + 1);
    };
    /**
     * 减少材质使用计数
     * @param id 材质ID
     */
    MaterialSystem.prototype.decrementUsageCount = function (id) {
        var count = this.materialUsageCount.get(id) || 0;
        this.materialUsageCount.set(id, Math.max(0, count - 1));
    };
    /**
     * 检查材质数量是否超过限制
     */
    MaterialSystem.prototype.checkMaterialLimit = function () {
        if (this.materials.size <= this.maxMaterials) {
            return;
        }
        // 找出使用次数最少的材质
        var leastUsedId = null;
        var leastUsedCount = Infinity;
        for (var _i = 0, _a = this.materialUsageCount.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], id = _b[0], count = _b[1];
            if (count < leastUsedCount) {
                leastUsedId = id;
                leastUsedCount = count;
            }
        }
        // 移除使用次数最少的材质
        if (leastUsedId) {
            var material = this.materials.get(leastUsedId);
            if (material) {
                material.dispose();
                this.materials.delete(leastUsedId);
                this.materialUsageCount.delete(leastUsedId);
                this.eventEmitter.emit('materialDisposed', leastUsedId);
            }
        }
    };
    /** 系统类型 */
    MaterialSystem.type = 'MaterialSystem';
    return MaterialSystem;
}(System_1.System));

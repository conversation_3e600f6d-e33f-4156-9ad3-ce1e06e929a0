"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioListener = void 0;
/**
 * 音频监听器
 * 用于处理3D音频的空间位置和方向
 */
var THREE = require("three");
/**
 * 音频监听器
 */
var AudioListener = /** @class */ (function () {
    /**
     * 创建音频监听器
     * @param context 音频上下文
     * @param options 音频监听器选项
     */
    function AudioListener(context, options) {
        if (options === void 0) { options = {}; }
        /** 位置 */
        this.position = new THREE.Vector3();
        /** 前方向 */
        this.forward = new THREE.Vector3(0, 0, -1);
        /** 上方向 */
        this.up = new THREE.Vector3(0, 1, 0);
        /** 速度 */
        this.velocity = new THREE.Vector3();
        /** 上一次位置 */
        this.previousPosition = new THREE.Vector3();
        /** 上一次更新时间 */
        this.previousTime = 0;
        /** 相机 */
        this.camera = null;
        /** 是否已销毁 */
        this.destroyed = false;
        this.context = context;
        this.listener = context.listener;
        // 设置初始位置和方向
        if (options.position)
            this.position.copy(options.position);
        if (options.forward)
            this.forward.copy(options.forward).normalize();
        if (options.up)
            this.up.copy(options.up).normalize();
        if (options.velocity)
            this.velocity.copy(options.velocity);
        this.previousPosition.copy(this.position);
        this.previousTime = context.currentTime;
        // 更新监听器
        this.updateListener();
    }
    /**
     * 更新监听器
     */
    AudioListener.prototype.updateListener = function () {
        // 设置位置
        if (this.listener.positionX && this.listener.positionY && this.listener.positionZ) {
            this.listener.positionX.value = this.getPosition().x;
            this.listener.positionY.value = this.getPosition().y;
            this.listener.positionZ.value = this.getPosition().z;
        }
        else {
            this.listener.setPosition(this.getPosition().x, this.getPosition().y, this.getPosition().z);
        }
        // 设置方向
        if (this.listener.forwardX && this.listener.forwardY && this.listener.forwardZ &&
            this.listener.upX && this.listener.upY && this.listener.upZ) {
            this.listener.forwardX.value = this.forward.x;
            this.listener.forwardY.value = this.forward.y;
            this.listener.forwardZ.value = this.forward.z;
            this.listener.upX.value = this.up.x;
            this.listener.upY.value = this.up.y;
            this.listener.upZ.value = this.up.z;
        }
        else {
            this.listener.setOrientation(this.forward.x, this.forward.y, this.forward.z, this.up.x, this.up.y, this.up.z);
        }
        // 设置速度
        if (this.listener.speedX && this.listener.speedY && this.listener.speedZ) {
            this.listener.speedX.value = this.velocity.x;
            this.listener.speedY.value = this.velocity.y;
            this.listener.speedZ.value = this.velocity.z;
        }
    };
    /**
     * 更新监听器
     */
    AudioListener.prototype.update = function () {
        // 如果有相机，则从相机更新位置和方向
        if (this.camera) {
            this.updateFromCamera();
        }
        // 计算速度
        var currentTime = this.context.currentTime;
        var deltaTime = currentTime - this.previousTime;
        if (deltaTime > 0) {
            this.velocity.copy(this.position).sub(this.previousPosition).divideScalar(deltaTime);
            this.previousPosition.copy(this.position);
            this.previousTime = currentTime;
        }
        // 更新监听器
        this.updateListener();
    };
    /**
     * 从相机更新位置和方向
     */
    AudioListener.prototype.updateFromCamera = function () {
        if (!this.camera)
            return;
        // 获取相机位置
        this.position.setFromMatrixPosition(this.camera.matrixWorld);
        // 获取相机方向
        this.forward.set(0, 0, -1).applyQuaternion(this.camera.quaternion);
        this.up.set(0, 1, 0).applyQuaternion(this.camera.quaternion);
    };
    /**
     * 设置位置
     * @param x X坐标
     * @param y Y坐标
     * @param z Z坐标
     */
    AudioListener.prototype.setPosition = function (x, y, z) {
        this.setPosition(x, y, z);
    };
    /**
     * 设置方向
     * @param forwardX 前方向X
     * @param forwardY 前方向Y
     * @param forwardZ 前方向Z
     * @param upX 上方向X
     * @param upY 上方向Y
     * @param upZ 上方向Z
     */
    AudioListener.prototype.setOrientation = function (forwardX, forwardY, forwardZ, upX, upY, upZ) {
        this.forward.set(forwardX, forwardY, forwardZ).normalize();
        this.up.set(upX, upY, upZ).normalize();
    };
    /**
     * 设置速度
     * @param x X速度
     * @param y Y速度
     * @param z Z速度
     */
    AudioListener.prototype.setVelocity = function (x, y, z) {
        this.velocity.set(x, y, z);
    };
    /**
     * 设置相机
     * @param camera 相机
     */
    AudioListener.prototype.setCamera = function (camera) {
        this.camera = camera;
        this.updateFromCamera();
    };
    /**
     * 获取位置
     * @returns 位置
     */
    AudioListener.prototype.getPosition = function () {
        return this.position.clone();
    };
    /**
     * 获取前方向
     * @returns 前方向
     */
    AudioListener.prototype.getForward = function () {
        return this.forward.clone();
    };
    /**
     * 获取上方向
     * @returns 上方向
     */
    AudioListener.prototype.getUp = function () {
        return this.up.clone();
    };
    /**
     * 获取速度
     * @returns 速度
     */
    AudioListener.prototype.getVelocity = function () {
        return this.velocity.clone();
    };
    /**
     * 获取相机
     * @returns 相机
     */
    AudioListener.prototype.getCamera = function () {
        return this.camera;
    };
    /**
     * 获取音频上下文
     * @returns 音频上下文
     */
    AudioListener.prototype.getContext = function () {
        return this.context;
    };
    /**
     * 获取原生监听器
     * @returns 原生监听器
     */
    AudioListener.prototype.getNativeListener = function () {
        return this.listener;
    };
    /**
     * 销毁监听器
     */
    AudioListener.prototype.dispose = function () {
        if (this.destroyed)
            return;
        this.camera = null;
        this.destroyed = true;
    };
    return AudioListener;
}());
exports.AudioListener = AudioListener;

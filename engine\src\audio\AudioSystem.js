"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioSystem = exports.AudioEventType = exports.AudioType = void 0;
/**
 * 音频系统
 * 用于处理3D音频效果和音频播放
 */
var THREE = require("three");
var EventEmitter_1 = require("../utils/EventEmitter");
var System_1 = require("../core/System");
var AudioListener_1 = require("./AudioListener");
var AudioSource_1 = require("./AudioSource");
/**
 * 音频类型
 */
var AudioType;
(function (AudioType) {
    /** 音效 */
    AudioType["SOUND"] = "sound";
    /** 音乐 */
    AudioType["MUSIC"] = "music";
    /** 语音 */
    AudioType["VOICE"] = "voice";
    /** 环境音 */
    AudioType["AMBIENT"] = "ambient";
    /** 界面音效 */
    AudioType["UI"] = "ui";
})(AudioType || (exports.AudioType = AudioType = {}));
/**
 * 音频事件类型
 */
var AudioEventType;
(function (AudioEventType) {
    /** 加载完成 */
    AudioEventType["LOAD"] = "load";
    /** 加载错误 */
    AudioEventType["ERROR"] = "error";
    /** 播放 */
    AudioEventType["PLAY"] = "play";
    /** 暂停 */
    AudioEventType["PAUSE"] = "pause";
    /** 停止 */
    AudioEventType["STOP"] = "stop";
    /** 结束 */
    AudioEventType["END"] = "end";
    /** 循环 */
    AudioEventType["LOOP"] = "loop";
    /** 音量变化 */
    AudioEventType["VOLUME_CHANGE"] = "volumeChange";
    /** 静音变化 */
    AudioEventType["MUTE_CHANGE"] = "muteChange";
    /** 音频添加 */
    AudioEventType["AUDIO_ADDED"] = "audioAdded";
    /** 音频移除 */
    AudioEventType["AUDIO_REMOVED"] = "audioRemoved";
})(AudioEventType || (exports.AudioEventType = AudioEventType = {}));
/**
 * 音频系统
 */
var AudioSystem = exports.AudioSystem = /** @class */ (function (_super) {
    __extends(AudioSystem, _super);
    /**
     * 创建音频系统
     * @param options 音频系统选项
     */
    function AudioSystem(options) {
        if (options === void 0) { options = {}; }
        var _this = 
        // 将系统名称转换为数字优先级，这里使用0作为默认优先级
        _super.call(this, 0) || this;
        /** 音频类型音量 */
        _this.typeVolumes = new Map();
        /** 音频类型静音 */
        _this.typeMuted = new Map();
        /** 音频上下文 */
        _this.context = null;
        /** 主音量节点 */
        _this.masterGain = null;
        /** 类型音量节点 */
        _this.typeGains = new Map();
        /** 音频监听器 */
        _this.listener = null;
        /** 音频源映射 */
        _this.sources = new Map();
        /** 当前播放的音频源 */
        _this.playingSources = new Set();
        /** 音频缓存 */
        _this.bufferCache = new Map();
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 是否已初始化 */
        _this.initialized = false;
        /** 是否已销毁 */
        _this.destroyed = false;
        /** 音频加载器 */
        _this.audioLoader = new THREE.AudioLoader();
        /** 是否支持Web Audio API */
        _this.supported = typeof AudioContext !== 'undefined';
        _this.audioEnabled = options.enabled !== undefined ? options.enabled : true;
        // 设置基类的启用状态
        _this.setEnabled(_this.audioEnabled);
        _this.volume = options.volume !== undefined ? options.volume : 1.0;
        _this.muted = options.muted !== undefined ? options.muted : false;
        _this.spatialAudio = options.spatialAudio !== undefined ? options.spatialAudio : true;
        _this.autoLoad = options.autoLoad !== undefined ? options.autoLoad : true;
        _this.maxSimultaneous = options.maxSimultaneous !== undefined ? options.maxSimultaneous : 32;
        // 设置音频类型音量
        if (options.typeVolumes) {
            for (var _i = 0, _a = Object.entries(options.typeVolumes); _i < _a.length; _i++) {
                var _b = _a[_i], type = _b[0], volume = _b[1];
                _this.typeVolumes.set(type, volume);
            }
        }
        // 设置音频类型静音
        if (options.typeMuted) {
            for (var _c = 0, _d = Object.entries(options.typeMuted); _c < _d.length; _c++) {
                var _e = _d[_c], type = _e[0], muted = _e[1];
                _this.typeMuted.set(type, muted);
            }
        }
        // 设置默认音频类型音量
        for (var _f = 0, _g = Object.values(AudioType); _f < _g.length; _f++) {
            var type = _g[_f];
            if (!_this.typeVolumes.has(type)) {
                _this.typeVolumes.set(type, 1.0);
            }
            if (!_this.typeMuted.has(type)) {
                _this.typeMuted.set(type, false);
            }
        }
        // 如果支持Web Audio API，则创建音频上下文
        if (_this.supported) {
            try {
                _this.context = new AudioContext(options.contextOptions);
                // 创建主音量节点
                _this.masterGain = _this.context.createGain();
                _this.masterGain.gain.value = _this.muted ? 0 : _this.volume;
                _this.masterGain.connect(_this.context.destination);
                // 创建类型音量节点
                for (var _h = 0, _j = Object.values(AudioType); _h < _j.length; _h++) {
                    var type = _j[_h];
                    var gain = _this.context.createGain();
                    var typeVolume = _this.typeVolumes.get(type) || 1.0;
                    var typeMuted = _this.typeMuted.get(type) || false;
                    gain.gain.value = typeMuted ? 0 : typeVolume;
                    gain.connect(_this.masterGain);
                    _this.typeGains.set(type, gain);
                }
                // 创建音频监听器
                _this.listener = new AudioListener_1.AudioListener(_this.context);
            }
            catch (error) {
                console.error('创建音频上下文失败:', error);
                _this.supported = false;
            }
        }
        return _this;
    }
    /**
     * 初始化系统
     */
    AudioSystem.prototype.initialize = function () {
        if (this.initialized || !this.supported)
            return;
        // 恢复音频上下文
        this.resumeContext();
        this.initialized = true;
    };
    /**
     * 恢复音频上下文
     */
    AudioSystem.prototype.resumeContext = function () {
        if (!this.context)
            return;
        // 如果音频上下文被挂起，则恢复
        if (this.context.state === 'suspended') {
            this.context.resume().catch(function (error) {
                console.error('恢复音频上下文失败:', error);
            });
        }
    };
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    AudioSystem.prototype.update = function (deltaTime) {
        if (!this.initialized || !this.isEnabled() || !this.supported)
            return;
        // 更新音频监听器
        if (this.listener) {
            this.listener.update();
        }
        // 更新所有音频源
        for (var _i = 0, _a = Array.from(this.playingSources); _i < _a.length; _i++) {
            var source = _a[_i];
            source.update(deltaTime);
        }
    };
    /**
     * 创建音频源
     * @param id 音频源ID
     * @param type 音频类型
     * @returns 音频源
     */
    AudioSystem.prototype.createSource = function (id, type) {
        if (type === void 0) { type = AudioType.SOUND; }
        if (!this.initialized || !this.supported || !this.context || !this.listener)
            return null;
        // 如果已存在相同ID的音频源，则返回
        if (this.sources.has(id)) {
            return this.sources.get(id) || null;
        }
        // 获取类型音量节点
        var typeGain = this.typeGains.get(type);
        if (!typeGain)
            return null;
        // 创建音频源
        var source = new AudioSource_1.AudioSource({
            id: id,
            type: type,
            context: this.context,
            listener: this.listener,
            destination: typeGain
        });
        // 添加到音频源映射
        this.sources.set(id, source);
        // 触发音频添加事件
        this.eventEmitter.emit(AudioEventType.AUDIO_ADDED, { id: id, type: type });
        return source;
    };
    /**
     * 移除音频源
     * @param id 音频源ID
     * @returns 是否成功移除
     */
    AudioSystem.prototype.removeSource = function (id) {
        var source = this.sources.get(id);
        if (!source)
            return false;
        // 停止音频源
        source.stop();
        // 从播放列表中移除
        this.playingSources.delete(source);
        // 销毁音频源
        source.dispose();
        // 从音频源映射中移除
        this.sources.delete(id);
        // 触发音频移除事件
        this.eventEmitter.emit(AudioEventType.AUDIO_REMOVED, { id: id, type: source.getType() });
        return true;
    };
    /**
     * 获取音频源
     * @param id 音频源ID
     * @returns 音频源
     */
    AudioSystem.prototype.getSource = function (id) {
        return this.sources.get(id) || null;
    };
    /**
     * 获取所有音频源
     * @returns 音频源数组
     */
    AudioSystem.prototype.getSources = function () {
        return Array.from(this.sources.values());
    };
    /**
     * 获取指定类型的音频源
     * @param type 音频类型
     * @returns 音频源数组
     */
    AudioSystem.prototype.getSourcesByType = function (type) {
        return Array.from(this.sources.values()).filter(function (source) { return source.getType() === type; });
    };
    /**
     * 获取当前播放的音频源
     * @returns 音频源数组
     */
    AudioSystem.prototype.getPlayingSources = function () {
        return Array.from(this.playingSources);
    };
    /**
     * 加载音频
     * @param url 音频URL
     * @param onLoad 加载完成回调
     * @param onError 加载错误回调
     */
    AudioSystem.prototype.loadAudio = function (url, onLoad, onError) {
        var _this = this;
        if (!this.initialized || !this.supported || !this.context) {
            if (onError)
                onError(new Error('音频系统未初始化或不支持'));
            return;
        }
        // 如果已缓存，则直接返回
        if (this.bufferCache.has(url)) {
            var buffer = this.bufferCache.get(url);
            if (buffer && onLoad) {
                onLoad(buffer);
            }
            return;
        }
        // 加载音频
        this.audioLoader.load(url, function (buffer) {
            // 缓存音频
            _this.bufferCache.set(url, buffer);
            // 触发加载完成事件
            _this.eventEmitter.emit(AudioEventType.LOAD, { url: url, buffer: buffer });
            // 调用回调
            if (onLoad)
                onLoad(buffer);
        }, undefined, function (error) {
            console.error('加载音频失败:', error);
            // 创建标准错误对象
            var errorObj = new Error(error instanceof Error ? error.message : String(error));
            // 触发加载错误事件
            _this.eventEmitter.emit(AudioEventType.ERROR, { url: url, error: errorObj });
            // 调用回调
            if (onError)
                onError(errorObj);
        });
    };
    /**
     * 播放音频
     * @param id 音频源ID
     * @param url 音频URL
     * @param options 播放选项
     * @returns 是否成功开始播放
     */
    AudioSystem.prototype.play = function (id, url, options) {
        var _this = this;
        if (options === void 0) { options = {}; }
        if (!this.initialized || !this.supported || !this.context)
            return false;
        // 恢复音频上下文
        this.resumeContext();
        // 如果达到最大同时播放数量，则停止最早的音频
        if (this.playingSources.size >= this.maxSimultaneous) {
            var oldestSource = this.playingSources.values().next().value;
            if (oldestSource) {
                oldestSource.stop();
                this.playingSources.delete(oldestSource);
            }
        }
        // 获取或创建音频源
        var source = this.getSource(id);
        if (!source) {
            source = this.createSource(id, options.type || AudioType.SOUND);
            if (!source)
                return false;
        }
        // 设置音频源选项
        if (options.loop !== undefined)
            source.setLoop(options.loop);
        if (options.volume !== undefined)
            source.setVolume(options.volume);
        if (options.playbackRate !== undefined)
            source.setPlaybackRate(options.playbackRate);
        if (options.position)
            source.setPosition(options.getPosition().x, options.getPosition().y, options.getPosition().z);
        if (options.velocity)
            source.setVelocity(options.velocity.x, options.velocity.y, options.velocity.z);
        if (options.refDistance !== undefined)
            source.setRefDistance(options.refDistance);
        if (options.maxDistance !== undefined)
            source.setMaxDistance(options.maxDistance);
        if (options.rolloffFactor !== undefined)
            source.setRolloffFactor(options.rolloffFactor);
        if (options.coneInnerAngle !== undefined)
            source.setConeInnerAngle(options.coneInnerAngle);
        if (options.coneOuterAngle !== undefined)
            source.setConeOuterAngle(options.coneOuterAngle);
        if (options.coneOuterGain !== undefined)
            source.setConeOuterGain(options.coneOuterGain);
        if (options.detune !== undefined)
            source.setDetune(options.detune);
        // 加载并播放音频
        if (this.bufferCache.has(url)) {
            // 如果已缓存，则直接播放
            var buffer = this.bufferCache.get(url);
            if (buffer) {
                source.setBuffer(buffer);
                source.play(options.offset, options.duration);
                this.playingSources.add(source);
                return true;
            }
        }
        else if (this.autoLoad) {
            // 如果未缓存且启用自动加载，则加载并播放
            this.loadAudio(url, function (buffer) {
                source === null || source === void 0 ? void 0 : source.setBuffer(buffer);
                source === null || source === void 0 ? void 0 : source.play(options.offset, options.duration);
                if (source)
                    _this.playingSources.add(source);
            }, function (error) {
                console.error('播放音频失败:', error);
            });
            return true;
        }
        return false;
    };
    /**
     * 停止音频
     * @param id 音频源ID
     * @returns 是否成功停止
     */
    AudioSystem.prototype.stop = function (id) {
        var source = this.getSource(id);
        if (!source)
            return false;
        source.stop();
        this.playingSources.delete(source);
        return true;
    };
    /**
     * 暂停音频
     * @param id 音频源ID
     * @returns 是否成功暂停
     */
    AudioSystem.prototype.pause = function (id) {
        var source = this.getSource(id);
        if (!source)
            return false;
        source.pause();
        return true;
    };
    /**
     * 恢复音频
     * @param id 音频源ID
     * @returns 是否成功恢复
     */
    AudioSystem.prototype.resume = function (id) {
        var source = this.getSource(id);
        if (!source)
            return false;
        source.resume();
        return true;
    };
    /**
     * 停止所有音频
     */
    AudioSystem.prototype.stopAll = function () {
        for (var _i = 0, _a = Array.from(this.playingSources); _i < _a.length; _i++) {
            var source = _a[_i];
            source.stop();
        }
        this.playingSources.clear();
    };
    /**
     * 暂停所有音频
     */
    AudioSystem.prototype.pauseAll = function () {
        for (var _i = 0, _a = Array.from(this.playingSources); _i < _a.length; _i++) {
            var source = _a[_i];
            source.pause();
        }
    };
    /**
     * 恢复所有音频
     */
    AudioSystem.prototype.resumeAll = function () {
        for (var _i = 0, _a = Array.from(this.playingSources); _i < _a.length; _i++) {
            var source = _a[_i];
            source.resume();
        }
    };
    /**
     * 停止指定类型的所有音频
     * @param type 音频类型
     */
    AudioSystem.prototype.stopAllByType = function (type) {
        for (var _i = 0, _a = Array.from(this.playingSources); _i < _a.length; _i++) {
            var source = _a[_i];
            if (source.getType() === type) {
                source.stop();
                this.playingSources.delete(source);
            }
        }
    };
    /**
     * 暂停指定类型的所有音频
     * @param type 音频类型
     */
    AudioSystem.prototype.pauseAllByType = function (type) {
        for (var _i = 0, _a = Array.from(this.playingSources); _i < _a.length; _i++) {
            var source = _a[_i];
            if (source.getType() === type) {
                source.pause();
            }
        }
    };
    /**
     * 恢复指定类型的所有音频
     * @param type 音频类型
     */
    AudioSystem.prototype.resumeAllByType = function (type) {
        for (var _i = 0, _a = Array.from(this.playingSources); _i < _a.length; _i++) {
            var source = _a[_i];
            if (source.getType() === type) {
                source.resume();
            }
        }
    };
    /**
     * 设置全局音量
     * @param volume 音量（0-1）
     */
    AudioSystem.prototype.setVolume = function (volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        // 更新主音量节点
        if (this.masterGain) {
            this.masterGain.gain.value = this.muted ? 0 : this.volume;
        }
        // 触发音量变化事件
        this.eventEmitter.emit(AudioEventType.VOLUME_CHANGE, { volume: this.volume });
    };
    /**
     * 获取全局音量
     * @returns 音量（0-1）
     */
    AudioSystem.prototype.getVolume = function () {
        return this.volume;
    };
    /**
     * 设置全局静音
     * @param muted 是否静音
     */
    AudioSystem.prototype.setMuted = function (muted) {
        this.muted = muted;
        // 更新主音量节点
        if (this.masterGain) {
            this.masterGain.gain.value = this.muted ? 0 : this.volume;
        }
        // 触发静音变化事件
        this.eventEmitter.emit(AudioEventType.MUTE_CHANGE, { muted: this.muted });
    };
    /**
     * 获取全局静音
     * @returns 是否静音
     */
    AudioSystem.prototype.getMuted = function () {
        return this.muted;
    };
    /**
     * 设置音频类型音量
     * @param type 音频类型
     * @param volume 音量（0-1）
     */
    AudioSystem.prototype.setTypeVolume = function (type, volume) {
        volume = Math.max(0, Math.min(1, volume));
        this.typeVolumes.set(type, volume);
        // 更新类型音量节点
        var gain = this.typeGains.get(type);
        var muted = this.typeMuted.get(type) || false;
        if (gain) {
            gain.gain.value = muted ? 0 : volume;
        }
        // 触发音量变化事件
        this.eventEmitter.emit(AudioEventType.VOLUME_CHANGE, { type: type, volume: volume });
    };
    /**
     * 获取音频类型音量
     * @param type 音频类型
     * @returns 音量（0-1）
     */
    AudioSystem.prototype.getTypeVolume = function (type) {
        return this.typeVolumes.get(type) || 1.0;
    };
    /**
     * 设置音频类型静音
     * @param type 音频类型
     * @param muted 是否静音
     */
    AudioSystem.prototype.setTypeMuted = function (type, muted) {
        this.typeMuted.set(type, muted);
        // 更新类型音量节点
        var gain = this.typeGains.get(type);
        var volume = this.typeVolumes.get(type) || 1.0;
        if (gain) {
            gain.gain.value = muted ? 0 : volume;
        }
        // 触发静音变化事件
        this.eventEmitter.emit(AudioEventType.MUTE_CHANGE, { type: type, muted: muted });
    };
    /**
     * 获取音频类型静音
     * @param type 音频类型
     * @returns 是否静音
     */
    AudioSystem.prototype.getTypeMuted = function (type) {
        return this.typeMuted.get(type) || false;
    };
    /**
     * 设置是否启用
     * @param enabled 是否启用
     */
    AudioSystem.prototype.setAudioEnabled = function (enabled) {
        this.audioEnabled = enabled;
        // 如果禁用，则暂停所有音频
        if (!enabled) {
            this.pauseAll();
            this.onDisable();
        }
        else {
            // 如果启用，则恢复音频上下文
            this.resumeContext();
            this.onEnable();
        }
    };
    /**
     * 获取是否启用
     * @returns 是否启用
     */
    AudioSystem.prototype.getAudioEnabled = function () {
        return this.audioEnabled;
    };
    /**
     * 设置是否启用空间音频
     * @param enabled 是否启用
     */
    AudioSystem.prototype.setSpatialAudio = function (enabled) {
        this.spatialAudio = enabled;
        // 更新所有音频源
        for (var _i = 0, _a = Array.from(this.sources.values()); _i < _a.length; _i++) {
            var source = _a[_i];
            source.setSpatial(enabled);
        }
    };
    /**
     * 获取是否启用空间音频
     * @returns 是否启用
     */
    AudioSystem.prototype.getSpatialAudio = function () {
        return this.spatialAudio;
    };
    /**
     * 获取音频监听器
     * @returns 音频监听器
     */
    AudioSystem.prototype.getListener = function () {
        return this.listener;
    };
    /**
     * 获取音频上下文
     * @returns 音频上下文
     */
    AudioSystem.prototype.getContext = function () {
        return this.context;
    };
    /**
     * 获取是否支持Web Audio API
     * @returns 是否支持
     */
    AudioSystem.prototype.isSupported = function () {
        return this.supported;
    };
    /**
     * 清除音频缓存
     */
    AudioSystem.prototype.clearCache = function () {
        this.bufferCache.clear();
    };
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器函数
     * @returns 当前实例，用于链式调用
     */
    AudioSystem.prototype.on = function (event, callback) {
        // 使用私有的事件发射器处理事件
        this.eventEmitter.on(event, callback);
        return this;
    };
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器函数
     * @returns 当前实例，用于链式调用
     */
    AudioSystem.prototype.off = function (event, callback) {
        // 使用私有的事件发射器处理事件
        this.eventEmitter.off(event, callback);
        return this;
    };
    /**
     * 销毁系统
     */
    AudioSystem.prototype.dispose = function () {
        if (this.destroyed)
            return;
        // 停止所有音频
        this.stopAll();
        // 销毁所有音频源
        for (var _i = 0, _a = Array.from(this.sources.values()); _i < _a.length; _i++) {
            var source = _a[_i];
            source.dispose();
        }
        this.sources.clear();
        this.playingSources.clear();
        this.bufferCache.clear();
        // 断开所有节点
        for (var _b = 0, _c = Array.from(this.typeGains.values()); _b < _c.length; _b++) {
            var gain = _c[_b];
            gain.disconnect();
        }
        if (this.masterGain) {
            this.masterGain.disconnect();
        }
        // 关闭音频上下文
        if (this.context && this.context.state !== 'closed') {
            this.context.close().catch(function (error) {
                console.error('关闭音频上下文失败:', error);
            });
        }
        // 销毁音频监听器
        if (this.listener) {
            this.listener.dispose();
        }
        // 移除所有事件监听器
        this.eventEmitter.removeAllListeners();
        this.typeGains.clear();
        this.masterGain = null;
        this.context = null;
        this.listener = null;
        this.initialized = false;
        this.destroyed = true;
        _super.prototype.dispose.call(this);
    };
    /** 系统名称 */
    AudioSystem.NAME = 'AudioSystem';
    return AudioSystem;
}(System_1.System));

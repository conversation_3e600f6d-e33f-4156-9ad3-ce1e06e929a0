"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhysicsBody = exports.BodyType = void 0;
/**
 * 物理体组件
 * 为实体提供物理属性和行为
 */
var THREE = require("three");
var CANNON = require("cannon-es");
var Component_1 = require("../core/Component");
var PhysicsCollider_1 = require("./PhysicsCollider");
var BodyType;
(function (BodyType) {
    BodyType["STATIC"] = "static";
    BodyType["DYNAMIC"] = "dynamic";
    BodyType["KINEMATIC"] = "kinematic";
})(BodyType || (exports.BodyType = BodyType = {}));
var PhysicsBody = exports.PhysicsBody = /** @class */ (function (_super) {
    __extends(PhysicsBody, _super);
    /**
     * 创建物理体组件
     * @param options 物理体选项
     */
    function PhysicsBody(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, PhysicsBody.type) || this;
        /** CANNON.js物理体 */
        _this.body = null;
        /** 物理材质 */
        _this.physicsMaterial = null;
        /** 物理世界 */
        _this.world = null;
        /** 是否已初始化 */
        _this.initialized = false;
        /** 碰撞回调 */
        _this.collisionCallbacks = {};
        _this.bodyType = options.type || BodyType.DYNAMIC;
        _this.mass = _this.bodyType === BodyType.STATIC ? 0 : (options.mass || 1);
        _this.fixedRotation = options.fixedRotation || false;
        _this.linearDamping = options.linearDamping || 0.01;
        _this.angularDamping = options.angularDamping || 0.01;
        _this.allowSleep = options.allowSleep !== undefined ? options.allowSleep : true;
        _this.collisionGroup = options.collisionGroup || 1;
        _this.collisionMask = options.collisionMask || -1; // 默认与所有组碰撞
        // 创建物理材质
        if (options.material) {
            _this.physicsMaterial = new CANNON.Material();
            _this.physicsMaterial.friction = options.material.friction !== undefined ? options.material.friction : 0.3;
            _this.physicsMaterial.restitution = options.material.restitution !== undefined ? options.material.restitution : 0.3;
        }
        return _this;
    }
    /**
     * 初始化物理体
     * @param world 物理世界
     */
    PhysicsBody.prototype.initialize = function (world) {
        if (this.initialized || !this.entity)
            return;
        this.world = world;
        // 创建物理体
        var bodyOptions = {
            mass: this.mass,
            fixedRotation: this.fixedRotation,
            linearDamping: this.linearDamping,
            angularDamping: this.angularDamping,
            allowSleep: this.allowSleep,
            collisionFilterGroup: this.collisionGroup,
            collisionFilterMask: this.collisionMask,
            material: this.physicsMaterial || undefined,
        };
        // 创建物理体
        this.body = new CANNON.Body(bodyOptions);
        // 设置物理体类型（使用类型断言）
        this.body.type = this.getCannonBodyType();
        // 设置用户数据（使用自定义属性）
        this.body.userData = { entity: this.entity };
        // 获取实体的变换组件
        var transform = this.entity.getTransform();
        if (transform) {
            // 设置初始位置
            var position = transform.getPosition();
            this.body.setPosition(position.x, position.y, position.z);
            // 设置初始旋转
            var rotation = transform.getRotation();
            var quaternion = new THREE.Quaternion().setFromEuler(new THREE.Euler(rotation.x, rotation.y, rotation.z));
            this.body.setRotationQuaternion(quaternion.x, quaternion.y, quaternion.z, quaternion.w);
        }
        // 获取实体的碰撞器组件
        var collider = this.entity.getComponent(PhysicsCollider_1.PhysicsCollider.type);
        if (collider) {
            // 添加碰撞形状
            var shapes = collider.getShapes();
            for (var _i = 0, shapes_1 = shapes; _i < shapes_1.length; _i++) {
                var shape = shapes_1[_i];
                this.body.addShape(shape);
            }
        }
        else {
            // 如果没有碰撞器，创建一个默认的盒体碰撞器
            console.warn("\u5B9E\u4F53 ".concat(this.entity.id, " \u6CA1\u6709\u78B0\u649E\u5668\u7EC4\u4EF6\uFF0C\u5C06\u4F7F\u7528\u9ED8\u8BA4\u76D2\u4F53\u78B0\u649E\u5668"));
            var boxShape = new CANNON.Box(new CANNON.Vec3(0.5, 0.5, 0.5));
            this.body.addShape(boxShape);
        }
        // 添加到物理世界
        world.addBody(this.body);
        this.initialized = true;
    };
    /**
     * 获取CANNON.js物理体类型
     * @returns CANNON.js物理体类型
     */
    PhysicsBody.prototype.getCannonBodyType = function () {
        switch (this.bodyType) {
            case BodyType.STATIC:
                return CANNON.Body.STATIC;
            case BodyType.DYNAMIC:
                return CANNON.Body.DYNAMIC;
            case BodyType.KINEMATIC:
                return CANNON.Body.KINEMATIC;
            default:
                return CANNON.Body.DYNAMIC;
        }
    };
    /**
     * 更新实体变换
     */
    PhysicsBody.prototype.updateTransform = function () {
        if (!this.initialized || !this.entity || !this.body)
            return;
        // 获取实体的变换组件
        var transform = this.entity.getTransform();
        if (transform) {
            // 如果是动态物体，从物理引擎更新到实体
            if (this.bodyType === BodyType.DYNAMIC) {
                // 更新位置
                transform.setPosition(this.body.getPosition().x, this.body.getPosition().y, this.body.getPosition().z);
                // 更新旋转
                if (!this.fixedRotation) {
                    var quaternion = new THREE.Quaternion(this.body.quaternion.x, this.body.quaternion.y, this.body.quaternion.z, this.body.quaternion.w);
                    var euler = new THREE.Euler().setFromQuaternion(quaternion);
                    transform.setRotation(euler.x, euler.y, euler.z);
                }
            }
            // 如果是静态或运动学物体，从实体更新到物理引擎
            else if (this.bodyType === BodyType.STATIC || this.bodyType === BodyType.KINEMATIC) {
                // 更新位置
                var position = transform.getPosition();
                this.body.setPosition(position.x, position.y, position.z);
                // 更新旋转
                if (!this.fixedRotation) {
                    var rotation = transform.getRotation();
                    var quaternion = new THREE.Quaternion().setFromEuler(new THREE.Euler(rotation.x, rotation.y, rotation.z));
                    this.body.setRotationQuaternion(quaternion.x, quaternion.y, quaternion.z, quaternion.w);
                }
                // 如果是运动学物体，需要更新速度
                if (this.bodyType === BodyType.KINEMATIC) {
                    this.body.velocity.set(0, 0, 0);
                    this.body.angularVelocity.set(0, 0, 0);
                }
            }
        }
    };
    /**
     * 应用力
     * @param force 力向量
     * @param worldPoint 世界坐标中的作用点（可选）
     */
    PhysicsBody.prototype.applyForce = function (force, worldPoint) {
        if (!this.initialized || !this.body)
            return;
        var forceVec = new CANNON.Vec3(force.x, force.y, force.z);
        if (worldPoint) {
            var pointVec = new CANNON.Vec3(worldPoint.x, worldPoint.y, worldPoint.z);
            this.body.applyForce(forceVec, pointVec);
        }
        else {
            this.body.applyForce(forceVec, this.body.position);
        }
    };
    /**
     * 应用冲量
     * @param impulse 冲量向量
     * @param worldPoint 世界坐标中的作用点（可选）
     */
    PhysicsBody.prototype.applyImpulse = function (impulse, worldPoint) {
        if (!this.initialized || !this.body)
            return;
        var impulseVec = new CANNON.Vec3(impulse.x, impulse.y, impulse.z);
        if (worldPoint) {
            var pointVec = new CANNON.Vec3(worldPoint.x, worldPoint.y, worldPoint.z);
            this.body.applyImpulse(impulseVec, pointVec);
        }
        else {
            this.body.applyImpulse(impulseVec, this.body.position);
        }
    };
    /**
     * 设置线性速度
     * @param velocity 速度向量
     */
    PhysicsBody.prototype.setLinearVelocity = function (velocity) {
        if (!this.initialized || !this.body)
            return;
        this.body.velocity.set(velocity.x, velocity.y, velocity.z);
    };
    /**
     * 获取线性速度
     * @returns 速度向量
     */
    PhysicsBody.prototype.getLinearVelocity = function () {
        if (!this.initialized || !this.body)
            return { x: 0, y: 0, z: 0 };
        return {
            x: this.body.velocity.x,
            y: this.body.velocity.y,
            z: this.body.velocity.z,
        };
    };
    /**
     * 设置角速度
     * @param velocity 角速度向量
     */
    PhysicsBody.prototype.setAngularVelocity = function (velocity) {
        if (!this.initialized || !this.body || this.fixedRotation)
            return;
        this.body.angularVelocity.set(velocity.x, velocity.y, velocity.z);
    };
    /**
     * 获取角速度
     * @returns 角速度向量
     */
    PhysicsBody.prototype.getAngularVelocity = function () {
        if (!this.initialized || !this.body || this.fixedRotation)
            return { x: 0, y: 0, z: 0 };
        return {
            x: this.body.angularVelocity.x,
            y: this.body.angularVelocity.y,
            z: this.body.angularVelocity.z,
        };
    };
    /**
     * 获取速度（getLinearVelocity的别名）
     * @returns 速度向量
     */
    PhysicsBody.prototype.getVelocity = function () {
        var velocity = this.getLinearVelocity();
        return new THREE.Vector3(velocity.x, velocity.y, velocity.z);
    };
    /**
     * 获取角速度（返回THREE.Vector3类型）
     * @returns 角速度向量
     */
    PhysicsBody.prototype.getAngularVelocityVector3 = function () {
        var angularVelocity = this.getAngularVelocity();
        return new THREE.Vector3(angularVelocity.x, angularVelocity.y, angularVelocity.z);
    };
    /**
     * 设置位置（接受THREE.Vector3类型）
     * @param position 位置向量
     */
    PhysicsBody.prototype.setPositionVector3 = function (position) {
        this.setPosition({ x: position.x, y: position.y, z: position.z });
    };
    /**
     * 获取位置（返回THREE.Vector3类型）
     * @returns 位置向量
     */
    PhysicsBody.prototype.getPositionVector3 = function () {
        var position = this.getPosition();
        return new THREE.Vector3(position.x, position.y, position.z);
    };
    /**
     * 设置碰撞回调
     * @param callbacks 碰撞回调函数
     */
    PhysicsBody.prototype.setCollisionCallbacks = function (callbacks) {
        this.collisionCallbacks = callbacks;
    };
    /**
     * 碰撞开始回调
     * @param otherEntity 碰撞的另一个实体
     * @param contact 碰撞信息
     */
    PhysicsBody.prototype.onCollisionStart = function (otherEntity, contact) {
        if (this.collisionCallbacks.start) {
            this.collisionCallbacks.start(otherEntity, contact);
        }
    };
    /**
     * 碰撞持续回调
     * @param otherEntity 碰撞的另一个实体
     * @param contact 碰撞信息
     */
    PhysicsBody.prototype.onCollisionStay = function (otherEntity, contact) {
        if (this.collisionCallbacks.stay) {
            this.collisionCallbacks.stay(otherEntity, contact);
        }
    };
    /**
     * 碰撞结束回调
     * @param otherEntity 碰撞的另一个实体
     * @param contact 碰撞信息
     */
    PhysicsBody.prototype.onCollisionEnd = function (otherEntity, contact) {
        if (this.collisionCallbacks.end) {
            this.collisionCallbacks.end(otherEntity, contact);
        }
    };
    /**
     * 触发器进入回调
     * @param otherEntity 碰撞的另一个实体
     * @param contact 碰撞信息
     */
    PhysicsBody.prototype.onTriggerEnter = function (otherEntity, contact) {
        if (this.collisionCallbacks.triggerEnter) {
            this.collisionCallbacks.triggerEnter(otherEntity, contact);
        }
    };
    /**
     * 触发器停留回调
     * @param otherEntity 碰撞的另一个实体
     * @param contact 碰撞信息
     */
    PhysicsBody.prototype.onTriggerStay = function (otherEntity, contact) {
        if (this.collisionCallbacks.triggerStay) {
            this.collisionCallbacks.triggerStay(otherEntity, contact);
        }
    };
    /**
     * 触发器离开回调
     * @param otherEntity 碰撞的另一个实体
     * @param contact 碰撞信息
     */
    PhysicsBody.prototype.onTriggerExit = function (otherEntity, contact) {
        if (this.collisionCallbacks.triggerExit) {
            this.collisionCallbacks.triggerExit(otherEntity, contact);
        }
    };
    /**
     * 获取CANNON.js物理体
     * @returns CANNON.js物理体
     */
    PhysicsBody.prototype.getCannonBody = function () {
        return this.body;
    };
    /**
     * 获取物理体类型
     * @returns 物理体类型
     */
    PhysicsBody.prototype.getBodyType = function () {
        return this.bodyType;
    };
    /**
     * 设置物理体类型
     * @param type 物理体类型
     */
    PhysicsBody.prototype.setBodyType = function (type) {
        if (this.bodyType === type)
            return;
        this.bodyType = type;
        if (this.body) {
            // 更新质量
            if (type === BodyType.STATIC) {
                this.mass = 0;
            }
            else if (this.mass === 0) {
                this.mass = 1;
            }
            // 更新物理体类型（使用类型断言）
            this.body.type = this.getCannonBodyType();
            this.body.mass = this.mass;
            this.body.updateMassProperties();
        }
    };
    /**
     * 销毁物理体
     */
    PhysicsBody.prototype.dispose = function () {
        if (this.initialized && this.body && this.world) {
            this.world.removeBody(this.body);
            this.body = null;
            this.world = null;
            this.initialized = false;
        }
        // 调用基类的dispose方法
        _super.prototype.dispose.call(this);
    };
    /** 组件类型 */
    PhysicsBody.type = 'PhysicsBody';
    return PhysicsBody;
}(Component_1.Component));

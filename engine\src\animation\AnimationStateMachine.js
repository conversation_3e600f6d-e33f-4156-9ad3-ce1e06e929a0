"use strict";
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnimationStateMachine = exports.AnimationStateMachineEventType = void 0;
/**
 * 动画状态机
 * 用于管理动画状态和状态之间的转换
 */
var EventEmitter_1 = require("../utils/EventEmitter");
/**
 * 动画状态机事件类型
 */
var AnimationStateMachineEventType;
(function (AnimationStateMachineEventType) {
    /** 状态进入 */
    AnimationStateMachineEventType["STATE_ENTER"] = "stateEnter";
    /** 状态退出 */
    AnimationStateMachineEventType["STATE_EXIT"] = "stateExit";
    /** 状态转换开始 */
    AnimationStateMachineEventType["TRANSITION_START"] = "transitionStart";
    /** 状态转换结束 */
    AnimationStateMachineEventType["TRANSITION_END"] = "transitionEnd";
})(AnimationStateMachineEventType || (exports.AnimationStateMachineEventType = AnimationStateMachineEventType = {}));
/**
 * 动画状态机
 */
var AnimationStateMachine = /** @class */ (function () {
    /**
     * 构造函数
     * @param animator 动画控制器
     */
    function AnimationStateMachine(animator) {
        /** 状态映射 */
        this.states = new Map();
        /** 转换规则列表 */
        this.transitions = [];
        /** 当前状态 */
        this.currentState = null;
        /** 上一个状态 */
        this.previousState = null;
        /** 是否正在转换 */
        this.isTransitioning = false;
        /** 当前转换规则 */
        this.currentTransition = null;
        /** 转换开始时间 */
        this.transitionStartTime = 0;
        /** 转换持续时间 */
        this.transitionDuration = 0;
        /** 事件发射器 */
        this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 参数映射 */
        this.parameters = new Map();
        /** 参数元数据映射 */
        this.parameterMetadata = new Map();
        /** 调试模式 */
        this.debugMode = false;
        this.animator = animator;
    }
    /**
     * 添加状态
     * @param state 动画状态
     */
    AnimationStateMachine.prototype.addState = function (state) {
        this.states.set(state.name, state);
    };
    /**
     * 添加转换规则
     * @param rule 转换规则
     */
    AnimationStateMachine.prototype.addTransition = function (rule) {
        this.transitions.push(rule);
    };
    /**
     * 设置参数
     * @param name 参数名称
     * @param value 参数值
     */
    AnimationStateMachine.prototype.setParameter = function (name, value) {
        this.parameters.set(name, value);
    };
    /**
     * 获取参数
     * @param name 参数名称
     * @returns 参数值
     */
    AnimationStateMachine.prototype.getParameter = function (name) {
        return this.parameters.get(name);
    };
    /**
     * 设置当前状态
     * @param stateName 状态名称
     */
    AnimationStateMachine.prototype.setCurrentState = function (stateName) {
        var state = this.states.get(stateName);
        if (!state) {
            console.warn("\u72B6\u6001 \"".concat(stateName, "\" \u4E0D\u5B58\u5728"));
            return;
        }
        if (this.currentState) {
            this.previousState = this.currentState;
            this.eventEmitter.emit(AnimationStateMachineEventType.STATE_EXIT, this.currentState);
        }
        this.currentState = state;
        this.isTransitioning = false;
        this.currentTransition = null;
        this.enterState(state);
        this.eventEmitter.emit(AnimationStateMachineEventType.STATE_ENTER, state);
    };
    /**
     * 更新状态机
     * @param deltaTime 时间增量（秒）
     */
    AnimationStateMachine.prototype.update = function (deltaTime) {
        if (!this.currentState)
            return;
        // 如果正在转换中
        if (this.isTransitioning && this.currentTransition) {
            var transitionTime = this.animator.getTime() - this.transitionStartTime;
            var progress = Math.min(transitionTime / this.transitionDuration, 1.0);
            // 如果转换完成
            if (progress >= 1.0) {
                this.isTransitioning = false;
                this.setCurrentState(this.currentTransition.to);
                this.eventEmitter.emit(AnimationStateMachineEventType.TRANSITION_END, {
                    from: this.currentTransition.from,
                    to: this.currentTransition.to
                });
                this.currentTransition = null;
            }
        }
        else {
            // 检查是否有满足条件的转换规则
            for (var _i = 0, _a = this.transitions; _i < _a.length; _i++) {
                var transition = _a[_i];
                if (transition.from === this.currentState.name && transition.condition()) {
                    this.startTransition(transition);
                    break;
                }
            }
        }
        // 更新当前状态
        this.updateState(this.currentState, deltaTime);
    };
    /**
     * 开始转换
     * @param transition 转换规则
     */
    AnimationStateMachine.prototype.startTransition = function (transition) {
        this.isTransitioning = true;
        this.currentTransition = transition;
        this.transitionStartTime = this.animator.getTime();
        this.transitionDuration = transition.duration;
        var targetState = this.states.get(transition.to);
        if (!targetState) {
            console.warn("\u76EE\u6807\u72B6\u6001 \"".concat(transition.to, "\" \u4E0D\u5B58\u5728"));
            return;
        }
        // 开始混合到目标状态
        this.blendToState(targetState, transition.duration);
        this.eventEmitter.emit(AnimationStateMachineEventType.TRANSITION_START, {
            from: transition.from,
            to: transition.to,
            duration: transition.duration
        });
    };
    /**
     * 进入状态
     * @param state 动画状态
     */
    AnimationStateMachine.prototype.enterState = function (state) {
        if (state.type === 'SingleAnimationState') {
            var singleState = state;
            this.animator.play(singleState.clipName, 0);
            this.animator.setLoop(singleState.loop);
        }
        else if (state.type === 'BlendAnimationState') {
            var blendState = state;
            // 处理混合状态
            this.updateBlendState(blendState);
        }
    };
    /**
     * 更新状态
     * @param state 动画状态
     * @param deltaTime 时间增量（秒）
     */
    AnimationStateMachine.prototype.updateState = function (state, deltaTime) {
        if (state.type === 'BlendAnimationState') {
            var blendState = state;
            this.updateBlendState(blendState);
        }
    };
    /**
     * 更新混合状态
     * @param state 混合动画状态
     */
    AnimationStateMachine.prototype.updateBlendState = function (state) {
        var paramValue = this.getParameter(state.parameterName);
        if (paramValue === undefined)
            return;
        if (state.blendSpaceType === '1D') {
            // 处理1D混合空间
            // 这里需要实现1D混合空间的逻辑
        }
        else if (state.blendSpaceType === '2D') {
            // 处理2D混合空间
            // 这里需要实现2D混合空间的逻辑
        }
    };
    /**
     * 混合到状态
     * @param state 目标状态
     * @param duration 混合持续时间（秒）
     */
    AnimationStateMachine.prototype.blendToState = function (state, duration) {
        if (state.type === 'SingleAnimationState') {
            var singleState = state;
            this.animator.play(singleState.clipName, duration);
        }
        else if (state.type === 'BlendAnimationState') {
            // 处理混合到混合状态的逻辑
        }
    };
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param callback 回调函数
     */
    AnimationStateMachine.prototype.addEventListener = function (type, callback) {
        this.eventEmitter.on(type, callback);
    };
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param callback 回调函数
     */
    AnimationStateMachine.prototype.removeEventListener = function (type, callback) {
        this.eventEmitter.off(type, callback);
    };
    /**
     * 获取所有状态
     * @returns 状态数组
     */
    AnimationStateMachine.prototype.getStates = function () {
        return Array.from(this.states.values());
    };
    /**
     * 获取状态
     * @param name 状态名称
     * @returns 状态，如果不存在则返回null
     */
    AnimationStateMachine.prototype.getState = function (name) {
        return this.states.get(name) || null;
    };
    /**
     * 移除状态
     * @param name 状态名称
     * @returns 是否成功移除
     */
    AnimationStateMachine.prototype.removeState = function (name) {
        // 检查是否存在
        if (!this.states.has(name)) {
            return false;
        }
        // 如果是当前状态，则清除当前状态
        if (this.currentState && this.currentState.name === name) {
            this.currentState = null;
        }
        // 移除相关的转换规则
        this.transitions = this.transitions.filter(function (transition) { return transition.from !== name && transition.to !== name; });
        // 移除状态
        return this.states.delete(name);
    };
    /**
     * 获取所有转换规则
     * @returns 转换规则数组
     */
    AnimationStateMachine.prototype.getTransitions = function () {
        return __spreadArray([], this.transitions, true);
    };
    /**
     * 获取转换规则
     * @param fromState 源状态名称
     * @param toState 目标状态名称
     * @returns 转换规则，如果不存在则返回null
     */
    AnimationStateMachine.prototype.getTransition = function (fromState, toState) {
        return this.transitions.find(function (transition) { return transition.from === fromState && transition.to === toState; }) || null;
    };
    /**
     * 移除转换规则
     * @param fromState 源状态名称
     * @param toState 目标状态名称
     * @returns 是否成功移除
     */
    AnimationStateMachine.prototype.removeTransition = function (fromState, toState) {
        var index = this.transitions.findIndex(function (transition) { return transition.from === fromState && transition.to === toState; });
        if (index === -1) {
            return false;
        }
        // 如果是当前转换，则清除当前转换
        if (this.currentTransition &&
            this.currentTransition.from === fromState &&
            this.currentTransition.to === toState) {
            this.isTransitioning = false;
            this.currentTransition = null;
        }
        // 移除转换规则
        this.transitions.splice(index, 1);
        return true;
    };
    /**
     * 获取当前状态
     * @returns 当前状态，如果没有则返回null
     */
    AnimationStateMachine.prototype.getCurrentState = function () {
        return this.currentState;
    };
    /**
     * 获取上一个状态
     * @returns 上一个状态，如果没有则返回null
     */
    AnimationStateMachine.prototype.getPreviousState = function () {
        return this.previousState;
    };
    /**
     * 获取所有参数
     * @returns 参数映射
     */
    AnimationStateMachine.prototype.getParameters = function () {
        return new Map(this.parameters);
    };
    /**
     * 移除参数
     * @param name 参数名称
     * @returns 是否成功移除
     */
    AnimationStateMachine.prototype.removeParameter = function (name) {
        // 移除参数元数据
        this.parameterMetadata.delete(name);
        // 移除参数
        return this.parameters.delete(name);
    };
    /**
     * 设置参数元数据
     * @param name 参数名称
     * @param metadata 参数元数据
     */
    AnimationStateMachine.prototype.setParameterMetadata = function (name, metadata) {
        this.parameterMetadata.set(name, metadata);
    };
    /**
     * 获取参数元数据
     * @param name 参数名称
     * @returns 参数元数据，如果不存在则返回null
     */
    AnimationStateMachine.prototype.getParameterMetadata = function (name) {
        return this.parameterMetadata.get(name) || null;
    };
    /**
     * 设置调试模式
     * @param enabled 是否启用
     */
    AnimationStateMachine.prototype.setDebugMode = function (enabled) {
        this.debugMode = enabled;
    };
    /**
     * 获取调试模式
     * @returns 是否启用调试模式
     */
    AnimationStateMachine.prototype.isDebugMode = function () {
        return this.debugMode;
    };
    /**
     * 获取动画控制器
     * @returns 动画控制器
     */
    AnimationStateMachine.prototype.getAnimator = function () {
        return this.animator;
    };
    /**
     * 重置状态机
     */
    AnimationStateMachine.prototype.reset = function () {
        this.currentState = null;
        this.previousState = null;
        this.isTransitioning = false;
        this.currentTransition = null;
    };
    /**
     * 清空状态机
     */
    AnimationStateMachine.prototype.clear = function () {
        this.states.clear();
        this.transitions = [];
        this.parameters.clear();
        this.parameterMetadata.clear();
        this.reset();
    };
    return AnimationStateMachine;
}());
exports.AnimationStateMachine = AnimationStateMachine;

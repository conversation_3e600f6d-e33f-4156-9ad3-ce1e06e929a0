"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedBandwidthController = exports.BandwidthAllocationMode = exports.BandwidthControlStrategy = void 0;
/**
 * 高级带宽控制器
 * 提供更精细的带宽控制和优化策略
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var NetworkQualityMonitor_1 = require("./NetworkQualityMonitor");
var types_1 = require("./types");
/**
 * 带宽控制策略
 */
var BandwidthControlStrategy;
(function (BandwidthControlStrategy) {
    /** 固定带宽 */
    BandwidthControlStrategy["FIXED"] = "fixed";
    /** 自适应带宽 */
    BandwidthControlStrategy["ADAPTIVE"] = "adaptive";
    /** 质量优先 */
    BandwidthControlStrategy["QUALITY_FIRST"] = "quality_first";
    /** 性能优先 */
    BandwidthControlStrategy["PERFORMANCE_FIRST"] = "performance_first";
    /** 优先级动态分配 */
    BandwidthControlStrategy["DYNAMIC_PRIORITY"] = "dynamic_priority";
    /** 预测性带宽分配 */
    BandwidthControlStrategy["PREDICTIVE"] = "predictive";
})(BandwidthControlStrategy || (exports.BandwidthControlStrategy = BandwidthControlStrategy = {}));
/**
 * 带宽分配模式
 */
var BandwidthAllocationMode;
(function (BandwidthAllocationMode) {
    /** 平均分配 */
    BandwidthAllocationMode["EQUAL"] = "equal";
    /** 按优先级分配 */
    BandwidthAllocationMode["PRIORITY"] = "priority";
    /** 按需分配 */
    BandwidthAllocationMode["ON_DEMAND"] = "on_demand";
    /** 动态分配 */
    BandwidthAllocationMode["DYNAMIC"] = "dynamic";
})(BandwidthAllocationMode || (exports.BandwidthAllocationMode = BandwidthAllocationMode = {}));
/**
 * 高级带宽控制器
 */
var AdvancedBandwidthController = /** @class */ (function (_super) {
    __extends(AdvancedBandwidthController, _super);
    /**
     * 创建高级带宽控制器
     * @param config 配置
     */
    function AdvancedBandwidthController(config) {
        var _a, _b;
        if (config === void 0) { config = {}; }
        var _this = _super.call(this) || this;
        /** 当前上行带宽使用（字节/秒） */
        _this.currentUploadUsage = 0;
        /** 当前下行带宽使用（字节/秒） */
        _this.currentDownloadUsage = 0;
        /** 上行数据计数（当前周期） */
        _this.uploadByteCount = 0;
        /** 下行数据计数（当前周期） */
        _this.downloadByteCount = 0;
        /** 上次计数重置时间 */
        _this.lastResetTime = Date.now();
        /** 调整定时器ID */
        _this.adjustTimerId = null;
        /** 优先级带宽分配 */
        _this.priorityBandwidthAllocation = new Map();
        /** 优先级带宽使用 */
        _this.priorityBandwidthUsage = new Map();
        /** 带宽请求队列 */
        _this.bandwidthRequests = [];
        /** 最近的网络质量数据 */
        _this.latestNetworkQuality = null;
        /** 带宽使用历史 */
        _this.usageHistory = [];
        /** 突发流量状态 */
        _this.burstState = {
            active: false,
            startTime: 0,
            originalUploadLimit: 0,
            originalDownloadLimit: 0,
        };
        /** 带宽预测数据 */
        _this.bandwidthPrediction = {
            predictedUpload: 0,
            predictedDownload: 0,
            confidence: 0,
        };
        // 默认配置
        _this.config = __assign({ maxUploadBandwidth: 1024 * 1024, maxDownloadBandwidth: 1024 * 1024, strategy: BandwidthControlStrategy.ADAPTIVE, allocation: {
                mode: BandwidthAllocationMode.PRIORITY,
                priorityAllocation: (_a = {},
                    _a[types_1.DataPriority.HIGHEST] = 0.4,
                    _a[types_1.DataPriority.HIGH] = 0.3,
                    _a[types_1.DataPriority.MEDIUM] = 0.2,
                    _a[types_1.DataPriority.LOW] = 0.07,
                    _a[types_1.DataPriority.LOWEST] = 0.03,
                    _a),
                minimumGuaranteed: (_b = {},
                    _b[types_1.DataPriority.HIGHEST] = 102400,
                    _b[types_1.DataPriority.HIGH] = 51200,
                    _b[types_1.DataPriority.MEDIUM] = 20480,
                    _b[types_1.DataPriority.LOW] = 10240,
                    _b[types_1.DataPriority.LOWEST] = 5120,
                    _b),
                allowBorrowing: true,
                enableDynamicAdjustment: true,
            }, targetUsage: 0.8, autoAdjust: true, adjustInterval: 1000, enableBurstControl: true, burstMultiplier: 1.5, burstDuration: 5000, enablePredictiveAllocation: true, predictiveWindowSize: 5000, enableSmoothTransition: true, smoothFactor: 0.3, enableHistory: true, historySize: 60 }, config);
        // 初始化带宽限制
        _this.currentUploadLimit = _this.config.maxUploadBandwidth;
        _this.currentDownloadLimit = _this.config.maxDownloadBandwidth;
        // 初始化优先级带宽分配
        _this.updatePriorityBandwidthAllocation();
        // 如果启用自动调整，则启动调整定时器
        if (_this.config.autoAdjust) {
            _this.startAutoAdjust();
        }
        // 初始化优先级带宽使用
        for (var _i = 0, _c = Object.values(types_1.DataPriority); _i < _c.length; _i++) {
            var priority = _c[_i];
            if (typeof priority === 'number') {
                _this.priorityBandwidthUsage.set(priority, 0);
            }
        }
        return _this;
    }
    /**
     * 启动自动调整
     */
    AdvancedBandwidthController.prototype.startAutoAdjust = function () {
        var _this = this;
        if (this.adjustTimerId !== null) {
            return;
        }
        this.adjustTimerId = window.setInterval(function () {
            _this.adjustBandwidth();
            _this.resetCounters();
            _this.processBandwidthRequests();
        }, this.config.adjustInterval);
    };
    /**
     * 停止自动调整
     */
    AdvancedBandwidthController.prototype.stopAutoAdjust = function () {
        if (this.adjustTimerId !== null) {
            clearInterval(this.adjustTimerId);
            this.adjustTimerId = null;
        }
    };
    /**
     * 记录上行数据
     * @param bytes 字节数
     * @param priority 数据优先级
     */
    AdvancedBandwidthController.prototype.recordUpload = function (bytes, priority) {
        if (priority === void 0) { priority = types_1.DataPriority.MEDIUM; }
        this.uploadByteCount += bytes;
        // 更新优先级带宽使用
        var currentUsage = this.priorityBandwidthUsage.get(priority) || 0;
        this.priorityBandwidthUsage.set(priority, currentUsage + bytes);
        // 检查是否需要启动突发流量控制
        if (this.config.enableBurstControl && !this.burstState.active) {
            var now = Date.now();
            var elapsed = (now - this.lastResetTime) / 1000; // 转换为秒
            if (elapsed > 0) {
                var currentRate = this.uploadByteCount / elapsed;
                // 如果当前速率超过限制的90%，启动突发流量控制
                if (currentRate > this.currentUploadLimit * 0.9) {
                    this.startBurstMode();
                }
            }
        }
    };
    /**
     * 记录下行数据
     * @param bytes 字节数
     * @param priority 数据优先级
     */
    AdvancedBandwidthController.prototype.recordDownload = function (bytes, priority) {
        if (priority === void 0) { priority = types_1.DataPriority.MEDIUM; }
        this.downloadByteCount += bytes;
        // 更新优先级带宽使用
        var currentUsage = this.priorityBandwidthUsage.get(priority) || 0;
        this.priorityBandwidthUsage.set(priority, currentUsage + bytes);
    };
    /**
     * 重置计数器
     */
    AdvancedBandwidthController.prototype.resetCounters = function () {
        var now = Date.now();
        var elapsed = (now - this.lastResetTime) / 1000; // 转换为秒
        if (elapsed > 0) {
            // 计算当前带宽使用
            this.currentUploadUsage = this.uploadByteCount / elapsed;
            this.currentDownloadUsage = this.downloadByteCount / elapsed;
            // 记录带宽使用历史
            if (this.config.enableHistory) {
                var usageData = {
                    upload: this.currentUploadUsage,
                    download: this.currentDownloadUsage,
                    uploadLimit: this.currentUploadLimit,
                    downloadLimit: this.currentDownloadLimit,
                    uploadUsageRatio: this.currentUploadLimit > 0 ? this.currentUploadUsage / this.currentUploadLimit : 0,
                    downloadUsageRatio: this.currentDownloadLimit > 0 ? this.currentDownloadUsage / this.currentDownloadLimit : 0,
                    priorityUsage: new Map(this.priorityBandwidthUsage),
                    timestamp: now,
                };
                this.usageHistory.unshift(usageData);
                // 限制历史记录大小
                if (this.usageHistory.length > this.config.historySize) {
                    this.usageHistory.pop();
                }
            }
            // 重置计数器
            this.uploadByteCount = 0;
            this.downloadByteCount = 0;
            this.lastResetTime = now;
            // 重置优先级带宽使用
            for (var _i = 0, _a = Object.values(types_1.DataPriority); _i < _a.length; _i++) {
                var priority = _a[_i];
                if (typeof priority === 'number') {
                    this.priorityBandwidthUsage.set(priority, 0);
                }
            }
            // 触发带宽使用更新事件
            this.emit('bandwidthUsageUpdated', this.getBandwidthUsage());
        }
    };
    /**
     * 调整带宽
     */
    AdvancedBandwidthController.prototype.adjustBandwidth = function () {
        // 检查突发流量控制状态
        if (this.burstState.active) {
            var now = Date.now();
            // 如果突发流量持续时间已过，恢复正常带宽
            if (now - this.burstState.startTime >= this.config.burstDuration) {
                this.stopBurstMode();
            }
        }
        // 根据策略调整带宽
        switch (this.config.strategy) {
            case BandwidthControlStrategy.FIXED:
                // 固定带宽，不进行调整
                break;
            case BandwidthControlStrategy.ADAPTIVE:
                this.adjustAdaptive();
                break;
            case BandwidthControlStrategy.QUALITY_FIRST:
                this.adjustQualityFirst();
                break;
            case BandwidthControlStrategy.PERFORMANCE_FIRST:
                this.adjustPerformanceFirst();
                break;
            case BandwidthControlStrategy.DYNAMIC_PRIORITY:
                this.adjustDynamicPriority();
                break;
            case BandwidthControlStrategy.PREDICTIVE:
                this.adjustPredictive();
                break;
        }
        // 更新优先级带宽分配
        this.updatePriorityBandwidthAllocation();
        // 触发带宽调整事件
        this.emit('bandwidthAdjusted', this.getBandwidthUsage());
    };
    /**
     * 自适应调整带宽
     */
    AdvancedBandwidthController.prototype.adjustAdaptive = function () {
        var now = Date.now();
        var elapsed = (now - this.lastResetTime) / 1000; // 转换为秒
        if (elapsed > 0) {
            // 计算使用率
            var uploadUsageRatio = this.currentUploadLimit > 0 ? this.currentUploadUsage / this.currentUploadLimit : 0;
            var downloadUsageRatio = this.currentDownloadLimit > 0 ? this.currentDownloadUsage / this.currentDownloadLimit : 0;
            // 根据使用率调整带宽限制
            if (uploadUsageRatio > this.config.targetUsage * 1.1) {
                // 使用率过高，降低限制
                this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, Math.max(this.currentUploadLimit * 0.9, this.currentUploadUsage));
            }
            else if (uploadUsageRatio < this.config.targetUsage * 0.8) {
                // 使用率过低，提高限制
                this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, Math.min(this.currentUploadLimit * 1.1, this.config.maxUploadBandwidth));
            }
            // 同样调整下行带宽
            if (downloadUsageRatio > this.config.targetUsage * 1.1) {
                this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, Math.max(this.currentDownloadLimit * 0.9, this.currentDownloadUsage));
            }
            else if (downloadUsageRatio < this.config.targetUsage * 0.8) {
                this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, Math.min(this.currentDownloadLimit * 1.1, this.config.maxDownloadBandwidth));
            }
        }
        // 如果有网络质量数据，根据网络质量进一步调整
        if (this.latestNetworkQuality) {
            this.adjustBasedOnNetworkQuality();
        }
    };
    /**
     * 质量优先调整带宽
     */
    AdvancedBandwidthController.prototype.adjustQualityFirst = function () {
        // 如果没有网络质量数据，使用自适应调整
        if (!this.latestNetworkQuality) {
            this.adjustAdaptive();
            return;
        }
        // 根据网络质量调整带宽，优先保证质量
        switch (this.latestNetworkQuality.level) {
            case NetworkQualityMonitor_1.NetworkQualityLevel.EXCELLENT:
                // 网络质量极好，使用最大带宽
                this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth);
                this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth);
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.GOOD:
                // 网络质量良好，使用90%带宽
                this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth * 0.9);
                this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth * 0.9);
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.MEDIUM:
                // 网络质量一般，使用70%带宽
                this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth * 0.7);
                this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth * 0.7);
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.BAD:
                // 网络质量差，使用50%带宽
                this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth * 0.5);
                this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth * 0.5);
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.VERY_BAD:
                // 网络质量极差，使用30%带宽
                this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth * 0.3);
                this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth * 0.3);
                break;
        }
    };
    /**
     * 性能优先调整带宽
     */
    AdvancedBandwidthController.prototype.adjustPerformanceFirst = function () {
        // 如果没有网络质量数据，使用自适应调整
        if (!this.latestNetworkQuality) {
            this.adjustAdaptive();
            return;
        }
        // 根据网络质量调整带宽，但更加激进地使用带宽
        switch (this.latestNetworkQuality.level) {
            case NetworkQualityMonitor_1.NetworkQualityLevel.EXCELLENT:
            case NetworkQualityMonitor_1.NetworkQualityLevel.GOOD:
                // 网络质量良好，使用最大带宽
                this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth);
                this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth);
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.MEDIUM:
                // 网络质量一般，使用80%带宽
                this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth * 0.8);
                this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth * 0.8);
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.BAD:
                // 网络质量差，使用60%带宽
                this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth * 0.6);
                this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth * 0.6);
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.VERY_BAD:
                // 网络质量极差，使用40%带宽
                this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, this.config.maxUploadBandwidth * 0.4);
                this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, this.config.maxDownloadBandwidth * 0.4);
                break;
        }
    };
    /**
     * 动态优先级调整带宽
     */
    AdvancedBandwidthController.prototype.adjustDynamicPriority = function () {
        // 先使用自适应调整总带宽
        this.adjustAdaptive();
        // 然后根据实际使用情况动态调整优先级分配
        if (this.config.allocation.enableDynamicAdjustment) {
            var totalUploadUsage = Array.from(this.priorityBandwidthUsage.values()).reduce(function (sum, usage) { return sum + usage; }, 0);
            if (totalUploadUsage > 0) {
                // 计算每个优先级的实际使用比例
                var usageRatios = new Map();
                for (var _i = 0, _a = this.priorityBandwidthUsage.entries(); _i < _a.length; _i++) {
                    var _b = _a[_i], priority = _b[0], usage = _b[1];
                    usageRatios.set(priority, usage / totalUploadUsage);
                }
                // 调整分配比例，向实际使用比例靠拢
                var allocation = this.config.allocation.priorityAllocation || {};
                var newAllocation = {};
                for (var _c = 0, _d = Object.values(types_1.DataPriority); _c < _d.length; _c++) {
                    var priority = _d[_c];
                    if (typeof priority === 'number') {
                        var currentAllocation = allocation[priority] || 0;
                        var usageRatio = usageRatios.get(priority) || 0;
                        // 平滑调整分配比例
                        newAllocation[priority] = this.smoothAdjust(currentAllocation, usageRatio);
                    }
                }
                // 归一化分配比例
                var totalAllocation = Object.values(newAllocation).reduce(function (sum, value) { return sum + value; }, 0);
                if (totalAllocation > 0) {
                    for (var _e = 0, _f = Object.values(types_1.DataPriority); _e < _f.length; _e++) {
                        var priority = _f[_e];
                        if (typeof priority === 'number') {
                            newAllocation[priority] = (newAllocation[priority] || 0) / totalAllocation;
                        }
                    }
                    // 更新分配比例
                    this.config.allocation.priorityAllocation = newAllocation;
                }
            }
        }
    };
    /**
     * 预测性调整带宽
     */
    AdvancedBandwidthController.prototype.adjustPredictive = function () {
        if (!this.config.enablePredictiveAllocation || this.usageHistory.length < 5) {
            // 如果没有足够的历史数据，使用自适应调整
            this.adjustAdaptive();
            return;
        }
        // 预测未来带宽使用
        this.predictBandwidthUsage();
        // 根据预测结果调整带宽
        if (this.bandwidthPrediction.confidence > 0.7) {
            // 预测可信度高，根据预测结果调整
            var predictedUploadRatio = this.bandwidthPrediction.predictedUpload / this.currentUploadLimit;
            var predictedDownloadRatio = this.bandwidthPrediction.predictedDownload / this.currentDownloadLimit;
            // 提前调整带宽以适应预测的使用
            if (predictedUploadRatio > this.config.targetUsage * 1.1) {
                // 预测使用率过高，提前增加带宽
                this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, Math.min(this.currentUploadLimit * 1.2, this.config.maxUploadBandwidth));
            }
            else if (predictedUploadRatio < this.config.targetUsage * 0.5) {
                // 预测使用率过低，提前减少带宽
                this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, Math.max(this.currentUploadLimit * 0.9, this.bandwidthPrediction.predictedUpload * 1.2));
            }
            // 同样调整下行带宽
            if (predictedDownloadRatio > this.config.targetUsage * 1.1) {
                this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, Math.min(this.currentDownloadLimit * 1.2, this.config.maxDownloadBandwidth));
            }
            else if (predictedDownloadRatio < this.config.targetUsage * 0.5) {
                this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, Math.max(this.currentDownloadLimit * 0.9, this.bandwidthPrediction.predictedDownload * 1.2));
            }
        }
        else {
            // 预测可信度低，使用自适应调整
            this.adjustAdaptive();
        }
    };
    /**
     * 根据网络质量调整带宽
     */
    AdvancedBandwidthController.prototype.adjustBasedOnNetworkQuality = function () {
        if (!this.latestNetworkQuality) {
            return;
        }
        // 根据网络质量调整带宽
        switch (this.latestNetworkQuality.level) {
            case NetworkQualityMonitor_1.NetworkQualityLevel.EXCELLENT:
                // 网络质量极好，可以使用更多带宽
                this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, Math.min(this.currentUploadLimit * 1.1, this.config.maxUploadBandwidth));
                this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, Math.min(this.currentDownloadLimit * 1.1, this.config.maxDownloadBandwidth));
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.GOOD:
                // 网络质量良好，略微增加带宽
                this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, Math.min(this.currentUploadLimit * 1.05, this.config.maxUploadBandwidth * 0.9));
                this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, Math.min(this.currentDownloadLimit * 1.05, this.config.maxDownloadBandwidth * 0.9));
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.MEDIUM:
                // 网络质量一般，保持当前带宽
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.BAD:
                // 网络质量差，减少带宽
                this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, Math.min(this.currentUploadLimit * 0.9, this.config.maxUploadBandwidth * 0.7));
                this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, Math.min(this.currentDownloadLimit * 0.9, this.config.maxDownloadBandwidth * 0.7));
                break;
            case NetworkQualityMonitor_1.NetworkQualityLevel.VERY_BAD:
                // 网络质量极差，大幅减少带宽
                this.currentUploadLimit = this.smoothAdjust(this.currentUploadLimit, Math.min(this.currentUploadLimit * 0.8, this.config.maxUploadBandwidth * 0.5));
                this.currentDownloadLimit = this.smoothAdjust(this.currentDownloadLimit, Math.min(this.currentDownloadLimit * 0.8, this.config.maxDownloadBandwidth * 0.5));
                break;
        }
    };
    /**
     * 预测带宽使用
     */
    AdvancedBandwidthController.prototype.predictBandwidthUsage = function () {
        if (this.usageHistory.length < 5) {
            // 历史数据不足，无法预测
            this.bandwidthPrediction = {
                predictedUpload: this.currentUploadUsage,
                predictedDownload: this.currentDownloadUsage,
                confidence: 0,
            };
            return;
        }
        // 使用线性回归预测未来带宽使用
        var uploadHistory = this.usageHistory.map(function (data) { return data.upload; });
        var downloadHistory = this.usageHistory.map(function (data) { return data.download; });
        // 简单线性回归
        var uploadTrend = this.calculateLinearTrend(uploadHistory);
        var downloadTrend = this.calculateLinearTrend(downloadHistory);
        // 预测未来带宽使用
        var predictedUpload = uploadHistory[0] + uploadTrend.slope;
        var predictedDownload = downloadHistory[0] + downloadTrend.slope;
        // 计算预测可信度
        var uploadConfidence = Math.min(1, Math.max(0, 1 - uploadTrend.error));
        var downloadConfidence = Math.min(1, Math.max(0, 1 - downloadTrend.error));
        var confidence = (uploadConfidence + downloadConfidence) / 2;
        // 更新预测结果
        this.bandwidthPrediction = {
            predictedUpload: Math.max(0, predictedUpload),
            predictedDownload: Math.max(0, predictedDownload),
            confidence: confidence,
        };
    };
    /**
     * 计算线性趋势
     * @param data 数据
     * @returns 趋势
     */
    AdvancedBandwidthController.prototype.calculateLinearTrend = function (data) {
        if (data.length < 2) {
            return { slope: 0, error: 1 };
        }
        // 计算平均值
        var sum = data.reduce(function (acc, value) { return acc + value; }, 0);
        var mean = sum / data.length;
        // 计算斜率
        var numerator = 0;
        var denominator = 0;
        for (var i = 0; i < data.length; i++) {
            var x = data.length - i - 1;
            var y = data[i];
            numerator += (x - (data.length - 1) / 2) * (y - mean);
            denominator += Math.pow(x - (data.length - 1) / 2, 2);
        }
        var slope = denominator !== 0 ? numerator / denominator : 0;
        // 计算误差
        var errorSum = 0;
        for (var i = 0; i < data.length; i++) {
            var x = data.length - i - 1;
            var y = data[i];
            var predicted = mean + slope * (x - (data.length - 1) / 2);
            errorSum += Math.pow(y - predicted, 2);
        }
        var error = Math.sqrt(errorSum / data.length) / (mean || 1);
        return { slope: slope, error: error };
    };
    /**
     * 平滑调整数值
     * @param currentValue 当前值
     * @param targetValue 目标值
     * @returns 调整后的值
     */
    AdvancedBandwidthController.prototype.smoothAdjust = function (currentValue, targetValue) {
        if (!this.config.enableSmoothTransition) {
            return targetValue;
        }
        var factor = this.config.smoothFactor;
        return currentValue * (1 - factor) + targetValue * factor;
    };
    /**
     * 更新优先级带宽分配
     */
    AdvancedBandwidthController.prototype.updatePriorityBandwidthAllocation = function () {
        var allocation = this.config.allocation;
        if (allocation.mode === BandwidthAllocationMode.PRIORITY) {
            // 按优先级分配带宽
            var priorityAllocation = allocation.priorityAllocation || {};
            for (var _i = 0, _a = Object.values(types_1.DataPriority); _i < _a.length; _i++) {
                var priority = _a[_i];
                if (typeof priority === 'number') {
                    var ratio = priorityAllocation[priority] || 0;
                    var bandwidth = this.currentUploadLimit * ratio;
                    this.priorityBandwidthAllocation.set(priority, bandwidth);
                }
            }
        }
        else if (allocation.mode === BandwidthAllocationMode.EQUAL) {
            // 平均分配带宽
            var priorityCount = Object.keys(types_1.DataPriority).filter(function (key) { return !isNaN(Number(key)); }).length;
            var bandwidthPerPriority = this.currentUploadLimit / priorityCount;
            for (var _b = 0, _c = Object.values(types_1.DataPriority); _b < _c.length; _b++) {
                var priority = _c[_b];
                if (typeof priority === 'number') {
                    this.priorityBandwidthAllocation.set(priority, bandwidthPerPriority);
                }
            }
        }
        // 确保最小保证带宽
        if (allocation.minimumGuaranteed) {
            for (var _d = 0, _e = Object.values(types_1.DataPriority); _d < _e.length; _d++) {
                var priority = _e[_d];
                if (typeof priority === 'number') {
                    var minBandwidth = allocation.minimumGuaranteed[priority] || 0;
                    var currentBandwidth = this.priorityBandwidthAllocation.get(priority) || 0;
                    if (currentBandwidth < minBandwidth) {
                        this.priorityBandwidthAllocation.set(priority, minBandwidth);
                    }
                }
            }
        }
    };
    /**
     * 启动突发流量模式
     */
    AdvancedBandwidthController.prototype.startBurstMode = function () {
        if (this.burstState.active) {
            return;
        }
        // 记录当前带宽限制
        this.burstState.originalUploadLimit = this.currentUploadLimit;
        this.burstState.originalDownloadLimit = this.currentDownloadLimit;
        // 增加带宽限制
        this.currentUploadLimit *= this.config.burstMultiplier;
        this.currentDownloadLimit *= this.config.burstMultiplier;
        // 更新状态
        this.burstState.active = true;
        this.burstState.startTime = Date.now();
        // 更新优先级带宽分配
        this.updatePriorityBandwidthAllocation();
        // 触发突发流量开始事件
        this.emit('burstModeStarted', {
            originalUploadLimit: this.burstState.originalUploadLimit,
            originalDownloadLimit: this.burstState.originalDownloadLimit,
            currentUploadLimit: this.currentUploadLimit,
            currentDownloadLimit: this.currentDownloadLimit,
            duration: this.config.burstDuration,
        });
    };
    /**
     * 停止突发流量模式
     */
    AdvancedBandwidthController.prototype.stopBurstMode = function () {
        if (!this.burstState.active) {
            return;
        }
        // 恢复原始带宽限制
        this.currentUploadLimit = this.burstState.originalUploadLimit;
        this.currentDownloadLimit = this.burstState.originalDownloadLimit;
        // 更新状态
        this.burstState.active = false;
        // 更新优先级带宽分配
        this.updatePriorityBandwidthAllocation();
        // 触发突发流量结束事件
        this.emit('burstModeEnded', {
            currentUploadLimit: this.currentUploadLimit,
            currentDownloadLimit: this.currentDownloadLimit,
        });
    };
    /**
     * 处理带宽请求
     */
    AdvancedBandwidthController.prototype.processBandwidthRequests = function () {
        if (this.bandwidthRequests.length === 0) {
            return;
        }
        // 按优先级排序请求
        this.bandwidthRequests.sort(function (a, b) { return a.priority - b.priority; });
        // 计算可用带宽
        var availableBandwidth = this.currentUploadLimit - this.currentUploadUsage;
        if (availableBandwidth <= 0) {
            // 没有可用带宽，延迟处理
            return;
        }
        // 处理请求
        var remainingBandwidth = availableBandwidth;
        var now = Date.now();
        var allocatedRequests = [];
        for (var _i = 0, _a = this.bandwidthRequests; _i < _a.length; _i++) {
            var request = _a[_i];
            // 检查请求是否已过期
            if (now - request.timestamp > request.maxDelay) {
                // 请求已过期，移除
                continue;
            }
            // 获取该优先级的分配带宽
            var priorityBandwidth = this.priorityBandwidthAllocation.get(request.priority) || 0;
            // 计算可分配的带宽
            var allocatableBandwidth = Math.min(remainingBandwidth, priorityBandwidth);
            // 如果允许借用未使用带宽，则可以使用更多带宽
            if (this.config.allocation.allowBorrowing) {
                allocatableBandwidth = remainingBandwidth;
            }
            // 如果可分配带宽足够，则分配
            if (allocatableBandwidth >= request.size) {
                request.allocated = true;
                request.allocatedBandwidth = allocatableBandwidth;
                remainingBandwidth -= allocatableBandwidth;
                allocatedRequests.push(request);
            }
        }
        // 移除已分配的请求
        this.bandwidthRequests = this.bandwidthRequests.filter(function (request) { return !allocatedRequests.includes(request); });
        // 触发带宽分配事件
        if (allocatedRequests.length > 0) {
            this.emit('bandwidthAllocated', {
                requests: allocatedRequests,
                remainingBandwidth: remainingBandwidth,
            });
        }
    };
    /**
     * 请求带宽
     * @param size 数据大小（字节）
     * @param priority 优先级
     * @param maxDelay 最大延迟（毫秒）
     * @returns 请求ID
     */
    AdvancedBandwidthController.prototype.requestBandwidth = function (size, priority, maxDelay) {
        if (priority === void 0) { priority = types_1.DataPriority.MEDIUM; }
        if (maxDelay === void 0) { maxDelay = 5000; }
        var requestId = "req_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9));
        // 创建请求
        var request = {
            id: requestId,
            size: size,
            priority: priority,
            maxDelay: maxDelay,
            timestamp: Date.now(),
            allocated: false,
            allocatedBandwidth: 0,
        };
        // 添加到请求队列
        this.bandwidthRequests.push(request);
        // 立即处理请求
        this.processBandwidthRequests();
        return requestId;
    };
    /**
     * 取消带宽请求
     * @param requestId 请求ID
     * @returns 是否成功取消
     */
    AdvancedBandwidthController.prototype.cancelBandwidthRequest = function (requestId) {
        var index = this.bandwidthRequests.findIndex(function (request) { return request.id === requestId; });
        if (index !== -1) {
            this.bandwidthRequests.splice(index, 1);
            return true;
        }
        return false;
    };
    /**
     * 设置网络质量数据
     * @param quality 网络质量数据
     */
    AdvancedBandwidthController.prototype.setNetworkQuality = function (quality) {
        this.latestNetworkQuality = quality;
    };
    /**
     * 设置最大带宽
     * @param uploadBandwidth 上行带宽（字节/秒）
     * @param downloadBandwidth 下行带宽（字节/秒）
     */
    AdvancedBandwidthController.prototype.setMaxBandwidth = function (uploadBandwidth, downloadBandwidth) {
        this.config.maxUploadBandwidth = uploadBandwidth;
        this.config.maxDownloadBandwidth = downloadBandwidth;
        // 如果当前限制超过最大值，则调整
        if (this.currentUploadLimit > uploadBandwidth) {
            this.currentUploadLimit = uploadBandwidth;
        }
        if (this.currentDownloadLimit > downloadBandwidth) {
            this.currentDownloadLimit = downloadBandwidth;
        }
        // 更新优先级带宽分配
        this.updatePriorityBandwidthAllocation();
    };
    /**
     * 获取带宽使用数据
     * @returns 带宽使用数据
     */
    AdvancedBandwidthController.prototype.getBandwidthUsage = function () {
        return {
            upload: this.currentUploadUsage,
            download: this.currentDownloadUsage,
            uploadLimit: this.currentUploadLimit,
            downloadLimit: this.currentDownloadLimit,
            uploadUsageRatio: this.currentUploadLimit > 0 ? this.currentUploadUsage / this.currentUploadLimit : 0,
            downloadUsageRatio: this.currentDownloadLimit > 0 ? this.currentDownloadUsage / this.currentDownloadLimit : 0,
            priorityUsage: new Map(this.priorityBandwidthUsage),
            timestamp: Date.now(),
        };
    };
    /**
     * 获取带宽使用历史
     * @returns 带宽使用历史
     */
    AdvancedBandwidthController.prototype.getBandwidthUsageHistory = function () {
        return __spreadArray([], this.usageHistory, true);
    };
    /**
     * 获取带宽预测
     * @returns 带宽预测
     */
    AdvancedBandwidthController.prototype.getBandwidthPrediction = function () {
        return __assign({}, this.bandwidthPrediction);
    };
    /**
     * 设置带宽控制策略
     * @param strategy 带宽控制策略
     */
    AdvancedBandwidthController.prototype.setStrategy = function (strategy) {
        this.config.strategy = strategy;
    };
    /**
     * 设置带宽分配配置
     * @param allocation 带宽分配配置
     */
    AdvancedBandwidthController.prototype.setAllocationConfig = function (allocation) {
        this.config.allocation = allocation;
        // 更新优先级带宽分配
        this.updatePriorityBandwidthAllocation();
    };
    /**
     * 重置带宽控制器
     */
    AdvancedBandwidthController.prototype.reset = function () {
        // 重置带宽限制
        this.currentUploadLimit = this.config.maxUploadBandwidth;
        this.currentDownloadLimit = this.config.maxDownloadBandwidth;
        // 重置计数器
        this.uploadByteCount = 0;
        this.downloadByteCount = 0;
        this.currentUploadUsage = 0;
        this.currentDownloadUsage = 0;
        this.lastResetTime = Date.now();
        // 重置优先级带宽使用
        for (var _i = 0, _a = Object.values(types_1.DataPriority); _i < _a.length; _i++) {
            var priority = _a[_i];
            if (typeof priority === 'number') {
                this.priorityBandwidthUsage.set(priority, 0);
            }
        }
        // 更新优先级带宽分配
        this.updatePriorityBandwidthAllocation();
        // 清空请求队列
        this.bandwidthRequests = [];
        // 停止突发流量模式
        if (this.burstState.active) {
            this.stopBurstMode();
        }
        // 清空历史记录
        this.usageHistory = [];
        // 重置预测数据
        this.bandwidthPrediction = {
            predictedUpload: 0,
            predictedDownload: 0,
            confidence: 0,
        };
    };
    /**
     * 销毁带宽控制器
     */
    AdvancedBandwidthController.prototype.dispose = function () {
        this.stopAutoAdjust();
        this.removeAllListeners();
        this.reset();
    };
    return AdvancedBandwidthController;
}(EventEmitter_1.EventEmitter));
exports.AdvancedBandwidthController = AdvancedBandwidthController;

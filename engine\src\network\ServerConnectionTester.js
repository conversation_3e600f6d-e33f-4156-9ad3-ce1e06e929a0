"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerConnectionTester = void 0;
/**
 * 服务器连接测试器
 * 用于测试与服务器的连接状态和质量
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var Debug_1 = require("../utils/Debug");
/**
 * 服务器连接测试器
 * 用于测试与服务器的连接状态和质量
 */
var ServerConnectionTester = /** @class */ (function (_super) {
    __extends(ServerConnectionTester, _super);
    /**
     * 创建服务器连接测试器
     * @param config 配置
     */
    function ServerConnectionTester(config) {
        var _this = _super.call(this) || this;
        /** 当前测试结果 */
        _this.currentResult = {};
        /** 测试是否正在进行 */
        _this.testing = false;
        /** WebSocket连接 */
        _this.ws = null;
        /** 测试超时定时器ID */
        _this.timeoutId = null;
        // 默认配置
        _this.config = {
            serverUrl: config.serverUrl,
            timeout: config.timeout || 30000,
            retries: config.retries || 3,
            detailedLogging: config.detailedLogging || false,
            testWebSocket: config.testWebSocket !== undefined ? config.testWebSocket : true,
            testHttp: config.testHttp !== undefined ? config.testHttp : true,
            testAvailability: config.testAvailability !== undefined ? config.testAvailability : true,
            testResponseTime: config.testResponseTime !== undefined ? config.testResponseTime : true,
            testServerStatus: config.testServerStatus !== undefined ? config.testServerStatus : true,
            testDnsResolution: config.testDnsResolution !== undefined ? config.testDnsResolution : false,
            testSslCertificate: config.testSslCertificate !== undefined ? config.testSslCertificate : false,
            testGeoLocation: config.testGeoLocation !== undefined ? config.testGeoLocation : false,
            testRouteTrace: config.testRouteTrace !== undefined ? config.testRouteTrace : false,
        };
        if (_this.config.detailedLogging) {
            Debug_1.Debug.log('ServerConnectionTester', '服务器连接测试器已创建');
        }
        return _this;
    }
    /**
     * 开始测试
     * @returns 测试结果Promise
     */
    ServerConnectionTester.prototype.startTest = function () {
        return __awaiter(this, void 0, void 0, function () {
            var error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.testing) {
                            throw new Error('测试已在进行中');
                        }
                        this.testing = true;
                        this.resetTestResult();
                        // 记录测试开始时间
                        this.currentResult.startTime = Date.now();
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 18, , 19]);
                        // 设置超时
                        this.setTestTimeout();
                        // 发送测试开始事件
                        this.emit('testStart', { time: this.currentResult.startTime });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', '开始服务器连接测试');
                        }
                        if (!this.config.testAvailability) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.testAvailability()];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        if (!this.config.testWebSocket) return [3 /*break*/, 5];
                        return [4 /*yield*/, this.testWebSocketConnection()];
                    case 4:
                        _a.sent();
                        _a.label = 5;
                    case 5:
                        if (!this.config.testHttp) return [3 /*break*/, 7];
                        return [4 /*yield*/, this.testHttpConnection()];
                    case 6:
                        _a.sent();
                        _a.label = 7;
                    case 7:
                        if (!this.config.testServerStatus) return [3 /*break*/, 9];
                        return [4 /*yield*/, this.testServerStatus()];
                    case 8:
                        _a.sent();
                        _a.label = 9;
                    case 9:
                        if (!this.config.testDnsResolution) return [3 /*break*/, 11];
                        return [4 /*yield*/, this.testDnsResolution()];
                    case 10:
                        _a.sent();
                        _a.label = 11;
                    case 11:
                        if (!this.config.testSslCertificate) return [3 /*break*/, 13];
                        return [4 /*yield*/, this.testSslCertificate()];
                    case 12:
                        _a.sent();
                        _a.label = 13;
                    case 13:
                        if (!this.config.testGeoLocation) return [3 /*break*/, 15];
                        return [4 /*yield*/, this.testGeoLocation()];
                    case 14:
                        _a.sent();
                        _a.label = 15;
                    case 15:
                        if (!this.config.testRouteTrace) return [3 /*break*/, 17];
                        return [4 /*yield*/, this.testRouteTrace()];
                    case 16:
                        _a.sent();
                        _a.label = 17;
                    case 17:
                        // 清除超时
                        this.clearTestTimeout();
                        // 记录测试结束时间
                        this.currentResult.endTime = Date.now();
                        this.currentResult.duration = this.currentResult.endTime - this.currentResult.startTime;
                        this.currentResult.success = true;
                        // 发送测试完成事件
                        this.emit('testComplete', this.getTestResult());
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', "\u670D\u52A1\u5668\u8FDE\u63A5\u6D4B\u8BD5\u5B8C\u6210: \u53EF\u7528=".concat(this.currentResult.available, ", \u54CD\u5E94\u65F6\u95F4=").concat(this.currentResult.responseTime, "ms"));
                        }
                        this.testing = false;
                        return [2 /*return*/, this.getTestResult()];
                    case 18:
                        error_1 = _a.sent();
                        // 清除超时
                        this.clearTestTimeout();
                        // 记录测试结束时间
                        this.currentResult.endTime = Date.now();
                        this.currentResult.duration = this.currentResult.endTime - this.currentResult.startTime;
                        this.currentResult.success = false;
                        this.currentResult.error = error_1 instanceof Error ? error_1.message : String(error_1);
                        // 发送测试失败事件
                        this.emit('testError', { error: this.currentResult.error, result: this.getTestResult() });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', "\u670D\u52A1\u5668\u8FDE\u63A5\u6D4B\u8BD5\u5931\u8D25: ".concat(this.currentResult.error));
                        }
                        this.testing = false;
                        return [2 /*return*/, this.getTestResult()];
                    case 19: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 取消测试
     */
    ServerConnectionTester.prototype.cancelTest = function () {
        if (!this.testing) {
            return;
        }
        // 清除超时
        this.clearTestTimeout();
        // 关闭WebSocket连接
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        // 记录测试结束时间
        this.currentResult.endTime = Date.now();
        this.currentResult.duration = this.currentResult.endTime - this.currentResult.startTime;
        this.currentResult.success = false;
        this.currentResult.error = '测试被取消';
        // 发送测试取消事件
        this.emit('testCancel', this.getTestResult());
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('ServerConnectionTester', '服务器连接测试被取消');
        }
        this.testing = false;
    };
    /**
     * 设置测试超时
     */
    ServerConnectionTester.prototype.setTestTimeout = function () {
        var _this = this;
        this.timeoutId = window.setTimeout(function () {
            if (_this.testing) {
                _this.cancelTest();
                _this.currentResult.error = '测试超时';
                _this.emit('testTimeout', _this.getTestResult());
                if (_this.config.detailedLogging) {
                    Debug_1.Debug.log('ServerConnectionTester', '服务器连接测试超时');
                }
            }
        }, this.config.timeout);
    };
    /**
     * 清除测试超时
     */
    ServerConnectionTester.prototype.clearTestTimeout = function () {
        if (this.timeoutId !== null) {
            clearTimeout(this.timeoutId);
            this.timeoutId = null;
        }
    };
    /**
     * 重置测试结果
     */
    ServerConnectionTester.prototype.resetTestResult = function () {
        this.currentResult = {
            success: false,
            startTime: 0,
            endTime: 0,
            duration: 0,
        };
    };
    /**
     * 获取测试结果
     * @returns 测试结果
     */
    ServerConnectionTester.prototype.getTestResult = function () {
        return this.currentResult;
    };
    /**
     * 测试服务器可用性
     */
    ServerConnectionTester.prototype.testAvailability = function () {
        return __awaiter(this, void 0, void 0, function () {
            var startTime_1, error_2;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', '开始测试服务器可用性');
                        }
                        // 发送可用性测试开始事件
                        this.emit('availabilityTestStart');
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        startTime_1 = Date.now();
                        // 模拟可用性测试
                        // 在实际应用中，这里应该发送一个简单的HTTP请求或WebSocket连接
                        return [4 /*yield*/, new Promise(function (resolve) {
                                setTimeout(function () {
                                    var responseTime = Date.now() - startTime_1;
                                    _this.currentResult.available = true;
                                    _this.currentResult.responseTime = responseTime;
                                    // 发送可用性测试完成事件
                                    _this.emit('availabilityTestComplete', {
                                        available: true,
                                        responseTime: responseTime,
                                    });
                                    if (_this.config.detailedLogging) {
                                        Debug_1.Debug.log('ServerConnectionTester', "\u670D\u52A1\u5668\u53EF\u7528\u6027\u6D4B\u8BD5\u5B8C\u6210: \u53EF\u7528=".concat(true, ", \u54CD\u5E94\u65F6\u95F4=").concat(responseTime, "ms"));
                                    }
                                    resolve();
                                }, 100 + Math.random() * 200); // 模拟100-300ms的响应时间
                            })];
                    case 2:
                        // 模拟可用性测试
                        // 在实际应用中，这里应该发送一个简单的HTTP请求或WebSocket连接
                        _a.sent();
                        return [3 /*break*/, 4];
                    case 3:
                        error_2 = _a.sent();
                        this.currentResult.available = false;
                        // 发送可用性测试失败事件
                        this.emit('availabilityTestError', {
                            error: error_2 instanceof Error ? error_2.message : String(error_2),
                        });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', "\u670D\u52A1\u5668\u53EF\u7528\u6027\u6D4B\u8BD5\u5931\u8D25: ".concat(error_2));
                        }
                        throw error_2;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 测试WebSocket连接
     */
    ServerConnectionTester.prototype.testWebSocketConnection = function () {
        return __awaiter(this, void 0, void 0, function () {
            var startTime_2, error_3;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', '开始测试WebSocket连接');
                        }
                        // 发送WebSocket测试开始事件
                        this.emit('webSocketTestStart');
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        startTime_2 = Date.now();
                        // 模拟WebSocket连接测试
                        // 在实际应用中，这里应该创建一个WebSocket连接
                        return [4 /*yield*/, new Promise(function (resolve) {
                                setTimeout(function () {
                                    var connectionTime = Date.now() - startTime_2;
                                    _this.currentResult.webSocketTest = {
                                        success: true,
                                        connectionTime: connectionTime,
                                    };
                                    // 发送WebSocket测试完成事件
                                    _this.emit('webSocketTestComplete', {
                                        success: true,
                                        connectionTime: connectionTime,
                                    });
                                    if (_this.config.detailedLogging) {
                                        Debug_1.Debug.log('ServerConnectionTester', "WebSocket\u8FDE\u63A5\u6D4B\u8BD5\u5B8C\u6210: \u6210\u529F=".concat(true, ", \u8FDE\u63A5\u65F6\u95F4=").concat(connectionTime, "ms"));
                                    }
                                    resolve();
                                }, 150 + Math.random() * 250); // 模拟150-400ms的连接时间
                            })];
                    case 2:
                        // 模拟WebSocket连接测试
                        // 在实际应用中，这里应该创建一个WebSocket连接
                        _a.sent();
                        return [3 /*break*/, 4];
                    case 3:
                        error_3 = _a.sent();
                        this.currentResult.webSocketTest = {
                            success: false,
                            connectionTime: 0,
                            error: error_3 instanceof Error ? error_3.message : String(error_3),
                        };
                        // 发送WebSocket测试失败事件
                        this.emit('webSocketTestError', {
                            error: this.currentResult.webSocketTest.error,
                        });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', "WebSocket\u8FDE\u63A5\u6D4B\u8BD5\u5931\u8D25: ".concat(error_3));
                        }
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 测试HTTP连接
     */
    ServerConnectionTester.prototype.testHttpConnection = function () {
        return __awaiter(this, void 0, void 0, function () {
            var startTime_3, error_4;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // 实现HTTP连接测试
                        // 这里使用模拟数据
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', '开始测试HTTP连接');
                        }
                        // 发送HTTP测试开始事件
                        this.emit('httpTestStart');
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        startTime_3 = Date.now();
                        // 模拟HTTP连接测试
                        return [4 /*yield*/, new Promise(function (resolve) {
                                setTimeout(function () {
                                    var responseTime = Date.now() - startTime_3;
                                    _this.currentResult.httpTest = {
                                        success: true,
                                        responseTime: responseTime,
                                        statusCode: 200,
                                    };
                                    // 发送HTTP测试完成事件
                                    _this.emit('httpTestComplete', {
                                        success: true,
                                        responseTime: responseTime,
                                        statusCode: 200,
                                    });
                                    if (_this.config.detailedLogging) {
                                        Debug_1.Debug.log('ServerConnectionTester', "HTTP\u8FDE\u63A5\u6D4B\u8BD5\u5B8C\u6210: \u6210\u529F=".concat(true, ", \u54CD\u5E94\u65F6\u95F4=").concat(responseTime, "ms, \u72B6\u6001\u7801=200"));
                                    }
                                    resolve();
                                }, 120 + Math.random() * 180); // 模拟120-300ms的响应时间
                            })];
                    case 2:
                        // 模拟HTTP连接测试
                        _a.sent();
                        return [3 /*break*/, 4];
                    case 3:
                        error_4 = _a.sent();
                        this.currentResult.httpTest = {
                            success: false,
                            responseTime: 0,
                            statusCode: 0,
                            error: error_4 instanceof Error ? error_4.message : String(error_4),
                        };
                        // 发送HTTP测试失败事件
                        this.emit('httpTestError', {
                            error: this.currentResult.httpTest.error,
                        });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', "HTTP\u8FDE\u63A5\u6D4B\u8BD5\u5931\u8D25: ".concat(error_4));
                        }
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 测试服务器状态
     */
    ServerConnectionTester.prototype.testServerStatus = function () {
        return __awaiter(this, void 0, void 0, function () {
            var error_5;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // 实现服务器状态测试
                        // 这里使用模拟数据
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', '开始测试服务器状态');
                        }
                        // 发送服务器状态测试开始事件
                        this.emit('serverStatusTestStart');
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        // 模拟服务器状态测试
                        return [4 /*yield*/, new Promise(function (resolve) {
                                setTimeout(function () {
                                    _this.currentResult.serverStatusTest = {
                                        success: true,
                                        status: 'running',
                                        version: '1.0.0',
                                        load: 0.35,
                                    };
                                    // 发送服务器状态测试完成事件
                                    _this.emit('serverStatusTestComplete', {
                                        success: true,
                                        status: 'running',
                                        version: '1.0.0',
                                        load: 0.35,
                                    });
                                    if (_this.config.detailedLogging) {
                                        Debug_1.Debug.log('ServerConnectionTester', "\u670D\u52A1\u5668\u72B6\u6001\u6D4B\u8BD5\u5B8C\u6210: \u6210\u529F=".concat(true, ", \u72B6\u6001=running, \u7248\u672C=1.0.0, \u8D1F\u8F7D=0.35"));
                                    }
                                    resolve();
                                }, 200); // 模拟200ms的响应时间
                            })];
                    case 2:
                        // 模拟服务器状态测试
                        _a.sent();
                        return [3 /*break*/, 4];
                    case 3:
                        error_5 = _a.sent();
                        this.currentResult.serverStatusTest = {
                            success: false,
                            status: 'unknown',
                            error: error_5 instanceof Error ? error_5.message : String(error_5),
                        };
                        // 发送服务器状态测试失败事件
                        this.emit('serverStatusTestError', {
                            error: this.currentResult.serverStatusTest.error,
                        });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', "\u670D\u52A1\u5668\u72B6\u6001\u6D4B\u8BD5\u5931\u8D25: ".concat(error_5));
                        }
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 测试DNS解析
     */
    ServerConnectionTester.prototype.testDnsResolution = function () {
        return __awaiter(this, void 0, void 0, function () {
            var startTime_4, error_6;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // 实现DNS解析测试
                        // 这里使用模拟数据
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', '开始测试DNS解析');
                        }
                        // 发送DNS解析测试开始事件
                        this.emit('dnsResolutionTestStart');
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        startTime_4 = Date.now();
                        // 模拟DNS解析测试
                        return [4 /*yield*/, new Promise(function (resolve) {
                                setTimeout(function () {
                                    var resolutionTime = Date.now() - startTime_4;
                                    _this.currentResult.dnsResolutionTest = {
                                        success: true,
                                        resolutionTime: resolutionTime,
                                        ipAddress: '***********',
                                    };
                                    // 发送DNS解析测试完成事件
                                    _this.emit('dnsResolutionTestComplete', {
                                        success: true,
                                        resolutionTime: resolutionTime,
                                        ipAddress: '***********',
                                    });
                                    if (_this.config.detailedLogging) {
                                        Debug_1.Debug.log('ServerConnectionTester', "DNS\u89E3\u6790\u6D4B\u8BD5\u5B8C\u6210: \u6210\u529F=".concat(true, ", \u89E3\u6790\u65F6\u95F4=").concat(resolutionTime, "ms, IP=***********"));
                                    }
                                    resolve();
                                }, 50 + Math.random() * 100); // 模拟50-150ms的解析时间
                            })];
                    case 2:
                        // 模拟DNS解析测试
                        _a.sent();
                        return [3 /*break*/, 4];
                    case 3:
                        error_6 = _a.sent();
                        this.currentResult.dnsResolutionTest = {
                            success: false,
                            resolutionTime: 0,
                            error: error_6 instanceof Error ? error_6.message : String(error_6),
                        };
                        // 发送DNS解析测试失败事件
                        this.emit('dnsResolutionTestError', {
                            error: this.currentResult.dnsResolutionTest.error,
                        });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', "DNS\u89E3\u6790\u6D4B\u8BD5\u5931\u8D25: ".concat(error_6));
                        }
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 测试SSL证书
     */
    ServerConnectionTester.prototype.testSslCertificate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var error_7;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // 实现SSL证书测试
                        // 这里使用模拟数据
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', '开始测试SSL证书');
                        }
                        // 发送SSL证书测试开始事件
                        this.emit('sslCertificateTestStart');
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        // 模拟SSL证书测试
                        return [4 /*yield*/, new Promise(function (resolve) {
                                setTimeout(function () {
                                    var _a, _b;
                                    _this.currentResult.sslCertificateTest = {
                                        success: true,
                                        valid: true,
                                        expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                                        issuer: 'Let\'s Encrypt',
                                    };
                                    // 发送SSL证书测试完成事件
                                    _this.emit('sslCertificateTestComplete', {
                                        success: true,
                                        valid: true,
                                        expiryDate: (_a = _this.currentResult.sslCertificateTest) === null || _a === void 0 ? void 0 : _a.expiryDate,
                                        issuer: 'Let\'s Encrypt',
                                    });
                                    if (_this.config.detailedLogging) {
                                        Debug_1.Debug.log('ServerConnectionTester', "SSL\u8BC1\u4E66\u6D4B\u8BD5\u5B8C\u6210: \u6210\u529F=".concat(true, ", \u6709\u6548=").concat(true, ", \u8FC7\u671F\u65F6\u95F4=").concat((_b = _this.currentResult.sslCertificateTest) === null || _b === void 0 ? void 0 : _b.expiryDate, ", \u9881\u53D1\u8005=Let's Encrypt"));
                                    }
                                    resolve();
                                }, 150); // 模拟150ms的响应时间
                            })];
                    case 2:
                        // 模拟SSL证书测试
                        _a.sent();
                        return [3 /*break*/, 4];
                    case 3:
                        error_7 = _a.sent();
                        this.currentResult.sslCertificateTest = {
                            success: false,
                            valid: false,
                            error: error_7 instanceof Error ? error_7.message : String(error_7),
                        };
                        // 发送SSL证书测试失败事件
                        this.emit('sslCertificateTestError', {
                            error: this.currentResult.sslCertificateTest.error,
                        });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', "SSL\u8BC1\u4E66\u6D4B\u8BD5\u5931\u8D25: ".concat(error_7));
                        }
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 测试服务器地理位置
     */
    ServerConnectionTester.prototype.testGeoLocation = function () {
        return __awaiter(this, void 0, void 0, function () {
            var error_8;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // 实现服务器地理位置测试
                        // 这里使用模拟数据
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', '开始测试服务器地理位置');
                        }
                        // 发送地理位置测试开始事件
                        this.emit('geoLocationTestStart');
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        // 模拟地理位置测试
                        return [4 /*yield*/, new Promise(function (resolve) {
                                setTimeout(function () {
                                    _this.currentResult.geoLocationTest = {
                                        success: true,
                                        country: '中国',
                                        city: '北京',
                                        longitude: 116.4074,
                                        latitude: 39.9042,
                                    };
                                    // 发送地理位置测试完成事件
                                    _this.emit('geoLocationTestComplete', {
                                        success: true,
                                        country: '中国',
                                        city: '北京',
                                        longitude: 116.4074,
                                        latitude: 39.9042,
                                    });
                                    if (_this.config.detailedLogging) {
                                        Debug_1.Debug.log('ServerConnectionTester', "\u670D\u52A1\u5668\u5730\u7406\u4F4D\u7F6E\u6D4B\u8BD5\u5B8C\u6210: \u6210\u529F=".concat(true, ", \u56FD\u5BB6=\u4E2D\u56FD, \u57CE\u5E02=\u5317\u4EAC, \u7ECF\u5EA6=116.4074, \u7EAC\u5EA6=39.9042"));
                                    }
                                    resolve();
                                }, 200); // 模拟200ms的响应时间
                            })];
                    case 2:
                        // 模拟地理位置测试
                        _a.sent();
                        return [3 /*break*/, 4];
                    case 3:
                        error_8 = _a.sent();
                        this.currentResult.geoLocationTest = {
                            success: false,
                            error: error_8 instanceof Error ? error_8.message : String(error_8),
                        };
                        // 发送地理位置测试失败事件
                        this.emit('geoLocationTestError', {
                            error: this.currentResult.geoLocationTest.error,
                        });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', "\u670D\u52A1\u5668\u5730\u7406\u4F4D\u7F6E\u6D4B\u8BD5\u5931\u8D25: ".concat(error_8));
                        }
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 测试路由跟踪
     */
    ServerConnectionTester.prototype.testRouteTrace = function () {
        return __awaiter(this, void 0, void 0, function () {
            var error_9;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // 实现路由跟踪测试
                        // 这里使用模拟数据
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', '开始测试路由跟踪');
                        }
                        // 发送路由跟踪测试开始事件
                        this.emit('routeTraceTestStart');
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        // 模拟路由跟踪测试
                        return [4 /*yield*/, new Promise(function (resolve) {
                                setTimeout(function () {
                                    var _a;
                                    _this.currentResult.routeTraceTest = {
                                        success: true,
                                        hops: 5,
                                        nodes: [
                                            { ip: '***********', responseTime: 1, location: '本地网关' },
                                            { ip: '********', responseTime: 10, location: '本地ISP' },
                                            { ip: '**********', responseTime: 30, location: '区域节点' },
                                            { ip: '***********', responseTime: 50, location: '骨干网' },
                                            { ip: '************', responseTime: 80, location: '目标服务器' },
                                        ],
                                    };
                                    // 发送路由跟踪测试完成事件
                                    _this.emit('routeTraceTestComplete', {
                                        success: true,
                                        hops: 5,
                                        nodes: (_a = _this.currentResult.routeTraceTest) === null || _a === void 0 ? void 0 : _a.nodes,
                                    });
                                    if (_this.config.detailedLogging) {
                                        Debug_1.Debug.log('ServerConnectionTester', "\u8DEF\u7531\u8DDF\u8E2A\u6D4B\u8BD5\u5B8C\u6210: \u6210\u529F=".concat(true, ", \u8DF3\u6570=5"));
                                    }
                                    resolve();
                                }, 500); // 模拟500ms的响应时间
                            })];
                    case 2:
                        // 模拟路由跟踪测试
                        _a.sent();
                        return [3 /*break*/, 4];
                    case 3:
                        error_9 = _a.sent();
                        this.currentResult.routeTraceTest = {
                            success: false,
                            hops: 0,
                            nodes: [],
                            error: error_9 instanceof Error ? error_9.message : String(error_9),
                        };
                        // 发送路由跟踪测试失败事件
                        this.emit('routeTraceTestError', {
                            error: this.currentResult.routeTraceTest.error,
                        });
                        if (this.config.detailedLogging) {
                            Debug_1.Debug.log('ServerConnectionTester', "\u8DEF\u7531\u8DDF\u8E2A\u6D4B\u8BD5\u5931\u8D25: ".concat(error_9));
                        }
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 销毁测试器
     */
    ServerConnectionTester.prototype.dispose = function () {
        if (this.testing) {
            this.cancelTest();
        }
        this.removeAllListeners();
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('ServerConnectionTester', '服务器连接测试器已销毁');
        }
    };
    return ServerConnectionTester;
}(EventEmitter_1.EventEmitter));
exports.ServerConnectionTester = ServerConnectionTester;

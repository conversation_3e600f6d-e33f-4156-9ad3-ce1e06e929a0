"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlendSpace1D = void 0;
/**
 * 一维混合空间
 */
var BlendSpace1D = /** @class */ (function () {
    /**
     * 构造函数
     * @param config 混合空间配置
     * @param name 名称
     * @param description 描述
     */
    function BlendSpace1D(config, name, description) {
        if (name === void 0) { name = ''; }
        if (description === void 0) { description = ''; }
        /** 节点列表 */
        this.nodes = [];
        /** 当前位置 */
        this.position = 0;
        /** 目标位置 */
        this.targetPosition = 0;
        /** 上一次更新时间 */
        this.lastUpdateTime = 0;
        /** 是否已初始化 */
        this.initialized = false;
        /** 名称 */
        this.name = '';
        /** 描述 */
        this.description = '';
        this.config = {
            minValue: config.minValue,
            maxValue: config.maxValue,
            normalizeInput: config.normalizeInput !== undefined ? config.normalizeInput : true,
            useSmoothing: config.useSmoothing !== undefined ? config.useSmoothing : false,
            smoothingFactor: config.smoothingFactor !== undefined ? config.smoothingFactor : 0.5,
            enableExtrapolation: config.enableExtrapolation !== undefined ? config.enableExtrapolation : false
        };
        this.name = name;
        this.description = description;
        this.lastUpdateTime = Date.now();
    }
    /**
     * 添加节点
     * @param clip 动画片段
     * @param position 位置
     * @param userData 用户数据
     * @returns 添加的节点
     */
    BlendSpace1D.prototype.addNode = function (clip, position, userData) {
        // 检查位置是否在范围内
        if (position < this.config.minValue || position > this.config.maxValue) {
            console.warn('BlendSpace1D: 节点位置超出范围', position);
        }
        var node = {
            clip: clip,
            position: position,
            weight: 0,
            userData: userData
        };
        this.nodes.push(node);
        // 按位置排序节点
        this.sortNodes();
        return node;
    };
    /**
     * 移除节点
     * @param node 要移除的节点
     * @returns 是否成功移除
     */
    BlendSpace1D.prototype.removeNode = function (node) {
        var index = this.nodes.indexOf(node);
        if (index === -1)
            return false;
        this.nodes.splice(index, 1);
        return true;
    };
    /**
     * 设置当前位置
     * @param position 位置
     * @param immediate 是否立即更新（不使用平滑）
     */
    BlendSpace1D.prototype.setPosition = function (position, immediate) {
        if (immediate === void 0) { immediate = false; }
        // 处理外推
        if (this.config.enableExtrapolation) {
            // 不限制位置
            this.targetPosition = position;
        }
        else {
            // 限制在范围内
            this.targetPosition = Math.max(this.config.minValue, Math.min(this.config.maxValue, position));
        }
        // 如果立即更新或不使用平滑，则直接设置位置
        if (immediate || !this.config.useSmoothing) {
            this.position = this.targetPosition;
        }
        // 标记为已初始化
        this.initialized = true;
    };
    /**
     * 获取当前位置
     * @returns 当前位置
     */
    BlendSpace1D.prototype.getPosition = function () {
        return this.position;
    };
    /**
     * 获取目标位置
     * @returns 目标位置
     */
    BlendSpace1D.prototype.getTargetPosition = function () {
        return this.targetPosition;
    };
    /**
     * 更新混合权重
     * @param deltaTime 时间增量（秒），如果不提供则自动计算
     */
    BlendSpace1D.prototype.update = function (deltaTime) {
        // 如果没有节点，则不更新
        if (this.nodes.length === 0)
            return;
        // 如果没有初始化，则不更新
        if (!this.initialized)
            return;
        // 计算时间增量
        var currentTime = Date.now();
        var dt = deltaTime !== undefined ? deltaTime : (currentTime - this.lastUpdateTime) / 1000;
        this.lastUpdateTime = currentTime;
        // 更新位置
        if (this.config.useSmoothing && this.position !== this.targetPosition) {
            // 使用平滑因子计算新位置
            var smoothFactor = Math.min(1.0, dt / (1.0 - this.config.smoothingFactor));
            this.position = this.position + (this.targetPosition - this.position) * smoothFactor;
            // 如果接近目标位置，则直接设置为目标位置
            if (Math.abs(this.position - this.targetPosition) < 0.001) {
                this.position = this.targetPosition;
            }
        }
        // 如果只有一个节点，则权重为1
        if (this.nodes.length === 1) {
            this.nodes[0].weight = 1;
            return;
        }
        // 找到当前位置所在的区间
        var _a = this.findNearestNodes(this.position), nodeA = _a[0], nodeB = _a[1];
        // 如果找不到节点，则不更新
        if (!nodeA || !nodeB)
            return;
        // 重置所有节点的权重
        for (var _i = 0, _b = this.nodes; _i < _b.length; _i++) {
            var node = _b[_i];
            node.weight = 0;
        }
        // 如果两个节点相同，则权重为1
        if (nodeA === nodeB) {
            nodeA.weight = 1;
            return;
        }
        // 计算插值因子
        var t = (this.position - nodeA.position) / (nodeB.position - nodeA.position);
        // 设置权重
        nodeA.weight = 1 - t;
        nodeB.weight = t;
    };
    /**
     * 按位置排序节点
     */
    BlendSpace1D.prototype.sortNodes = function () {
        this.nodes.sort(function (a, b) { return a.position - b.position; });
    };
    /**
     * 找到最近的两个节点
     * @param position 位置
     * @returns 最近的两个节点
     */
    BlendSpace1D.prototype.findNearestNodes = function (position) {
        // 如果没有节点，则返回null
        if (this.nodes.length === 0)
            return [null, null];
        // 如果只有一个节点，则返回相同的节点
        if (this.nodes.length === 1)
            return [this.nodes[0], this.nodes[0]];
        // 如果位置小于等于最小节点位置，则返回最小的两个节点
        if (position <= this.nodes[0].position) {
            return [this.nodes[0], this.nodes[0]];
        }
        // 如果位置大于等于最大节点位置，则返回最大的两个节点
        if (position >= this.nodes[this.nodes.length - 1].position) {
            return [this.nodes[this.nodes.length - 1], this.nodes[this.nodes.length - 1]];
        }
        // 找到位置所在的区间
        for (var i = 0; i < this.nodes.length - 1; i++) {
            if (position >= this.nodes[i].position && position <= this.nodes[i + 1].position) {
                return [this.nodes[i], this.nodes[i + 1]];
            }
        }
        // 如果找不到，则返回null
        return [null, null];
    };
    /**
     * 获取所有节点
     * @returns 节点列表
     */
    BlendSpace1D.prototype.getNodes = function () {
        return this.nodes;
    };
    /**
     * 获取活跃节点（权重大于0的节点）
     * @returns 活跃节点列表
     */
    BlendSpace1D.prototype.getActiveNodes = function () {
        return this.nodes.filter(function (node) { return node.weight > 0; });
    };
    /**
     * 获取配置
     * @returns 混合空间配置
     */
    BlendSpace1D.prototype.getConfig = function () {
        return __assign({}, this.config);
    };
    /**
     * 设置配置
     * @param config 混合空间配置
     */
    BlendSpace1D.prototype.setConfig = function (config) {
        // 更新配置
        if (config.minValue !== undefined)
            this.config.minValue = config.minValue;
        if (config.maxValue !== undefined)
            this.config.maxValue = config.maxValue;
        if (config.normalizeInput !== undefined)
            this.config.normalizeInput = config.normalizeInput;
        if (config.useSmoothing !== undefined)
            this.config.useSmoothing = config.useSmoothing;
        if (config.smoothingFactor !== undefined)
            this.config.smoothingFactor = config.smoothingFactor;
        if (config.enableExtrapolation !== undefined)
            this.config.enableExtrapolation = config.enableExtrapolation;
        // 确保当前位置在范围内
        if (!this.config.enableExtrapolation) {
            this.position = Math.max(this.config.minValue, Math.min(this.config.maxValue, this.position));
            this.targetPosition = Math.max(this.config.minValue, Math.min(this.config.maxValue, this.targetPosition));
        }
    };
    /**
     * 获取名称
     * @returns 名称
     */
    BlendSpace1D.prototype.getName = function () {
        return this.name;
    };
    /**
     * 设置名称
     * @param name 名称
     */
    BlendSpace1D.prototype.setName = function (name) {
        this.name = name;
    };
    /**
     * 获取描述
     * @returns 描述
     */
    BlendSpace1D.prototype.getDescription = function () {
        return this.description;
    };
    /**
     * 设置描述
     * @param description 描述
     */
    BlendSpace1D.prototype.setDescription = function (description) {
        this.description = description;
    };
    /**
     * 清空所有节点
     */
    BlendSpace1D.prototype.clearNodes = function () {
        this.nodes = [];
    };
    /**
     * 获取节点数量
     * @returns 节点数量
     */
    BlendSpace1D.prototype.getNodeCount = function () {
        return this.nodes.length;
    };
    /**
     * 获取节点
     * @param index 节点索引
     * @returns 节点，如果不存在则返回null
     */
    BlendSpace1D.prototype.getNode = function (index) {
        if (index < 0 || index >= this.nodes.length) {
            return null;
        }
        return this.nodes[index];
    };
    /**
     * 更新节点位置
     * @param index 节点索引
     * @param position 新位置
     * @returns 是否成功更新
     */
    BlendSpace1D.prototype.updateNodePosition = function (index, position) {
        if (index < 0 || index >= this.nodes.length) {
            return false;
        }
        // 检查位置是否在范围内
        if (position < this.config.minValue || position > this.config.maxValue) {
            console.warn('BlendSpace1D: 节点位置超出范围', position);
        }
        // 更新位置
        this.nodes[index].position = position;
        // 重新排序
        this.sortNodes();
        return true;
    };
    /**
     * 序列化为JSON
     * @returns JSON对象
     */
    BlendSpace1D.prototype.toJSON = function () {
        return {
            name: this.name,
            description: this.description,
            config: __assign({}, this.config),
            nodes: this.nodes.map(function (node) { return ({
                clipName: node.clip.name,
                position: node.position,
                userData: node.userData
            }); })
        };
    };
    /**
     * 从JSON创建混合空间
     * @param json JSON对象
     * @param getClip 获取动画片段的函数
     * @returns 混合空间
     */
    BlendSpace1D.fromJSON = function (json, getClip) {
        // 创建混合空间
        var blendSpace = new BlendSpace1D(json.config, json.name, json.description);
        // 添加节点
        for (var _i = 0, _a = json.nodes; _i < _a.length; _i++) {
            var nodeData = _a[_i];
            var clip = getClip(nodeData.clipName);
            if (clip) {
                blendSpace.addNode(clip, nodeData.position, nodeData.userData);
            }
            else {
                console.warn("BlendSpace1D: \u627E\u4E0D\u5230\u52A8\u753B\u7247\u6BB5 \"".concat(nodeData.clipName, "\""));
            }
        }
        return blendSpace;
    };
    return BlendSpace1D;
}());
exports.BlendSpace1D = BlendSpace1D;

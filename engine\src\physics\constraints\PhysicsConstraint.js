"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhysicsConstraint = exports.ConstraintType = void 0;
var Component_1 = require("../../core/Component");
/**
 * 物理约束类型
 */
var ConstraintType;
(function (ConstraintType) {
    /** 点对点约束 */
    ConstraintType["POINT_TO_POINT"] = "pointToPoint";
    /** 铰链约束 */
    ConstraintType["HINGE"] = "hinge";
    /** 距离约束 */
    ConstraintType["DISTANCE"] = "distance";
    /** 锁定约束 */
    ConstraintType["LOCK"] = "lock";
    /** 弹簧约束 */
    ConstraintType["SPRING"] = "spring";
    /** 圆锥扭转约束 */
    ConstraintType["CONE_TWIST"] = "coneTwist";
    /** 滑动约束 */
    ConstraintType["SLIDER"] = "slider";
    /** 固定约束 */
    ConstraintType["FIXED"] = "fixed";
    /** 车轮约束 */
    ConstraintType["WHEEL"] = "wheel";
})(ConstraintType || (exports.ConstraintType = ConstraintType = {}));
/**
 * 物理约束基类
 */
var PhysicsConstraint = exports.PhysicsConstraint = /** @class */ (function (_super) {
    __extends(PhysicsConstraint, _super);
    /**
     * 创建物理约束
     * @param type 约束类型
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    function PhysicsConstraint(type, targetEntity, options) {
        if (targetEntity === void 0) { targetEntity = null; }
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, PhysicsConstraint.type) || this;
        /** CANNON.js约束 */
        _this.constraint = null;
        /** 物理世界 */
        _this.world = null;
        /** 是否已初始化 */
        _this.initialized = false;
        /** 目标实体 */
        _this.targetEntity = null;
        /** 碰撞启用 */
        _this.collideConnected = true;
        _this.constraintType = type;
        _this.targetEntity = targetEntity;
        _this.collideConnected = options.collideConnected !== undefined ? options.collideConnected : true;
        return _this;
    }
    /**
     * 初始化约束
     * @param world 物理世界
     */
    PhysicsConstraint.prototype.initialize = function (world) {
        if (this.initialized)
            return;
        this.world = world;
        // 创建约束
        this.createConstraint();
        // 如果创建成功，添加到物理世界
        if (this.constraint) {
            world.addConstraint(this.constraint);
            this.initialized = true;
        }
    };
    /**
     * 获取源实体的物理体
     * @returns 源实体的物理体
     */
    PhysicsConstraint.prototype.getSourceBody = function () {
        if (!this.entity)
            return null;
        var physicsBody = this.entity.getComponent(PhysicsBody.type);
        if (!physicsBody)
            return null;
        return physicsBody.getCannonBody();
    };
    /**
     * 获取目标实体的物理体
     * @returns 目标实体的物理体
     */
    PhysicsConstraint.prototype.getTargetBody = function () {
        if (!this.targetEntity)
            return null;
        var physicsBody = this.targetEntity.getComponent(PhysicsBody.type);
        if (!physicsBody)
            return null;
        return physicsBody.getCannonBody();
    };
    /**
     * 设置目标实体
     * @param entity 目标实体
     */
    PhysicsConstraint.prototype.setTargetEntity = function (entity) {
        // 如果已初始化，需要重新创建约束
        if (this.initialized && this.constraint && this.world) {
            this.world.removeConstraint(this.constraint);
            this.constraint = null;
            this.initialized = false;
        }
        this.targetEntity = entity;
        // 如果有物理世界，重新初始化
        if (this.world) {
            this.initialize(this.world);
        }
    };
    /**
     * 获取目标实体
     * @returns 目标实体
     */
    PhysicsConstraint.prototype.getTargetEntity = function () {
        return this.targetEntity;
    };
    /**
     * 获取约束类型
     * @returns 约束类型
     */
    PhysicsConstraint.prototype.getConstraintType = function () {
        return this.constraintType;
    };
    /**
     * 获取CANNON.js约束
     * @returns CANNON.js约束
     */
    PhysicsConstraint.prototype.getCannonConstraint = function () {
        return this.constraint;
    };
    /**
     * 启用约束
     */
    PhysicsConstraint.prototype.enable = function () {
        if (!this.enabled && this.constraint) {
            this.constraint.enable();
            this.enabled = true;
        }
    };
    /**
     * 禁用约束
     */
    PhysicsConstraint.prototype.disable = function () {
        if (this.enabled && this.constraint) {
            this.constraint.disable();
            this.enabled = false;
        }
    };
    /**
     * 是否启用
     * @returns 是否启用
     */
    PhysicsConstraint.prototype.isEnabled = function () {
        return this.enabled;
    };
    /**
     * 设置是否允许连接的物体之间碰撞
     * @param collide 是否允许碰撞
     */
    PhysicsConstraint.prototype.setCollideConnected = function (collide) {
        this.collideConnected = collide;
        if (this.constraint) {
            this.constraint.collideConnected = collide;
        }
    };
    /**
     * 是否允许连接的物体之间碰撞
     * @returns 是否允许碰撞
     */
    PhysicsConstraint.prototype.isCollideConnected = function () {
        return this.collideConnected;
    };
    /**
     * 销毁约束
     */
    PhysicsConstraint.prototype.dispose = function () {
        if (this.initialized && this.constraint && this.world) {
            this.world.removeConstraint(this.constraint);
            this.constraint = null;
            this.world = null;
            this.initialized = false;
        }
        _super.prototype.dispose.call(this);
    };
    /** 组件类型 */
    PhysicsConstraint.type = 'PhysicsConstraint';
    return PhysicsConstraint;
}(Component_1.Component));

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhysicsRaycastResult = void 0;
/**
 * 物理射线检测结果
 * 用于存储射线检测的结果
 */
var THREE = require("three");
/**
 * 物理射线检测结果
 */
var PhysicsRaycastResult = /** @class */ (function () {
    /**
     * 创建物理射线检测结果
     * @param cannonResult CANNON射线检测结果
     * @param entityMap 物理体到实体的映射
     */
    function PhysicsRaycastResult(cannonResult, entityMap) {
        /** 是否命中 */
        this.hit = false;
        /** 命中点（世界坐标） */
        this.hitPoint = new THREE.Vector3();
        /** 命中法线（世界坐标） */
        this.hitNormal = new THREE.Vector3();
        /** 命中距离 */
        this.hitDistance = 0;
        /** 命中的物理体 */
        this.hitBody = null;
        /** 命中的实体 */
        this.hitEntity = null;
        /** 命中的形状 */
        this.hitShape = null;
        /** 原始CANNON射线检测结果 */
        this.cannonResult = null;
        if (cannonResult) {
            this.updateFromCannonResult(cannonResult, entityMap);
        }
    }
    /**
     * 是否命中
     * @returns 是否命中
     */
    PhysicsRaycastResult.prototype.hasHit = function () {
        return this.hit;
    };
    /**
     * 获取命中点（世界坐标）
     * @returns 命中点
     */
    PhysicsRaycastResult.prototype.getHitPoint = function () {
        return this.hitPoint.clone();
    };
    /**
     * 获取命中法线（世界坐标）
     * @returns 命中法线
     */
    PhysicsRaycastResult.prototype.getHitNormal = function () {
        return this.hitNormal.clone();
    };
    /**
     * 获取命中距离
     * @returns 命中距离
     */
    PhysicsRaycastResult.prototype.getHitDistance = function () {
        return this.hitDistance;
    };
    /**
     * 获取命中的物理体
     * @returns 命中的物理体
     */
    PhysicsRaycastResult.prototype.getHitBody = function () {
        return this.hitBody;
    };
    /**
     * 获取命中的实体
     * @returns 命中的实体
     */
    PhysicsRaycastResult.prototype.getHitEntity = function () {
        return this.hitEntity;
    };
    /**
     * 获取命中的形状
     * @returns 命中的形状
     */
    PhysicsRaycastResult.prototype.getHitShape = function () {
        return this.hitShape;
    };
    /**
     * 获取原始CANNON射线检测结果
     * @returns 原始CANNON射线检测结果
     */
    PhysicsRaycastResult.prototype.getCannonResult = function () {
        return this.cannonResult;
    };
    /**
     * 重置结果
     */
    PhysicsRaycastResult.prototype.reset = function () {
        this.hit = false;
        this.hitPoint.set(0, 0, 0);
        this.hitNormal.set(0, 0, 0);
        this.hitDistance = 0;
        this.hitBody = null;
        this.hitEntity = null;
        this.hitShape = null;
        this.cannonResult = null;
    };
    /**
     * 从CANNON射线检测结果更新
     * @param cannonResult CANNON射线检测结果
     * @param entityMap 物理体到实体的映射
     */
    PhysicsRaycastResult.prototype.updateFromCannonResult = function (cannonResult, entityMap) {
        this.cannonResult = cannonResult;
        this.hit = cannonResult.hasHit;
        if (this.hit) {
            this.hitPoint.set(cannonResult.hitPointWorld.x, cannonResult.hitPointWorld.y, cannonResult.hitPointWorld.z);
            this.hitNormal.set(cannonResult.hitNormalWorld.x, cannonResult.hitNormalWorld.y, cannonResult.hitNormalWorld.z);
            this.hitDistance = cannonResult.distance;
            this.hitBody = cannonResult.body;
            this.hitShape = cannonResult.shape;
            // 查找命中的实体
            if (entityMap && this.hitBody) {
                this.hitEntity = entityMap.get(this.hitBody) || null;
            }
            else {
                this.hitEntity = this.getEntityFromBody(this.hitBody);
            }
        }
        else {
            this.hitPoint.set(0, 0, 0);
            this.hitNormal.set(0, 0, 0);
            this.hitDistance = 0;
            this.hitBody = null;
            this.hitEntity = null;
            this.hitShape = null;
        }
    };
    /**
     * 从物理体获取实体
     * @param body 物理体
     * @returns 实体
     */
    PhysicsRaycastResult.prototype.getEntityFromBody = function (body) {
        if (!body)
            return null;
        // 使用类型断言访问自定义属性
        var userData = body.userData;
        if (!userData)
            return null;
        return userData.entity || null;
    };
    return PhysicsRaycastResult;
}());
exports.PhysicsRaycastResult = PhysicsRaycastResult;

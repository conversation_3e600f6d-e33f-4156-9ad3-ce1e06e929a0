/**
 * 物理风效果系统
 * 用于模拟基于物理的风效果，使植被的摆动更加真实
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { VegetationComponent } from '../components/VegetationComponent';
import { TerrainInstancedRenderingSystem } from '../../terrain/TerrainInstancedRenderingSystem';

/**
 * 风场类型
 */
export enum WindFieldType {
  /** 均匀风场 */
  UNIFORM = 'uniform',
  /** 湍流风场 */
  TURBULENT = 'turbulent',
  /** 旋转风场 */
  VORTEX = 'vortex',
  /** 脉冲风场 */
  PULSE = 'pulse',
  /** 自定义风场 */
  CUSTOM = 'custom'
}

/**
 * 风区域类型
 */
export enum WindZoneType {
  /** 全局风区域 */
  GLOBAL = 'global',
  /** 球形风区域 */
  SPHERE = 'sphere',
  /** 盒形风区域 */
  BOX = 'box',
  /** 圆柱形风区域 */
  CYLINDER = 'cylinder',
  /** 自定义风区域 */
  CUSTOM = 'custom'
}

/**
 * 风区域接口
 */
export interface WindZone {
  /** 区域类型 */
  type: WindZoneType;
  /** 区域位置 */
  position: THREE.Vector3;
  /** 区域大小 */
  size: THREE.Vector3;
  /** 区域旋转 */
  rotation: THREE.Euler;
  /** 风场类型 */
  fieldType: WindFieldType;
  /** 风力强度 */
  strength: number;
  /** 风力方向 */
  direction: THREE.Vector3;
  /** 风力频率 */
  frequency: number;
  /** 湍流强度 */
  turbulence: number;
  /** 衰减距离 */
  falloffDistance: number;
  /** 是否启用 */
  enabled: boolean;
  /** 自定义数据 */
  userData?: any;
}

/**
 * 物理风效果系统配置
 */
export interface PhysicalWindSystemConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否使用物理模拟 */
  usePhysics?: boolean;
  /** 是否使用GPU加速 */
  useGPU?: boolean;
  /** 是否使用风区域 */
  useWindZones?: boolean;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
  /** 全局风力参数 */
  globalWind?: {
    /** 风场类型 */
    fieldType?: WindFieldType;
    /** 风力强度 */
    strength?: number;
    /** 风力方向 */
    direction?: THREE.Vector3;
    /** 风力频率 */
    frequency?: number;
    /** 湍流强度 */
    turbulence?: number;
  };
}

/**
 * 物理风效果系统事件类型
 */
export enum PhysicalWindSystemEventType {
  /** 风区域添加 */
  WIND_ZONE_ADDED = 'wind_zone_added',
  /** 风区域移除 */
  WIND_ZONE_REMOVED = 'wind_zone_removed',
  /** 风区域更新 */
  WIND_ZONE_UPDATED = 'wind_zone_updated',
  /** 全局风力更新 */
  GLOBAL_WIND_UPDATED = 'global_wind_updated'
}

/**
 * 物理风效果系统
 */
export class PhysicalWindSystem extends System {
  /** 系统类型 */
  public static readonly TYPE: string = 'PhysicalWindSystem';

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: PhysicalWindSystemConfig = {
    enabled: true,
    autoUpdate: true,
    updateFrequency: 1,
    usePhysics: true,
    useGPU: false,
    useWindZones: true,
    useDebugVisualization: false,
    globalWind: {
      fieldType: WindFieldType.UNIFORM,
      strength: 0.1,
      direction: new THREE.Vector3(1, 0, 0),
      frequency: 0.2,
      turbulence: 0.1
    }
  };

  /** 配置 */
  private config: PhysicalWindSystemConfig;

  /** 是否启用 */
  private enabled: boolean;

  /** 是否自动更新 */
  private autoUpdate: boolean;

  /** 更新频率 */
  private updateFrequency: number;

  /** 帧计数器 */
  private frameCount: number = 0;

  /** 是否使用物理模拟 */
  private usePhysics: boolean;

  /** 是否使用GPU加速 */
  private useGPU: boolean;

  /** 是否使用风区域 */
  private useWindZones: boolean;

  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** 全局风力参数 */
  private globalWind: {
    fieldType: WindFieldType;
    strength: number;
    direction: THREE.Vector3;
    frequency: number;
    turbulence: number;
  };

  /** 风区域列表 */
  private windZones: Map<string, WindZone> = new Map();

  /** 植被组件列表 */
  private vegetationComponents: Map<Entity, VegetationComponent> = new Map();

  /** 实例化渲染系统 */
  private instancedRenderingSystem: TerrainInstancedRenderingSystem | null = null;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor = new PerformanceMonitor();

  /** 当前时间 */
  private time: number = 0;

  /** 调试网格列表 */
  private debugMeshes: THREE.Object3D[] = [];

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config?: PhysicalWindSystemConfig) {
    super(world);

    // 合并配置
    this.config = { ...PhysicalWindSystem.DEFAULT_CONFIG, ...config };

    // 设置属性
    this.enabled = this.config.enabled !== undefined ? this.config.enabled : true;
    this.autoUpdate = this.config.autoUpdate !== undefined ? this.config.autoUpdate : true;
    this.updateFrequency = this.config.updateFrequency || 1;
    this.usePhysics = this.config.usePhysics !== undefined ? this.config.usePhysics : true;
    this.useGPU = this.config.useGPU !== undefined ? this.config.useGPU : false;
    this.useWindZones = this.config.useWindZones !== undefined ? this.config.useWindZones : true;
    this.useDebugVisualization = this.config.useDebugVisualization !== undefined ? this.config.useDebugVisualization : false;

    // 设置全局风力参数
    this.globalWind = {
      fieldType: this.config.globalWind?.fieldType || WindFieldType.UNIFORM,
      strength: this.config.globalWind?.strength || 0.1,
      direction: this.config.globalWind?.direction?.clone() || new THREE.Vector3(1, 0, 0),
      frequency: this.config.globalWind?.frequency || 0.2,
      turbulence: this.config.globalWind?.turbulence || 0.1
    };
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return PhysicalWindSystem.TYPE;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();

    // 查找实例化渲染系统
    this.instancedRenderingSystem = this.world.getSystem(TerrainInstancedRenderingSystem.TYPE) as TerrainInstancedRenderingSystem;
    if (!this.instancedRenderingSystem && this.usePhysics) {
      Debug.warn('PhysicalWindSystem', '找不到TerrainInstancedRenderingSystem，物理风效果可能无法正常工作');
    }

    // 初始化调试可视化
    if (this.useDebugVisualization) {
      this.initDebugVisualization();
    }

    Debug.log('PhysicalWindSystem', '物理风效果系统初始化完成');
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    // 清除调试网格
    this.clearDebugMeshes();

    // 清除风区域
    this.windZones.clear();

    // 清除植被组件
    this.vegetationComponents.clear();

    super.destroy();
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled || !this.autoUpdate) {
      return;
    }

    // 更新时间
    this.time += deltaTime;

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.updateFrequency !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.useDebugVisualization) {
      this.performanceMonitor.start('physicalWindUpdate');
    }

    // 更新所有植被组件
    this.updateVegetationComponents(deltaTime);

    // 如果启用了调试可视化，更新调试可视化
    if (this.useDebugVisualization) {
      this.updateDebugVisualization();
      this.performanceMonitor.end('physicalWindUpdate');
    }
  }

  /**
   * 添加植被组件
   * @param entity 实体
   * @param component 植被组件
   */
  public addVegetationComponent(entity: Entity, component: VegetationComponent): void {
    this.vegetationComponents.set(entity, component);
  }

  /**
   * 移除植被组件
   * @param entity 实体
   */
  public removeVegetationComponent(entity: Entity): void {
    this.vegetationComponents.delete(entity);
  }

  /**
   * 添加风区域
   * @param id 风区域ID
   * @param zone 风区域
   */
  public addWindZone(id: string, zone: WindZone): void {
    this.windZones.set(id, zone);

    // 如果启用了调试可视化，创建调试网格
    if (this.useDebugVisualization) {
      this.createWindZoneDebugMesh(id, zone);
    }

    // 发出风区域添加事件
    this.eventEmitter.emit(PhysicalWindSystemEventType.WIND_ZONE_ADDED, id, zone);
  }

  /**
   * 移除风区域
   * @param id 风区域ID
   */
  public removeWindZone(id: string): void {
    // 获取风区域
    const zone = this.windZones.get(id);
    if (!zone) {
      return;
    }

    // 从风区域列表中移除
    this.windZones.delete(id);

    // 如果启用了调试可视化，移除调试网格
    if (this.useDebugVisualization) {
      this.removeWindZoneDebugMesh(id);
    }

    // 发出风区域移除事件
    this.eventEmitter.emit(PhysicalWindSystemEventType.WIND_ZONE_REMOVED, id, zone);
  }

  /**
   * 更新风区域
   * @param id 风区域ID
   * @param zone 风区域
   */
  public updateWindZone(id: string, zone: Partial<WindZone>): void {
    // 获取风区域
    const existingZone = this.windZones.get(id);
    if (!existingZone) {
      return;
    }

    // 更新风区域属性
    Object.assign(existingZone, zone);

    // 如果启用了调试可视化，更新调试网格
    if (this.useDebugVisualization) {
      this.updateWindZoneDebugMesh(id, existingZone);
    }

    // 发出风区域更新事件
    this.eventEmitter.emit(PhysicalWindSystemEventType.WIND_ZONE_UPDATED, id, existingZone);
  }

  /**
   * 设置全局风力参数
   * @param params 风力参数
   */
  public setGlobalWind(params: Partial<typeof this.globalWind>): void {
    // 更新全局风力参数
    if (params.fieldType !== undefined) {
      this.globalWind.fieldType = params.fieldType;
    }
    if (params.strength !== undefined) {
      this.globalWind.strength = params.strength;
    }
    if (params.direction !== undefined) {
      this.globalWind.direction.copy(params.direction);
    }
    if (params.frequency !== undefined) {
      this.globalWind.frequency = params.frequency;
    }
    if (params.turbulence !== undefined) {
      this.globalWind.turbulence = params.turbulence;
    }

    // 发出全局风力更新事件
    this.eventEmitter.emit(PhysicalWindSystemEventType.GLOBAL_WIND_UPDATED, this.globalWind);
  }

  /**
   * 获取全局风力参数
   * @returns 全局风力参数
   */
  public getGlobalWind(): typeof this.globalWind {
    return { ...this.globalWind };
  }

  /**
   * 更新所有植被组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateVegetationComponents(deltaTime: number): void {
    // 遍历所有植被组件
    for (const [entity, component] of this.vegetationComponents.entries()) {
      // 如果组件未启用风效果，则跳过
      if (!component.useWind) {
        continue;
      }

      // 更新植被风效果
      this.updateVegetationWindEffect(entity, component, deltaTime);
    }
  }

  /**
   * 更新植被风效果
   * @param entity 实体
   * @param component 植被组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateVegetationWindEffect(entity: Entity, component: VegetationComponent, deltaTime: number): void {
    // 遍历所有实例
    for (const [instanceId, instance] of component.instances.entries()) {
      // 获取植被项
      const item = component.items[instance.itemIndex];
      if (!item.windEffect) {
        continue;
      }

      // 计算风力
      const windForce = this.calculateWindForce(instance.position, deltaTime);

      // 应用风力
      this.applyWindForce(entity, component, instanceId, instance, windForce, deltaTime);
    }
  }

  /**
   * 计算风力
   * @param position 位置
   * @param deltaTime 帧间隔时间（秒）
   * @returns 风力向量
   */
  private calculateWindForce(position: THREE.Vector3, deltaTime: number): THREE.Vector3 {
    // 创建风力向量
    const windForce = new THREE.Vector3();

    // 如果使用风区域，计算风区域风力
    if (this.useWindZones && this.windZones.size > 0) {
      // 遍历所有风区域
      for (const [id, zone] of this.windZones.entries()) {
        // 如果风区域未启用，则跳过
        if (!zone.enabled) {
          continue;
        }

        // 计算风区域风力
        const zoneForce = this.calculateWindZoneForce(position, zone, deltaTime);

        // 累加风力
        windForce.add(zoneForce);
      }
    } else {
      // 否则，使用全局风力
      windForce.copy(this.calculateGlobalWindForce(position, deltaTime));
    }

    return windForce;
  }

  /**
   * 计算全局风力
   * @param position 位置
   * @param deltaTime 帧间隔时间（秒）
   * @returns 风力向量
   */
  private calculateGlobalWindForce(position: THREE.Vector3, deltaTime: number): THREE.Vector3 {
    // 创建风力向量
    const windForce = new THREE.Vector3();

    // 根据风场类型计算风力
    switch (this.globalWind.fieldType) {
      case WindFieldType.UNIFORM:
        // 均匀风场
        windForce.copy(this.globalWind.direction).normalize().multiplyScalar(this.globalWind.strength);
        break;
      case WindFieldType.TURBULENT:
        // 湍流风场
        windForce.copy(this.globalWind.direction).normalize().multiplyScalar(this.globalWind.strength);
        // 添加湍流
        const turbulence = this.calculateTurbulence(position, this.time, this.globalWind.frequency, this.globalWind.turbulence);
        windForce.add(turbulence);
        break;
      case WindFieldType.VORTEX:
        // 旋转风场
        const vortexForce = this.calculateVortexForce(position, this.globalWind.strength, this.time * this.globalWind.frequency);
        windForce.add(vortexForce);
        break;
      case WindFieldType.PULSE:
        // 脉冲风场
        const pulseStrength = Math.sin(this.time * this.globalWind.frequency) * this.globalWind.strength;
        windForce.copy(this.globalWind.direction).normalize().multiplyScalar(pulseStrength);
        break;
      case WindFieldType.CUSTOM:
        // 自定义风场
        // 这里可以添加自定义风场的计算逻辑
        break;
    }

    return windForce;
  }

  /**
   * 计算风区域风力
   * @param position 位置
   * @param zone 风区域
   * @param deltaTime 帧间隔时间（秒）
   * @returns 风力向量
   */
  private calculateWindZoneForce(position: THREE.Vector3, zone: WindZone, deltaTime: number): THREE.Vector3 {
    // 创建风力向量
    const windForce = new THREE.Vector3();

    // 计算位置相对于风区域的位置
    const relativePosition = position.clone().sub(zone.position);

    // 根据风区域类型计算风力
    let inZone = false;
    let falloffFactor = 1.0;

    switch (zone.type) {
      case WindZoneType.GLOBAL:
        // 全局风区域
        inZone = true;
        break;
      case WindZoneType.SPHERE:
        // 球形风区域
        const distance = relativePosition.length();
        if (distance <= zone.size.x) {
          inZone = true;
          // 计算衰减因子
          if (zone.falloffDistance > 0 && distance > zone.size.x - zone.falloffDistance) {
            falloffFactor = 1.0 - (distance - (zone.size.x - zone.falloffDistance)) / zone.falloffDistance;
          }
        }
        break;
      case WindZoneType.BOX:
        // 盒形风区域
        // 将相对位置转换到风区域的局部坐标系
        const localPosition = relativePosition.clone().applyEuler(zone.rotation);
        if (
          Math.abs(localPosition.x) <= zone.size.x / 2 &&
          Math.abs(localPosition.y) <= zone.size.y / 2 &&
          Math.abs(localPosition.z) <= zone.size.z / 2
        ) {
          inZone = true;
          // 计算衰减因子
          if (zone.falloffDistance > 0) {
            const dx = Math.max(0, Math.abs(localPosition.x) - (zone.size.x / 2 - zone.falloffDistance));
            const dy = Math.max(0, Math.abs(localPosition.y) - (zone.size.y / 2 - zone.falloffDistance));
            const dz = Math.max(0, Math.abs(localPosition.z) - (zone.size.z / 2 - zone.falloffDistance));
            falloffFactor = 1.0 - Math.max(dx, dy, dz) / zone.falloffDistance;
          }
        }
        break;
      case WindZoneType.CYLINDER:
        // 圆柱形风区域
        // 将相对位置转换到风区域的局部坐标系
        const cylinderLocalPosition = relativePosition.clone().applyEuler(zone.rotation);
        const horizontalDistance = Math.sqrt(cylinderLocalPosition.x * cylinderLocalPosition.x + cylinderLocalPosition.z * cylinderLocalPosition.z);
        if (
          horizontalDistance <= zone.size.x / 2 &&
          Math.abs(cylinderLocalPosition.y) <= zone.size.y / 2
        ) {
          inZone = true;
          // 计算衰减因子
          if (zone.falloffDistance > 0) {
            const dr = Math.max(0, horizontalDistance - (zone.size.x / 2 - zone.falloffDistance));
            const dy = Math.max(0, Math.abs(cylinderLocalPosition.y) - (zone.size.y / 2 - zone.falloffDistance));
            falloffFactor = 1.0 - Math.max(dr, dy) / zone.falloffDistance;
          }
        }
        break;
      case WindZoneType.CUSTOM:
        // 自定义风区域
        // 这里可以添加自定义风区域的计算逻辑
        break;
    }

    // 如果不在区域内，返回零向量
    if (!inZone) {
      return windForce;
    }

    // 根据风场类型计算风力
    switch (zone.fieldType) {
      case WindFieldType.UNIFORM:
        // 均匀风场
        windForce.copy(zone.direction).normalize().multiplyScalar(zone.strength * falloffFactor);
        break;
      case WindFieldType.TURBULENT:
        // 湍流风场
        windForce.copy(zone.direction).normalize().multiplyScalar(zone.strength * falloffFactor);
        // 添加湍流
        const turbulence = this.calculateTurbulence(position, this.time, zone.frequency, zone.turbulence);
        windForce.add(turbulence.multiplyScalar(falloffFactor));
        break;
      case WindFieldType.VORTEX:
        // 旋转风场
        const vortexForce = this.calculateVortexForce(relativePosition, zone.strength, this.time * zone.frequency);
        windForce.add(vortexForce.multiplyScalar(falloffFactor));
        break;
      case WindFieldType.PULSE:
        // 脉冲风场
        const pulseStrength = Math.sin(this.time * zone.frequency) * zone.strength * falloffFactor;
        windForce.copy(zone.direction).normalize().multiplyScalar(pulseStrength);
        break;
      case WindFieldType.CUSTOM:
        // 自定义风场
        // 这里可以添加自定义风场的计算逻辑
        break;
    }

    return windForce;
  }

  /**
   * 计算湍流
   * @param position 位置
   * @param time 时间
   * @param frequency 频率
   * @param strength 强度
   * @returns 湍流向量
   */
  private calculateTurbulence(position: THREE.Vector3, time: number, frequency: number, strength: number): THREE.Vector3 {
    // 使用柏林噪声或简单的正弦函数模拟湍流
    const x = Math.sin(position.x * 0.1 + time * frequency) * strength;
    const y = Math.sin(position.y * 0.1 + time * frequency * 1.1) * strength;
    const z = Math.sin(position.z * 0.1 + time * frequency * 1.2) * strength;

    return new THREE.Vector3(x, y, z);
  }

  /**
   * 计算旋转风场力
   * @param position 相对位置
   * @param strength 强度
   * @param angle 角度
   * @returns 旋转力向量
   */
  private calculateVortexForce(position: THREE.Vector3, strength: number, angle: number): THREE.Vector3 {
    // 计算水平距离
    const horizontalDistance = Math.sqrt(position.x * position.x + position.z * position.z);
    if (horizontalDistance < 0.001) {
      return new THREE.Vector3(0, 0, 0);
    }

    // 计算旋转方向
    const direction = new THREE.Vector3(-position.z, 0, position.x).normalize();

    // 计算旋转力
    const force = direction.multiplyScalar(strength);

    // 添加上下波动
    force.y = Math.sin(angle) * strength * 0.2;

    return force;
  }

  /**
   * 应用风力
   * @param entity 实体
   * @param component 植被组件
   * @param instanceId 实例ID
   * @param instance 实例
   * @param windForce 风力
   * @param deltaTime 帧间隔时间（秒）
   */
  private applyWindForce(
    entity: Entity,
    component: VegetationComponent,
    instanceId: string,
    instance: any,
    windForce: THREE.Vector3,
    deltaTime: number
  ): void {
    // 获取植被项
    const item = component.items[instance.itemIndex];

    // 计算风力偏移
    const windStrength = windForce.length();
    const windDirection = windForce.clone().normalize();

    // 计算风力作用方向
    const forceDirection = new THREE.Vector3();
    forceDirection.x = windDirection.x;
    forceDirection.z = windDirection.z;

    // 添加垂直分量
    forceDirection.y = windDirection.y * 0.2; // 减小垂直分量

    // 计算风力偏移
    const maxRotation = 0.2; // 最大旋转角度（弧度）
    const rotationX = forceDirection.z * windStrength * maxRotation;
    const rotationZ = -forceDirection.x * windStrength * maxRotation;

    // 如果使用实例化渲染，更新实例化渲染系统
    if (this.useInstancing && this.instancedRenderingSystem) {
      // 获取实例数据
      const instanceData = {
        rotation: new THREE.Euler(
          instance.rotation.x + rotationX,
          instance.rotation.y,
          instance.rotation.z + rotationZ
        )
      };

      // 更新实例
      this.instancedRenderingSystem.updateInstance(instanceId, instanceData);
    } else if (instance.userData.model) {
      // 否则，更新模型的旋转
      const model = instance.userData.model as THREE.Object3D;
      model.rotation.x = instance.rotation.x + rotationX;
      model.rotation.z = instance.rotation.z + rotationZ;
    }
  }

  /**
   * 初始化调试可视化
   */
  private initDebugVisualization(): void {
    // 清除现有调试网格
    this.clearDebugMeshes();

    // 创建全局风力调试网格
    this.createGlobalWindDebugMesh();

    // 创建风区域调试网格
    for (const [id, zone] of this.windZones.entries()) {
      this.createWindZoneDebugMesh(id, zone);
    }
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 更新全局风力调试网格
    this.updateGlobalWindDebugMesh();

    // 更新风区域调试网格
    for (const [id, zone] of this.windZones.entries()) {
      this.updateWindZoneDebugMesh(id, zone);
    }
  }

  /**
   * 清除调试网格
   */
  private clearDebugMeshes(): void {
    // 获取活跃场景
    const scene = this.world.getActiveScene();
    if (!scene) {
      return;
    }

    // 移除所有调试网格
    for (const mesh of this.debugMeshes) {
      scene.getThreeScene().remove(mesh);
    }

    // 清空调试网格列表
    this.debugMeshes = [];
  }

  /**
   * 创建全局风力调试网格
   */
  private createGlobalWindDebugMesh(): void {
    // 获取活跃场景
    const scene = this.world.getActiveScene();
    if (!scene) {
      return;
    }

    // 创建箭头辅助对象
    const arrowHelper = new THREE.ArrowHelper(
      this.globalWind.direction.clone().normalize(),
      new THREE.Vector3(0, 50, 0),
      this.globalWind.strength * 10,
      0x00ff00
    );
    arrowHelper.name = 'global_wind_debug';

    // 添加到场景
    scene.getThreeScene().add(arrowHelper);

    // 添加到调试网格列表
    this.debugMeshes.push(arrowHelper);
  }

  /**
   * 更新全局风力调试网格
   */
  private updateGlobalWindDebugMesh(): void {
    // 获取活跃场景
    const scene = this.world.getActiveScene();
    if (!scene) {
      return;
    }

    // 查找全局风力调试网格
    const arrowHelper = scene.getThreeScene().getObjectByName('global_wind_debug') as THREE.ArrowHelper;
    if (arrowHelper) {
      // 更新方向和长度
      arrowHelper.setDirection(this.globalWind.direction.clone().normalize());
      arrowHelper.setLength(this.globalWind.strength * 10);
    }
  }

  /**
   * 创建风区域调试网格
   * @param id 风区域ID
   * @param zone 风区域
   */
  private createWindZoneDebugMesh(id: string, zone: WindZone): void {
    // 获取活跃场景
    const scene = this.world.getActiveScene();
    if (!scene) {
      return;
    }

    // 创建风区域网格
    let zoneMesh: THREE.Object3D;
    const material = new THREE.MeshBasicMaterial({
      color: 0x0088ff,
      transparent: true,
      opacity: 0.2,
      wireframe: true
    });

    switch (zone.type) {
      case WindZoneType.SPHERE:
        // 球形风区域
        const sphereGeometry = new THREE.SphereGeometry(zone.size.x, 16, 16);
        zoneMesh = new THREE.Mesh(sphereGeometry, material);
        break;
      case WindZoneType.BOX:
        // 盒形风区域
        const boxGeometry = new THREE.BoxGeometry(zone.size.x, zone.size.y, zone.size.z);
        zoneMesh = new THREE.Mesh(boxGeometry, material);
        break;
      case WindZoneType.CYLINDER:
        // 圆柱形风区域
        const cylinderGeometry = new THREE.CylinderGeometry(zone.size.x / 2, zone.size.x / 2, zone.size.y, 16);
        zoneMesh = new THREE.Mesh(cylinderGeometry, material);
        break;
      case WindZoneType.GLOBAL:
      default:
        // 全局风区域或其他类型
        zoneMesh = new THREE.Object3D();
        break;
    }

    // 设置位置和旋转
    zoneMesh.position.copy(zone.position);
    zoneMesh.rotation.copy(zone.rotation);
    zoneMesh.name = `wind_zone_${id}`;

    // 创建箭头辅助对象
    const arrowHelper = new THREE.ArrowHelper(
      zone.direction.clone().normalize(),
      new THREE.Vector3(0, 0, 0),
      zone.strength * 5,
      0x00ffff
    );
    arrowHelper.name = `wind_zone_arrow_${id}`;

    // 添加箭头到风区域网格
    zoneMesh.add(arrowHelper);

    // 添加到场景
    scene.getThreeScene().add(zoneMesh);

    // 添加到调试网格列表
    this.debugMeshes.push(zoneMesh);
  }

  /**
   * 更新风区域调试网格
   * @param id 风区域ID
   * @param zone 风区域
   */
  private updateWindZoneDebugMesh(id: string, zone: WindZone): void {
    // 获取活跃场景
    const scene = this.world.getActiveScene();
    if (!scene) {
      return;
    }

    // 查找风区域网格
    const zoneMesh = scene.getThreeScene().getObjectByName(`wind_zone_${id}`);
    if (!zoneMesh) {
      return;
    }

    // 更新位置和旋转
    zoneMesh.position.copy(zone.position);
    zoneMesh.rotation.copy(zone.rotation);

    // 更新可见性
    zoneMesh.visible = zone.enabled;

    // 查找箭头辅助对象
    const arrowHelper = zoneMesh.getObjectByName(`wind_zone_arrow_${id}`) as THREE.ArrowHelper;
    if (arrowHelper) {
      // 更新方向和长度
      arrowHelper.setDirection(zone.direction.clone().normalize());
      arrowHelper.setLength(zone.strength * 5);
    }
  }

  /**
   * 移除风区域调试网格
   * @param id 风区域ID
   */
  private removeWindZoneDebugMesh(id: string): void {
    // 获取活跃场景
    const scene = this.world.getActiveScene();
    if (!scene) {
      return;
    }

    // 查找风区域网格
    const zoneMesh = scene.getThreeScene().getObjectByName(`wind_zone_${id}`);
    if (!zoneMesh) {
      return;
    }

    // 从场景中移除
    scene.getThreeScene().remove(zoneMesh);

    // 从调试网格列表中移除
    const index = this.debugMeshes.indexOf(zoneMesh);
    if (index !== -1) {
      this.debugMeshes.splice(index, 1);
    }
  }
}
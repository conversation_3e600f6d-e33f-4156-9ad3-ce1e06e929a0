"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpringConstraint = void 0;
/**
 * 弹簧约束
 * 在两个物体之间创建一个弹簧
 */
var THREE = require("three");
var CANNON = require("cannon-es");
var PhysicsConstraint_1 = require("./PhysicsConstraint");
/**
 * 弹簧约束
 */
var SpringConstraint = exports.SpringConstraint = /** @class */ (function (_super) {
    __extends(SpringConstraint, _super);
    /**
     * 创建弹簧约束
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    function SpringConstraint(targetEntity, options) {
        if (targetEntity === void 0) { targetEntity = null; }
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, PhysicsConstraint_1.ConstraintType.SPRING, targetEntity, options) || this;
        /** 世界坐标系中的方向 */
        _this.worldAxis = null;
        // 设置源物体上的连接点
        var localAnchorA = options.localAnchorA || new THREE.Vector3(0, 0, 0);
        _this.localAnchorA = new CANNON.Vec3(localAnchorA.x, localAnchorA.y, localAnchorA.z);
        // 设置目标物体上的连接点
        var localAnchorB = options.localAnchorB || new THREE.Vector3(0, 0, 0);
        _this.localAnchorB = new CANNON.Vec3(localAnchorB.x, localAnchorB.y, localAnchorB.z);
        // 设置弹簧参数
        _this.restLength = options.restLength !== undefined ? options.restLength : 1;
        _this.stiffness = options.stiffness !== undefined ? options.stiffness : 100;
        _this.damping = options.damping !== undefined ? options.damping : 1;
        _this.maxForce = 1e6; // 默认最大力
        // 设置世界坐标系中的方向
        if (options.worldAxis) {
            _this.worldAxis = new CANNON.Vec3(options.worldAxis.x, options.worldAxis.y, options.worldAxis.z);
            _this.worldAxis.normalize();
        }
        return _this;
    }
    /**
     * 创建约束
     */
    SpringConstraint.prototype.createConstraint = function () {
        // 获取源物体和目标物体
        var bodyA = this.getSourceBody();
        var bodyB = this.getTargetBody();
        // 如果没有源物体或目标物体，则无法创建约束
        if (!bodyA || !bodyB) {
            console.warn('无法创建弹簧约束：缺少源物体或目标物体');
            return;
        }
        // CANNON.js没有内置的Spring约束，使用DistanceConstraint作为替代
        this.constraint = new CANNON.DistanceConstraint(bodyA, bodyB, this.restLength, this.maxForce);
        this.constraint.collideConnected = this.collideConnected;
    };
    /**
     * 设置源物体上的连接点
     * @param anchor 连接点（局部坐标）
     */
    SpringConstraint.prototype.setLocalAnchorA = function (anchor) {
        this.localAnchorA.set(anchor.x, anchor.y, anchor.z);
        // 注意：由于使用DistanceConstraint替代，锚点功能受限
    };
    /**
     * 获取源物体上的连接点
     * @returns 连接点（局部坐标）
     */
    SpringConstraint.prototype.getLocalAnchorA = function () {
        return new THREE.Vector3(this.localAnchorA.x, this.localAnchorA.y, this.localAnchorA.z);
    };
    /**
     * 设置目标物体上的连接点
     * @param anchor 连接点（局部坐标）
     */
    SpringConstraint.prototype.setLocalAnchorB = function (anchor) {
        this.localAnchorB.set(anchor.x, anchor.y, anchor.z);
        // 注意：由于使用DistanceConstraint替代，锚点功能受限
    };
    /**
     * 获取目标物体上的连接点
     * @returns 连接点（局部坐标）
     */
    SpringConstraint.prototype.getLocalAnchorB = function () {
        return new THREE.Vector3(this.localAnchorB.x, this.localAnchorB.y, this.localAnchorB.z);
    };
    /**
     * 设置弹簧静止长度
     * @param length 静止长度
     */
    SpringConstraint.prototype.setRestLength = function (length) {
        this.restLength = length;
        // 注意：由于使用DistanceConstraint替代，需要重新创建约束来更新长度
        if (this.initialized && this.constraint && this.world) {
            this.world.removeConstraint(this.constraint);
            this.constraint = null;
            this.initialized = false;
            this.initialize(this.world);
        }
    };
    /**
     * 获取弹簧静止长度
     * @returns 静止长度
     */
    SpringConstraint.prototype.getRestLength = function () {
        return this.restLength;
    };
    /**
     * 设置弹簧刚度
     * @param stiffness 刚度
     */
    SpringConstraint.prototype.setStiffness = function (stiffness) {
        this.stiffness = stiffness;
        // 注意：由于使用DistanceConstraint替代，刚度功能受限
    };
    /**
     * 获取弹簧刚度
     * @returns 刚度
     */
    SpringConstraint.prototype.getStiffness = function () {
        return this.stiffness;
    };
    /**
     * 设置阻尼系数
     * @param damping 阻尼系数
     */
    SpringConstraint.prototype.setDamping = function (damping) {
        this.damping = damping;
        // 注意：由于使用DistanceConstraint替代，阻尼功能受限
    };
    /**
     * 获取阻尼系数
     * @returns 阻尼系数
     */
    SpringConstraint.prototype.getDamping = function () {
        return this.damping;
    };
    /**
     * 设置世界坐标系中的方向
     * @param axis 方向
     */
    SpringConstraint.prototype.setWorldAxis = function (axis) {
        if (axis) {
            if (!this.worldAxis) {
                this.worldAxis = new CANNON.Vec3();
            }
            this.worldAxis.set(axis.x, axis.y, axis.z);
            this.worldAxis.normalize();
        }
        else {
            this.worldAxis = null;
        }
        // 注意：由于使用DistanceConstraint替代，世界轴功能受限
    };
    /**
     * 获取世界坐标系中的方向
     * @returns 方向
     */
    SpringConstraint.prototype.getWorldAxis = function () {
        if (!this.worldAxis)
            return null;
        return new THREE.Vector3(this.worldAxis.x, this.worldAxis.y, this.worldAxis.z);
    };
    /**
     * 应用约束
     * 弹簧约束需要在每一帧手动应用
     */
    SpringConstraint.prototype.applyConstraint = function () {
        // 注意：由于使用DistanceConstraint替代，不需要手动应用力
    };
    /**
     * 更新约束
     * 在物理系统的每一帧调用
     */
    SpringConstraint.prototype.update = function () {
        // 注意：由于使用DistanceConstraint替代，不需要手动更新
    };
    /** 组件类型 */
    SpringConstraint.type = 'SpringConstraint';
    return SpringConstraint;
}(PhysicsConstraint_1.PhysicsConstraint));

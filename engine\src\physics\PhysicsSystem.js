"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhysicsSystem = void 0;
/**
 * 物理系统
 * 基于cannon.js实现物理模拟
 */
var THREE = require("three");
var CANNON = require("cannon-es");
var System_1 = require("../core/System");
var PhysicsCollider_1 = require("./PhysicsCollider");
var Debug_1 = require("../utils/Debug");
var CollisionDetector_1 = require("./collision/CollisionDetector");
var CollisionEvent_1 = require("./collision/CollisionEvent");
var PhysicsConstraint_1 = require("./constraints/PhysicsConstraint");
var SpringConstraint_1 = require("./constraints/SpringConstraint");
var CharacterControllerComponent_1 = require("./components/CharacterControllerComponent");
var PhysicsRaycastResult_1 = require("./PhysicsRaycastResult");
var ContinuousCollisionDetection_1 = require("./ccd/ContinuousCollisionDetection");
var PhysicsDebugger_1 = require("./debug/PhysicsDebugger");
var EnhancedPhysicsDebugger_1 = require("./debug/EnhancedPhysicsDebugger");
var PhysicsSystem = exports.PhysicsSystem = /** @class */ (function (_super) {
    __extends(PhysicsSystem, _super);
    /**
     * 创建物理系统
     * @param options 物理系统选项
     */
    function PhysicsSystem(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, 1) || this;
        /** 物理更新累积时间 */
        _this.accumulator = 0;
        /** 物理体映射 */
        _this.bodies = new Map();
        /** 碰撞器映射 */
        _this.colliders = new Map();
        /** 约束映射 */
        _this.constraints = new Map();
        /** 角色控制器映射 */
        _this.characterControllers = new Map();
        /** 连续碰撞检测 */
        _this.ccd = null;
        /** 调试渲染器 */
        _this.debugRenderer = null;
        /** 物理调试器 */
        _this.physicsDebugger = null;
        /** 调试网格映射 */
        _this.debugMeshes = new Map();
        // 创建物理世界
        _this.physicsWorld = new CANNON.World();
        // 设置重力
        var gravity = options.gravity || { x: 0, y: -9.82, z: 0 };
        _this.physicsWorld.gravity.set(gravity.x, gravity.y, gravity.z);
        // 设置更新频率
        _this.updateFrequency = options.updateFrequency || 60;
        _this.fixedTimeStep = 1 / _this.updateFrequency;
        // 设置休眠
        _this.physicsWorld.allowSleep = options.allowSleep !== undefined ? options.allowSleep : true;
        // 设置迭代次数（使用类型断言）
        _this.physicsWorld.solver.iterations = options.iterations || 10;
        // 设置连续碰撞检测
        if (options.enableCCD) {
            // 创建连续碰撞检测
            _this.ccd = new ContinuousCollisionDetection_1.ContinuousCollisionDetection(_this.physicsWorld, options.ccdOptions);
        }
        // 设置调试
        _this.debug = options.debug || false;
        _this.useEnhancedDebugger = options.useEnhancedDebugger || false;
        if (_this.debug) {
            _this.debugRenderer = new THREE.Scene();
            // 创建物理调试器
            if (_this.useEnhancedDebugger) {
                _this.physicsDebugger = new EnhancedPhysicsDebugger_1.EnhancedPhysicsDebugger(_this, options.debuggerOptions);
            }
            else {
                _this.physicsDebugger = new PhysicsDebugger_1.PhysicsDebugger(_this, options.debuggerOptions);
            }
            // 初始化物理调试器
            _this.physicsDebugger.initialize();
        }
        // 创建碰撞检测器
        _this.collisionDetector = new CollisionDetector_1.CollisionDetector(_this.physicsWorld);
        // 监听碰撞事件
        _this.collisionDetector.on(CollisionEvent_1.CollisionEventType.BEGIN, _this.handleCollisionEvent.bind(_this));
        _this.collisionDetector.on(CollisionEvent_1.CollisionEventType.STAY, _this.handleCollisionEvent.bind(_this));
        _this.collisionDetector.on(CollisionEvent_1.CollisionEventType.END, _this.handleCollisionEvent.bind(_this));
        _this.collisionDetector.on(CollisionEvent_1.CollisionEventType.TRIGGER_ENTER, _this.handleCollisionEvent.bind(_this));
        _this.collisionDetector.on(CollisionEvent_1.CollisionEventType.TRIGGER_STAY, _this.handleCollisionEvent.bind(_this));
        _this.collisionDetector.on(CollisionEvent_1.CollisionEventType.TRIGGER_EXIT, _this.handleCollisionEvent.bind(_this));
        return _this;
    }
    /**
     * 初始化系统
     */
    PhysicsSystem.prototype.initialize = function () {
        if (this.engine) {
            // 获取世界中的所有实体
            var world = this.engine.getWorld();
            if (world) {
                // 查找具有物理组件的实体
                var entities = world.getAllEntities();
                for (var _i = 0, entities_1 = entities; _i < entities_1.length; _i++) {
                    var entity = entities_1[_i];
                    this.setupEntityPhysics(entity);
                }
                // 监听实体创建事件
                world.on('entityCreated', this.handleEntityCreated.bind(this));
                // 监听实体移除事件
                world.on('entityRemoved', this.handleEntityRemoved.bind(this));
            }
        }
    };
    /**
     * 处理实体创建事件
     * @param entity 创建的实体
     */
    PhysicsSystem.prototype.handleEntityCreated = function (entity) {
        this.setupEntityPhysics(entity);
    };
    /**
     * 处理实体移除事件
     * @param entity 移除的实体
     */
    PhysicsSystem.prototype.handleEntityRemoved = function (entity) {
        this.removeEntityPhysics(entity);
    };
    /**
     * 设置实体的物理属性
     * @param entity 实体
     */
    PhysicsSystem.prototype.setupEntityPhysics = function (entity) {
        // 检查实体是否有物理体组件
        var physicsBody = entity.getComponent(PhysicsBody.type);
        if (physicsBody) {
            // 初始化物理体
            physicsBody.initialize(this.physicsWorld);
            // 添加到映射
            this.bodies.set(entity.id, physicsBody);
            // 注册到碰撞检测器
            this.collisionDetector.registerBody(entity, physicsBody);
        }
        // 检查实体是否有碰撞器组件
        var physicsCollider = entity.getComponent(PhysicsCollider_1.PhysicsCollider.type);
        if (physicsCollider) {
            // 初始化碰撞器
            physicsCollider.initialize(this.physicsWorld);
            // 添加到映射
            this.colliders.set(entity.id, physicsCollider);
            // 注册到碰撞检测器
            this.collisionDetector.registerCollider(entity, physicsCollider);
        }
        // 检查实体是否有约束组件
        var constraint = entity.getComponent(PhysicsConstraint_1.PhysicsConstraint.type);
        if (constraint) {
            // 初始化约束
            constraint.initialize(this.physicsWorld);
            // 添加到映射
            this.constraints.set("".concat(entity.id, "_").concat(constraint.constructor.name), constraint);
        }
        // 检查实体是否有角色控制器组件
        var characterController = entity.getComponent(CharacterControllerComponent_1.CharacterControllerComponent.type);
        if (characterController) {
            // 初始化角色控制器
            characterController.initialize(this.physicsWorld);
            // 添加到映射
            this.characterControllers.set(entity.id, characterController);
        }
    };
    /**
     * 移除实体的物理属性
     * @param entity 实体
     */
    PhysicsSystem.prototype.removeEntityPhysics = function (entity) {
        // 移除物理体
        var physicsBody = this.bodies.get(entity.id);
        if (physicsBody) {
            // 从碰撞检测器中注销
            this.collisionDetector.unregisterBody(entity);
            // 销毁物理体
            physicsBody.dispose();
            this.bodies.delete(entity.id);
            // 移除调试网格
            if (this.debug && this.debugRenderer) {
                var body = physicsBody.getCannonBody();
                if (body) {
                    var debugMesh = this.debugMeshes.get(body);
                    if (debugMesh) {
                        this.debugRenderer.remove(debugMesh);
                        this.debugMeshes.delete(body);
                    }
                }
            }
        }
        // 移除碰撞器
        var physicsCollider = this.colliders.get(entity.id);
        if (physicsCollider) {
            // 从碰撞检测器中注销
            this.collisionDetector.unregisterCollider(entity);
            // 销毁碰撞器
            physicsCollider.dispose();
            this.colliders.delete(entity.id);
        }
        // 移除约束
        for (var _i = 0, _a = this.constraints.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], key = _b[0], constraint = _b[1];
            if (key.startsWith("".concat(entity.id, "_"))) {
                constraint.dispose();
                this.constraints.delete(key);
            }
        }
        // 移除角色控制器
        var characterController = this.characterControllers.get(entity.id);
        if (characterController) {
            characterController.dispose();
            this.characterControllers.delete(entity.id);
        }
    };
    /**
     * 处理碰撞事件
     * @param event 碰撞事件
     */
    PhysicsSystem.prototype.handleCollisionEvent = function (event) {
        // 发出碰撞事件
        this.emit(event.type, event);
        // 获取实体
        var entityA = event.entityA;
        var entityB = event.entityB;
        // 通知实体组件
        var physicsBodyA = this.bodies.get(entityA.id);
        var physicsBodyB = this.bodies.get(entityB.id);
        if (physicsBodyA) {
            switch (event.type) {
                case CollisionEvent_1.CollisionEventType.BEGIN:
                    physicsBodyA.onCollisionStart(entityB, event);
                    break;
                case CollisionEvent_1.CollisionEventType.STAY:
                    physicsBodyA.onCollisionStay(entityB, event);
                    break;
                case CollisionEvent_1.CollisionEventType.END:
                    physicsBodyA.onCollisionEnd(entityB, event);
                    break;
                case CollisionEvent_1.CollisionEventType.TRIGGER_ENTER:
                    physicsBodyA.onTriggerEnter(entityB, event);
                    break;
                case CollisionEvent_1.CollisionEventType.TRIGGER_STAY:
                    physicsBodyA.onTriggerStay(entityB, event);
                    break;
                case CollisionEvent_1.CollisionEventType.TRIGGER_EXIT:
                    physicsBodyA.onTriggerExit(entityB, event);
                    break;
            }
        }
        if (physicsBodyB) {
            switch (event.type) {
                case CollisionEvent_1.CollisionEventType.BEGIN:
                    physicsBodyB.onCollisionStart(entityA, event);
                    break;
                case CollisionEvent_1.CollisionEventType.STAY:
                    physicsBodyB.onCollisionStay(entityA, event);
                    break;
                case CollisionEvent_1.CollisionEventType.END:
                    physicsBodyB.onCollisionEnd(entityA, event);
                    break;
                case CollisionEvent_1.CollisionEventType.TRIGGER_ENTER:
                    physicsBodyB.onTriggerEnter(entityA, event);
                    break;
                case CollisionEvent_1.CollisionEventType.TRIGGER_STAY:
                    physicsBodyB.onTriggerStay(entityA, event);
                    break;
                case CollisionEvent_1.CollisionEventType.TRIGGER_EXIT:
                    physicsBodyB.onTriggerExit(entityA, event);
                    break;
            }
        }
        // 通知实体
        if (typeof entityA.onCollision === 'function') {
            entityA['onCollision'](event);
        }
        if (typeof entityB.onCollision === 'function') {
            entityB['onCollision'](event);
        }
    };
    /**
     * 固定时间步长更新
     * @param _fixedDeltaTime 固定帧间隔时间（秒）- 未使用，使用 this.fixedTimeStep 代替
     */
    PhysicsSystem.prototype.fixedUpdate = function (_fixedDeltaTime) {
        // 更新弹簧约束
        for (var _i = 0, _a = this.constraints.values(); _i < _a.length; _i++) {
            var constraint = _a[_i];
            if (constraint instanceof SpringConstraint_1.SpringConstraint) {
                constraint.update();
            }
        }
        // 更新物理世界
        if (this.ccd) {
            // 使用连续碰撞检测更新
            this.ccd.update(this.fixedTimeStep);
        }
        else {
            // 正常更新
            this.physicsWorld.step(this.fixedTimeStep);
        }
        // 更新碰撞检测器
        this.collisionDetector.update(this.fixedTimeStep);
        // 更新实体位置和旋转
        for (var _b = 0, _c = this.bodies.entries(); _b < _c.length; _b++) {
            var _d = _c[_b], _entityId = _d[0], physicsBody = _d[1];
            physicsBody.updateTransform();
        }
        // 更新调试渲染
        if (this.debug && this.debugRenderer) {
            if (this.physicsDebugger) {
                this.physicsDebugger.update();
            }
            else {
                this.updateDebugRenderer();
            }
        }
    };
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    PhysicsSystem.prototype.update = function (deltaTime) {
        // 累积时间
        this.accumulator += deltaTime;
        // 固定时间步长更新
        while (this.accumulator >= this.fixedTimeStep) {
            this.fixedUpdate(this.fixedTimeStep);
            this.accumulator -= this.fixedTimeStep;
        }
    };
    /**
     * 更新调试渲染器
     */
    PhysicsSystem.prototype.updateDebugRenderer = function () {
        if (!this.debugRenderer)
            return;
        // 更新现有调试网格
        for (var _i = 0, _a = this.physicsWorld.bodies; _i < _a.length; _i++) {
            var body = _a[_i];
            var mesh = this.debugMeshes.get(body);
            // 如果没有调试网格，创建一个
            if (!mesh) {
                mesh = this.createDebugMesh(body);
                if (mesh) {
                    this.debugRenderer.add(mesh);
                    this.debugMeshes.set(body, mesh);
                }
            }
            // 更新位置和旋转
            if (mesh) {
                mesh.position.copy(new THREE.Vector3(body.getPosition().x, body.getPosition().y, body.getPosition().z));
                mesh.quaternion.copy(new THREE.Quaternion(body.quaternion.x, body.quaternion.y, body.quaternion.z, body.quaternion.w));
            }
        }
    };
    /**
     * 创建调试网格
     * @param body 物理体
     * @returns 调试网格
     */
    PhysicsSystem.prototype.createDebugMesh = function (body) {
        var group = new THREE.Group();
        // 为每个形状创建网格
        for (var i = 0; i < body.shapes.length; i++) {
            var shape = body.shapes[i];
            var material = new THREE.MeshBasicMaterial({
                color: 0x00ff00,
                wireframe: true,
                transparent: true,
                opacity: 0.5,
            });
            var geometry = void 0;
            var mesh = void 0;
            // 根据形状类型创建几何体
            switch (shape.type) {
                case CANNON.Shape.types.BOX:
                    var boxShape = shape;
                    geometry = new THREE.BoxGeometry(boxShape.halfExtents.x * 2, boxShape.halfExtents.y * 2, boxShape.halfExtents.z * 2);
                    mesh = new THREE.Mesh(geometry, material);
                    break;
                case CANNON.Shape.types.SPHERE:
                    var sphereShape = shape;
                    geometry = new THREE.SphereGeometry(sphereShape.radius, 16, 16);
                    mesh = new THREE.Mesh(geometry, material);
                    break;
                case CANNON.Shape.types.CYLINDER:
                    var cylinderShape = shape; // CANNON.Cylinder
                    geometry = new THREE.CylinderGeometry(cylinderShape.radiusTop, cylinderShape.radiusBottom, cylinderShape.height, 16);
                    mesh = new THREE.Mesh(geometry, material);
                    // 旋转以匹配CANNON.js的方向
                    mesh.rotation.x = Math.PI / 2;
                    break;
                case CANNON.Shape.types.PLANE:
                    geometry = new THREE.PlaneGeometry(10, 10);
                    mesh = new THREE.Mesh(geometry, material);
                    break;
                default:
                    // 对于不支持的形状，创建一个小球作为占位符
                    geometry = new THREE.SphereGeometry(0.1, 8, 8);
                    mesh = new THREE.Mesh(geometry, material);
                    Debug_1.Debug.warn("\u4E0D\u652F\u6301\u7684\u7269\u7406\u5F62\u72B6\u7C7B\u578B: ".concat(shape.type));
                    break;
            }
            // 应用形状的位置和旋转
            if (body.shapeOffsets[i]) {
                mesh.position.copy(new THREE.Vector3(body.shapeOffsets[i].x, body.shapeOffsets[i].y, body.shapeOffsets[i].z));
            }
            if (body.shapeOrientations[i]) {
                mesh.quaternion.copy(new THREE.Quaternion(body.shapeOrientations[i].x, body.shapeOrientations[i].y, body.shapeOrientations[i].z, body.shapeOrientations[i].w));
            }
            group.add(mesh);
        }
        return group;
    };
    /**
     * 获取调试渲染器场景
     * @returns 调试渲染器场景
     */
    PhysicsSystem.prototype.getDebugRenderer = function () {
        return this.debugRenderer;
    };
    /**
     * 设置重力
     * @param x X轴重力
     * @param y Y轴重力
     * @param z Z轴重力
     */
    PhysicsSystem.prototype.setGravity = function (x, y, z) {
        this.physicsWorld.gravity.set(x, y, z);
    };
    /**
     * 获取重力
     * @returns 重力向量
     */
    PhysicsSystem.prototype.getGravity = function () {
        return this.physicsWorld.gravity;
    };
    /**
     * 射线检测（最近的一个）
     * @param from 起点
     * @param to 终点
     * @param options 选项
     * @returns 射线检测结果
     */
    PhysicsSystem.prototype.raycastClosest = function (from, to, options) {
        var _a;
        if (options === void 0) { options = {}; }
        // 创建射线
        var rayFrom = new CANNON.Vec3(from.x, from.y, from.z);
        var rayTo = new CANNON.Vec3(to.x, to.y, to.z);
        // 设置选项
        var rayOptions = {
            skipBackfaces: options.skipBackfaces !== undefined ? options.skipBackfaces : true,
            collisionFilterMask: options.collisionFilterMask !== undefined ? options.collisionFilterMask : -1,
            collisionFilterGroup: options.collisionFilterGroup !== undefined ? options.collisionFilterGroup : -1,
        };
        // 创建射线检测结果
        var result = new CANNON.RaycastResult();
        // 执行射线检测
        this.physicsWorld.raycastClosest(rayFrom, rayTo, rayOptions, result);
        // 创建物理体到实体的映射
        var bodyToEntity = new Map();
        for (var _i = 0, _b = this.bodies.entries(); _i < _b.length; _i++) {
            var _c = _b[_i], entityId = _c[0], physicsBody = _c[1];
            var body = physicsBody.getCannonBody();
            if (body) {
                var entity = (_a = this.engine) === null || _a === void 0 ? void 0 : _a.getWorld().getEntity(entityId);
                if (entity) {
                    bodyToEntity.set(body, entity);
                }
            }
        }
        // 返回结果
        return new PhysicsRaycastResult_1.PhysicsRaycastResult(result, bodyToEntity);
    };
    /**
     * 射线检测（所有相交的）
     * @param from 起点
     * @param to 终点
     * @param options 选项
     * @returns 射线检测结果数组
     */
    PhysicsSystem.prototype.raycastAll = function (from, to, options) {
        var _a;
        if (options === void 0) { options = {}; }
        // 创建射线
        var rayFrom = new CANNON.Vec3(from.x, from.y, from.z);
        var rayTo = new CANNON.Vec3(to.x, to.y, to.z);
        // 设置选项
        var rayOptions = {
            skipBackfaces: options.skipBackfaces !== undefined ? options.skipBackfaces : true,
            collisionFilterMask: options.collisionFilterMask !== undefined ? options.collisionFilterMask : -1,
            collisionFilterGroup: options.collisionFilterGroup !== undefined ? options.collisionFilterGroup : -1,
        };
        // 创建物理体到实体的映射
        var bodyToEntity = new Map();
        for (var _i = 0, _b = this.bodies.entries(); _i < _b.length; _i++) {
            var _c = _b[_i], entityId = _c[0], physicsBody = _c[1];
            var body = physicsBody.getCannonBody();
            if (body) {
                var entity = (_a = this.engine) === null || _a === void 0 ? void 0 : _a.getWorld().getEntity(entityId);
                if (entity) {
                    bodyToEntity.set(body, entity);
                }
            }
        }
        // 执行射线检测
        var results = [];
        this.physicsWorld.raycastAll(rayFrom, rayTo, rayOptions, function (result) {
            results.push(new PhysicsRaycastResult_1.PhysicsRaycastResult(result, bodyToEntity));
        });
        // 返回结果
        return results;
    };
    /**
     * 射线检测（任意一个）
     * @param from 起点
     * @param to 终点
     * @param options 选项
     * @returns 射线检测结果
     */
    PhysicsSystem.prototype.raycast = function (from, to, options) {
        var _a;
        if (options === void 0) { options = {}; }
        // 创建射线
        var rayFrom = new CANNON.Vec3(from.x, from.y, from.z);
        var rayTo = new CANNON.Vec3(to.x, to.y, to.z);
        // 设置选项
        var rayOptions = {
            skipBackfaces: options.skipBackfaces !== undefined ? options.skipBackfaces : true,
            collisionFilterMask: options.collisionFilterMask !== undefined ? options.collisionFilterMask : -1,
            collisionFilterGroup: options.collisionFilterGroup !== undefined ? options.collisionFilterGroup : -1,
        };
        // 创建射线检测结果
        var result = new CANNON.RaycastResult();
        // 创建物理体到实体的映射
        var bodyToEntity = new Map();
        for (var _i = 0, _b = this.bodies.entries(); _i < _b.length; _i++) {
            var _c = _b[_i], entityId = _c[0], physicsBody = _c[1];
            var body = physicsBody.getCannonBody();
            if (body) {
                var entity = (_a = this.engine) === null || _a === void 0 ? void 0 : _a.getWorld().getEntity(entityId);
                if (entity) {
                    bodyToEntity.set(body, entity);
                }
            }
        }
        // 执行射线检测
        this.physicsWorld.raycastAny(rayFrom, rayTo, rayOptions, result);
        // 返回结果
        return new PhysicsRaycastResult_1.PhysicsRaycastResult(result, bodyToEntity);
    };
    /**
     * 获取物理世界
     * @returns 物理世界
     */
    PhysicsSystem.prototype.getPhysicsWorld = function () {
        return this.physicsWorld;
    };
    /**
     * 获取实体的物理体
     * @param entity 实体
     * @returns 物理体
     */
    PhysicsSystem.prototype.getPhysicsBody = function (entity) {
        return this.bodies.get(entity.id) || null;
    };
    /**
     * 设置调试模式
     * @param debug 是否启用调试
     * @param useEnhancedDebugger 是否使用增强型调试器
     * @param debuggerOptions 调试器选项
     */
    PhysicsSystem.prototype.setDebug = function (debug, useEnhancedDebugger, debuggerOptions) {
        if (useEnhancedDebugger === void 0) { useEnhancedDebugger = false; }
        if (debuggerOptions === void 0) { debuggerOptions = {}; }
        if (this.debug === debug && this.useEnhancedDebugger === useEnhancedDebugger) {
            return;
        }
        this.debug = debug;
        this.useEnhancedDebugger = useEnhancedDebugger;
        if (debug && !this.debugRenderer) {
            this.debugRenderer = new THREE.Scene();
            // 创建物理调试器
            if (useEnhancedDebugger) {
                this.physicsDebugger = new EnhancedPhysicsDebugger_1.EnhancedPhysicsDebugger(this, debuggerOptions);
            }
            else {
                this.physicsDebugger = new PhysicsDebugger_1.PhysicsDebugger(this, debuggerOptions);
            }
            // 初始化物理调试器
            this.physicsDebugger.initialize();
        }
        else if (!debug && this.debugRenderer) {
            // 清空调试渲染器
            if (this.physicsDebugger) {
                if (typeof this.physicsDebugger.dispose === 'function') {
                    this.physicsDebugger.dispose();
                }
                this.physicsDebugger = null;
            }
            else {
                while (this.debugRenderer.children.length > 0) {
                    var child = this.debugRenderer.children[0];
                    this.debugRenderer.remove(child);
                }
                this.debugMeshes.clear();
            }
            this.debugRenderer = null;
        }
    };
    /**
     * 是否启用调试
     * @returns 是否启用调试
     */
    PhysicsSystem.prototype.isDebug = function () {
        return this.debug;
    };
    /**
     * 是否使用增强型调试器
     * @returns 是否使用增强型调试器
     */
    PhysicsSystem.prototype.isUsingEnhancedDebugger = function () {
        return this.useEnhancedDebugger;
    };
    /**
     * 获取物理调试器
     * @returns 物理调试器
     */
    PhysicsSystem.prototype.getPhysicsDebugger = function () {
        return this.physicsDebugger;
    };
    /**
     * 计算角色控制器移动
     * @param entity 实体
     * @param desiredTranslation 期望的移动向量
     * @param filterGroups 碰撞组过滤
     * @param filterPredicate 碰撞过滤谓词函数
     */
    PhysicsSystem.prototype.computeCharacterControllerMovement = function (entity, desiredTranslation, filterGroups, filterPredicate) {
        var characterController = this.characterControllers.get(entity.id);
        if (!characterController) {
            console.warn("\u5B9E\u4F53 ".concat(entity.id, " \u6CA1\u6709\u89D2\u8272\u63A7\u5236\u5668\u7EC4\u4EF6"));
            return;
        }
        characterController.computeColliderMovement(desiredTranslation, filterGroups, filterPredicate);
    };
    /**
     * 获取角色控制器计算出的移动向量
     * @param entity 实体
     * @returns 计算出的移动向量
     */
    PhysicsSystem.prototype.getCharacterControllerComputedMovement = function (entity) {
        var characterController = this.characterControllers.get(entity.id);
        if (!characterController) {
            console.warn("\u5B9E\u4F53 ".concat(entity.id, " \u6CA1\u6709\u89D2\u8272\u63A7\u5236\u5668\u7EC4\u4EF6"));
            return new THREE.Vector3();
        }
        return characterController.getComputedMovement();
    };
    /**
     * 检查实体是否有角色控制器
     * @param entity 实体
     * @returns 是否有角色控制器
     */
    PhysicsSystem.prototype.hasCharacterController = function (entity) {
        return this.characterControllers.has(entity.id);
    };
    /**
     * 获取角色控制器
     * @param entity 实体
     * @returns 角色控制器
     */
    PhysicsSystem.prototype.getCharacterController = function (entity) {
        return this.characterControllers.get(entity.id) || null;
    };
    /**
     * 启用实体的连续碰撞检测
     * @param entity 实体
     */
    PhysicsSystem.prototype.enableEntityCCD = function (entity) {
        if (!this.ccd) {
            console.warn('连续碰撞检测未启用');
            return;
        }
        this.ccd.enableEntityCCD(entity);
    };
    /**
     * 禁用实体的连续碰撞检测
     * @param entity 实体
     */
    PhysicsSystem.prototype.disableEntityCCD = function (entity) {
        if (!this.ccd) {
            return;
        }
        this.ccd.disableEntityCCD(entity);
    };
    /**
     * 检查实体是否启用连续碰撞检测
     * @param entity 实体
     * @returns 是否启用连续碰撞检测
     */
    PhysicsSystem.prototype.isEntityCCDEnabled = function (entity) {
        if (!this.ccd) {
            return false;
        }
        return this.ccd.isEntityCCDEnabled(entity);
    };
    /**
     * 获取连续碰撞检测
     * @returns 连续碰撞检测
     */
    PhysicsSystem.prototype.getCCD = function () {
        return this.ccd;
    };
    /**
     * 销毁系统
     */
    PhysicsSystem.prototype.dispose = function () {
        // 销毁碰撞检测器
        this.collisionDetector.dispose();
        // 移除实体事件监听
        if (this.engine) {
            var world = this.engine.getWorld();
            if (world) {
                world.off('entityCreated', this.handleEntityCreated.bind(this));
                world.off('entityRemoved', this.handleEntityRemoved.bind(this));
            }
        }
        // 清空物理体和碰撞器
        for (var _i = 0, _a = this.bodies.values(); _i < _a.length; _i++) {
            var physicsBody = _a[_i];
            physicsBody.dispose();
        }
        this.bodies.clear();
        for (var _b = 0, _c = this.colliders.values(); _b < _c.length; _b++) {
            var physicsCollider = _c[_b];
            physicsCollider.dispose();
        }
        this.colliders.clear();
        // 清空约束
        for (var _d = 0, _e = this.constraints.values(); _d < _e.length; _d++) {
            var constraint = _e[_d];
            constraint.dispose();
        }
        this.constraints.clear();
        // 清空角色控制器
        for (var _f = 0, _g = this.characterControllers.values(); _f < _g.length; _f++) {
            var characterController = _g[_f];
            characterController.dispose();
        }
        this.characterControllers.clear();
        // 清空调试渲染器
        if (this.debugRenderer) {
            while (this.debugRenderer.children.length > 0) {
                var child = this.debugRenderer.children[0];
                this.debugRenderer.remove(child);
            }
            this.debugMeshes.clear();
            this.debugRenderer = null;
        }
        _super.prototype.dispose.call(this);
    };
    /** 系统类型 */
    PhysicsSystem.type = 'PhysicsSystem';
    return PhysicsSystem;
}(System_1.System));

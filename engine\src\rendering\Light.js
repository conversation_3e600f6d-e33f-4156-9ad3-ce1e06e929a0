"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Light = exports.LightType = void 0;
/**
 * 光源组件
 * 表示场景中的各种光源
 */
var THREE = require("three");
var Component_1 = require("../core/Component");
/**
 * 光源类型枚举
 */
var LightType;
(function (LightType) {
    LightType["AMBIENT"] = "ambient";
    LightType["DIRECTIONAL"] = "directional";
    LightType["POINT"] = "point";
    LightType["SPOT"] = "spot";
    LightType["HEMISPHERE"] = "hemisphere";
    LightType["RECT_AREA"] = "rectArea";
})(LightType || (exports.LightType = LightType = {}));
/**
 * 光源组件类
 */
var Light = exports.Light = /** @class */ (function (_super) {
    __extends(Light, _super);
    /**
     * 创建光源组件
     * @param options 光源选项
     */
    function Light(options) {
        var _this = _super.call(this, Light.type) || this;
        _this.lightType = options.type;
        // 创建对应类型的光源
        switch (_this.lightType) {
            case LightType.AMBIENT:
                _this.light = _this.createAmbientLight(options);
                break;
            case LightType.DIRECTIONAL:
                _this.light = _this.createDirectionalLight(options);
                break;
            case LightType.POINT:
                _this.light = _this.createPointLight(options);
                break;
            case LightType.SPOT:
                _this.light = _this.createSpotLight(options);
                break;
            case LightType.HEMISPHERE:
                _this.light = _this.createHemisphereLight(options);
                break;
            case LightType.RECT_AREA:
                _this.light = _this.createRectAreaLight(options);
                break;
            default:
                throw new Error("\u4E0D\u652F\u6301\u7684\u5149\u6E90\u7C7B\u578B: ".concat(options.type));
        }
        return _this;
    }
    /**
     * 创建环境光
     * @param options 环境光选项
     * @returns Three.js环境光
     */
    Light.prototype.createAmbientLight = function (options) {
        var color = options.color !== undefined ? options.color : 0xffffff;
        var intensity = options.intensity !== undefined ? options.intensity : 1;
        return new THREE.AmbientLight(color, intensity);
    };
    /**
     * 创建方向光
     * @param options 方向光选项
     * @returns Three.js方向光
     */
    Light.prototype.createDirectionalLight = function (options) {
        var color = options.color !== undefined ? options.color : 0xffffff;
        var intensity = options.intensity !== undefined ? options.intensity : 1;
        var light = new THREE.DirectionalLight(color, intensity);
        // 设置阴影
        light.castShadow = options.castShadow !== undefined ? options.castShadow : false;
        if (light.castShadow) {
            // 设置阴影贴图大小
            if (options.shadowMapSize !== undefined) {
                light.shadow.mapSize.width = options.shadowMapSize;
                light.shadow.mapSize.height = options.shadowMapSize;
            }
            // 设置阴影相机参数
            if (options.shadowCameraNear !== undefined)
                light.shadow.camera.near = options.shadowCameraNear;
            if (options.shadowCameraFar !== undefined)
                light.shadow.camera.far = options.shadowCameraFar;
            if (options.shadowCameraLeft !== undefined)
                light.shadow.camera.left = options.shadowCameraLeft;
            if (options.shadowCameraRight !== undefined)
                light.shadow.camera.right = options.shadowCameraRight;
            if (options.shadowCameraTop !== undefined)
                light.shadow.camera.top = options.shadowCameraTop;
            if (options.shadowCameraBottom !== undefined)
                light.shadow.camera.bottom = options.shadowCameraBottom;
            // 设置阴影偏移和半径
            if (options.shadowBias !== undefined)
                light.shadow.bias = options.shadowBias;
            if (options.shadowRadius !== undefined)
                light.shadow.radius = options.shadowRadius;
        }
        return light;
    };
    /**
     * 创建点光源
     * @param options 点光源选项
     * @returns Three.js点光源
     */
    Light.prototype.createPointLight = function (options) {
        var color = options.color !== undefined ? options.color : 0xffffff;
        var intensity = options.intensity !== undefined ? options.intensity : 1;
        var distance = options.distance !== undefined ? options.distance : 0;
        var decay = options.decay !== undefined ? options.decay : 2;
        var light = new THREE.PointLight(color, intensity, distance, decay);
        // 设置阴影
        light.castShadow = options.castShadow !== undefined ? options.castShadow : false;
        if (light.castShadow) {
            // 设置阴影贴图大小
            if (options.shadowMapSize !== undefined) {
                light.shadow.mapSize.width = options.shadowMapSize;
                light.shadow.mapSize.height = options.shadowMapSize;
            }
            // 设置阴影相机参数
            if (options.shadowCameraNear !== undefined)
                light.shadow.camera.near = options.shadowCameraNear;
            if (options.shadowCameraFar !== undefined)
                light.shadow.camera.far = options.shadowCameraFar;
            // 设置阴影偏移和半径
            if (options.shadowBias !== undefined)
                light.shadow.bias = options.shadowBias;
            if (options.shadowRadius !== undefined)
                light.shadow.radius = options.shadowRadius;
        }
        return light;
    };
    /**
     * 创建聚光灯
     * @param options 聚光灯选项
     * @returns Three.js聚光灯
     */
    Light.prototype.createSpotLight = function (options) {
        var color = options.color !== undefined ? options.color : 0xffffff;
        var intensity = options.intensity !== undefined ? options.intensity : 1;
        var distance = options.distance !== undefined ? options.distance : 0;
        var angle = options.angle !== undefined ? options.angle : Math.PI / 3;
        var penumbra = options.penumbra !== undefined ? options.penumbra : 0;
        var decay = options.decay !== undefined ? options.decay : 2;
        var light = new THREE.SpotLight(color, intensity, distance, angle, penumbra, decay);
        // 设置阴影
        light.castShadow = options.castShadow !== undefined ? options.castShadow : false;
        if (light.castShadow) {
            // 设置阴影贴图大小
            if (options.shadowMapSize !== undefined) {
                light.shadow.mapSize.width = options.shadowMapSize;
                light.shadow.mapSize.height = options.shadowMapSize;
            }
            // 设置阴影相机参数
            if (options.shadowCameraNear !== undefined)
                light.shadow.camera.near = options.shadowCameraNear;
            if (options.shadowCameraFar !== undefined)
                light.shadow.camera.far = options.shadowCameraFar;
            // 设置阴影偏移和半径
            if (options.shadowBias !== undefined)
                light.shadow.bias = options.shadowBias;
            if (options.shadowRadius !== undefined)
                light.shadow.radius = options.shadowRadius;
        }
        return light;
    };
    /**
     * 创建半球光
     * @param options 半球光选项
     * @returns Three.js半球光
     */
    Light.prototype.createHemisphereLight = function (options) {
        var skyColor = options.color !== undefined ? options.color : 0xffffff;
        var groundColor = options.groundColor !== undefined ? options.groundColor : 0x444444;
        var intensity = options.intensity !== undefined ? options.intensity : 1;
        return new THREE.HemisphereLight(skyColor, groundColor, intensity);
    };
    /**
     * 创建矩形区域光
     * @param options 矩形区域光选项
     * @returns Three.js矩形区域光
     */
    Light.prototype.createRectAreaLight = function (options) {
        var color = options.color !== undefined ? options.color : 0xffffff;
        var intensity = options.intensity !== undefined ? options.intensity : 1;
        var width = options.width !== undefined ? options.width : 10;
        var height = options.height !== undefined ? options.height : 10;
        return new THREE.RectAreaLight(color, intensity, width, height);
    };
    /**
     * 当组件附加到实体时调用
     */
    Light.prototype.onAttach = function () {
        var _a;
        if (!this.entity)
            return;
        // 获取实体的变换组件
        var transform = this.entity.getTransform();
        if (transform) {
            // 将光源添加到变换的Three.js对象
            transform.getObject3D().add(this.light);
            // 如果是方向光或聚光灯，还需要添加目标
            if (this.light instanceof THREE.DirectionalLight || this.light instanceof THREE.SpotLight) {
                transform.getObject3D().add(this.light.target);
            }
        }
        // 如果是方向光，通知阴影系统
        if (this.lightType === LightType.DIRECTIONAL) {
            var shadowSystem = (_a = this.entity.getWorld()) === null || _a === void 0 ? void 0 : _a.getSystem('ShadowSystem');
            if (shadowSystem) {
                shadowSystem.addDirectionalLight(this.entity);
            }
        }
    };
    /**
     * 当组件从实体分离时调用
     */
    Light.prototype.onDetach = function () {
        var _a;
        // 如果是方向光，通知阴影系统
        if (this.lightType === LightType.DIRECTIONAL) {
            var shadowSystem = (_a = this.entity.getWorld()) === null || _a === void 0 ? void 0 : _a.getSystem('ShadowSystem');
            if (shadowSystem) {
                shadowSystem.removeDirectionalLight(this.entity);
            }
        }
    };
    /**
     * 获取光源类型
     * @returns 光源类型
     */
    Light.prototype.getType = function () {
        return this.lightType;
    };
    /**
     * 获取Three.js光源
     * @returns Three.js光源
     */
    Light.prototype.getThreeLight = function () {
        return this.light;
    };
    /**
     * 设置光源颜色
     * @param color 颜色
     */
    Light.prototype.setColor = function (color) {
        if (this.light instanceof THREE.Light && !(this.light instanceof THREE.HemisphereLight)) {
            this.light.color.set(color);
        }
        else if (this.light instanceof THREE.HemisphereLight) {
            this.light.skyColor.set(color);
        }
    };
    /**
     * 设置光源强度
     * @param intensity 强度
     */
    Light.prototype.setIntensity = function (intensity) {
        if (this.light instanceof THREE.Light) {
            this.light.intensity = intensity;
        }
    };
    /**
     * 设置是否投射阴影
     * @param castShadow 是否投射阴影
     */
    Light.prototype.setCastShadow = function (castShadow) {
        if (this.light instanceof THREE.DirectionalLight ||
            this.light instanceof THREE.PointLight ||
            this.light instanceof THREE.SpotLight) {
            this.light.castShadow = castShadow;
        }
    };
    /** 组件类型 */
    Light.type = 'Light';
    return Light;
}(Component_1.Component));

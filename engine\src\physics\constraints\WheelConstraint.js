"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WheelConstraint = void 0;
/**
 * 车轮约束
 * 模拟车轮的悬挂和转向
 */
var THREE = require("three");
var CANNON = require("cannon-es");
var PhysicsConstraint_1 = require("./PhysicsConstraint");
/**
 * 车轮约束
 */
var WheelConstraint = exports.WheelConstraint = /** @class */ (function (_super) {
    __extends(WheelConstraint, _super);
    /**
     * 创建车轮约束
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    function WheelConstraint(targetEntity, options) {
        if (targetEntity === void 0) { targetEntity = null; }
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, PhysicsConstraint_1.ConstraintType.WHEEL, targetEntity, options) || this;
        /** 悬挂约束 */
        _this.suspensionConstraint = null;
        /** 转向约束 */
        _this.steeringConstraint = null;
        // 设置轴向
        _this.axisA = options.axisA ? options.axisA.clone() : new THREE.Vector3(0, 1, 0);
        _this.axisB = options.axisB ? options.axisB.clone() : new THREE.Vector3(0, 0, 1);
        // 设置悬挂参数
        _this.suspensionStiffness = options.suspensionStiffness !== undefined ? options.suspensionStiffness : 100;
        _this.suspensionDamping = options.suspensionDamping !== undefined ? options.suspensionDamping : 10;
        _this.suspensionLength = options.suspensionLength !== undefined ? options.suspensionLength : 0.2;
        _this.suspensionMaxLength = options.suspensionMaxLength !== undefined ? options.suspensionMaxLength : 0.3;
        _this.suspensionMinLength = options.suspensionMinLength !== undefined ? options.suspensionMinLength : 0.1;
        // 设置转向角度
        _this.steeringAngle = options.steeringAngle !== undefined ? options.steeringAngle : 0;
        // 设置最大力
        _this.maxForce = options.maxForce !== undefined ? options.maxForce : 1e6;
        return _this;
    }
    /**
     * 创建约束
     */
    WheelConstraint.prototype.createConstraint = function () {
        // 获取源物体和目标物体
        var bodyA = this.getSourceBody();
        var bodyB = this.getTargetBody();
        // 如果没有源物体或目标物体，则无法创建约束
        if (!bodyA || !bodyB) {
            console.warn('无法创建车轮约束：缺少源物体或目标物体');
            return;
        }
        // 创建悬挂约束
        this.suspensionConstraint = new CANNON.PointToPointConstraint(bodyA, new CANNON.Vec3(0, -this.suspensionLength, 0), bodyB, new CANNON.Vec3(0, 0, 0), this.maxForce);
        // 创建转向约束
        this.steeringConstraint = new CANNON.HingeConstraint(bodyA, bodyB, {
            pivotA: new CANNON.Vec3(0, -this.suspensionLength, 0),
            axisA: new CANNON.Vec3(this.axisA.x, this.axisA.y, this.axisA.z),
            pivotB: new CANNON.Vec3(0, 0, 0),
            axisB: new CANNON.Vec3(this.axisB.x, this.axisB.y, this.axisB.z)
        });
        // 设置转向角度
        this.steeringConstraint.setMotorEnabled(true);
        this.steeringConstraint.setMotorSpeed(0);
        this.steeringConstraint.setMotorMaxForce(this.maxForce);
        // 设置是否允许连接的物体之间碰撞
        this.suspensionConstraint.collideConnected = this.collideConnected;
        this.steeringConstraint.collideConnected = this.collideConnected;
        // 保存约束
        this.constraint = this.suspensionConstraint;
        // 添加转向约束到物理世界
        if (this.world) {
            this.world.addConstraint(this.steeringConstraint);
        }
    };
    /**
     * 更新约束
     */
    WheelConstraint.prototype.update = function () {
        if (!this.suspensionConstraint || !this.steeringConstraint) {
            return;
        }
        // 更新转向角度
        this.steeringConstraint.setMotorTarget(this.steeringAngle);
    };
    /**
     * 设置悬挂轴向
     * @param axis 轴向
     */
    WheelConstraint.prototype.setAxisA = function (axis) {
        this.axisA.copy(axis);
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取悬挂轴向
     * @returns 轴向
     */
    WheelConstraint.prototype.getAxisA = function () {
        return this.axisA.clone();
    };
    /**
     * 设置转向轴向
     * @param axis 轴向
     */
    WheelConstraint.prototype.setAxisB = function (axis) {
        this.axisB.copy(axis);
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取转向轴向
     * @returns 轴向
     */
    WheelConstraint.prototype.getAxisB = function () {
        return this.axisB.clone();
    };
    /**
     * 设置悬挂刚度
     * @param stiffness 刚度
     */
    WheelConstraint.prototype.setSuspensionStiffness = function (stiffness) {
        this.suspensionStiffness = stiffness;
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取悬挂刚度
     * @returns 刚度
     */
    WheelConstraint.prototype.getSuspensionStiffness = function () {
        return this.suspensionStiffness;
    };
    /**
     * 设置悬挂阻尼
     * @param damping 阻尼
     */
    WheelConstraint.prototype.setSuspensionDamping = function (damping) {
        this.suspensionDamping = damping;
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取悬挂阻尼
     * @returns 阻尼
     */
    WheelConstraint.prototype.getSuspensionDamping = function () {
        return this.suspensionDamping;
    };
    /**
     * 设置悬挂长度
     * @param length 长度
     */
    WheelConstraint.prototype.setSuspensionLength = function (length) {
        this.suspensionLength = length;
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取悬挂长度
     * @returns 长度
     */
    WheelConstraint.prototype.getSuspensionLength = function () {
        return this.suspensionLength;
    };
    /**
     * 重新创建约束
     */
    WheelConstraint.prototype.recreateConstraint = function () {
        if (this.initialized && this.constraint && this.world) {
            // 移除旧约束
            this.world.removeConstraint(this.constraint);
            if (this.steeringConstraint) {
                this.world.removeConstraint(this.steeringConstraint);
            }
            // 重置状态
            this.constraint = null;
            this.suspensionConstraint = null;
            this.steeringConstraint = null;
            this.initialized = false;
            // 重新初始化
            this.initialize(this.world);
        }
    };
    /**
     * 设置转向角度
     * @param angle 角度
     */
    WheelConstraint.prototype.setSteeringAngle = function (angle) {
        this.steeringAngle = angle;
        // 更新约束
        this.update();
    };
    /**
     * 获取转向角度
     * @returns 角度
     */
    WheelConstraint.prototype.getSteeringAngle = function () {
        return this.steeringAngle;
    };
    /**
     * 销毁约束
     */
    WheelConstraint.prototype.dispose = function () {
        // 移除转向约束
        if (this.steeringConstraint && this.world) {
            this.world.removeConstraint(this.steeringConstraint);
            this.steeringConstraint = null;
        }
        // 调用父类的销毁方法
        _super.prototype.dispose.call(this);
    };
    /** 组件类型 */
    WheelConstraint.type = 'WheelConstraint';
    return WheelConstraint;
}(PhysicsConstraint_1.PhysicsConstraint));

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SoftBodyComponent = exports.SoftBodyType = void 0;
/**
 * 软体组件
 * 基于粒子和约束实现软体物理
 */
var THREE = require("three");
var CANNON = require("cannon-es");
var Component_1 = require("../../core/Component");
var PhysicsMaterialFactory_1 = require("../PhysicsMaterialFactory");
/**
 * 软体类型
 */
var SoftBodyType;
(function (SoftBodyType) {
    /** 布料 */
    SoftBodyType["CLOTH"] = "cloth";
    /** 绳索 */
    SoftBodyType["ROPE"] = "rope";
    /** 体积软体 */
    SoftBodyType["VOLUME"] = "volume";
    /** 气球 */
    SoftBodyType["BALLOON"] = "balloon";
    /** 果冻 */
    SoftBodyType["JELLY"] = "jelly";
})(SoftBodyType || (exports.SoftBodyType = SoftBodyType = {}));
/**
 * 软体组件
 */
var SoftBodyComponent = exports.SoftBodyComponent = /** @class */ (function (_super) {
    __extends(SoftBodyComponent, _super);
    /**
     * 创建软体组件
     * @param options 软体组件选项
     */
    function SoftBodyComponent(options) {
        var _this = _super.call(this, SoftBodyComponent.type) || this;
        /** 网格对象 */
        _this.mesh = null;
        /** 粒子物理体数组 */
        _this.particles = [];
        /** 约束数组 */
        _this.constraints = [];
        /** 物理约束数组 */
        _this.physicsConstraints = [];
        /** 是否已初始化 */
        _this.initialized = false;
        /** 物理世界引用 */
        _this.world = null;
        _this.type = options.type;
        _this.mass = options.mass || 1;
        _this.stiffness = options.stiffness || 100;
        _this.damping = options.damping || 0.1;
        _this.fixedCorners = options.fixedCorners || false;
        _this.fixedEnds = options.fixedEnds || false;
        _this.mesh = options.mesh || null;
        _this.material = options.materialName ?
            PhysicsMaterialFactory_1.PhysicsMaterialFactory.getMaterial(options.materialName) :
            null;
        _this.params = options.params || {};
        return _this;
    }
    /**
     * 初始化软体
     * @param world 物理世界
     */
    SoftBodyComponent.prototype.initialize = function (world) {
        if (this.initialized || !this.entity)
            return;
        this.world = world;
        // 根据软体类型创建不同的软体
        switch (this.type) {
            case SoftBodyType.CLOTH:
                this.createCloth();
                break;
            case SoftBodyType.ROPE:
                this.createRope();
                break;
            case SoftBodyType.VOLUME:
                this.createVolumeSoftBody();
                break;
            case SoftBodyType.BALLOON:
                this.createBalloon();
                break;
            case SoftBodyType.JELLY:
                this.createJelly();
                break;
            default:
                console.warn("\u4E0D\u652F\u6301\u7684\u8F6F\u4F53\u7C7B\u578B: ".concat(this.type));
                return;
        }
        // 创建物理约束
        this.createPhysicsConstraints();
        this.initialized = true;
    };
    /**
     * 获取软体类型
     * @returns 软体类型
     */
    SoftBodyComponent.prototype.getType = function () {
        return this.type;
    };
    /**
     * 获取粒子数组
     * @returns 粒子数组
     */
    SoftBodyComponent.prototype.getParticles = function () {
        if (!this.initialized)
            return null;
        return this.particles;
    };
    /**
     * 应用外力到所有粒子
     * @param force 力向量
     */
    SoftBodyComponent.prototype.applyForce = function (force) {
        if (!this.initialized)
            return;
        var cannonForce = new CANNON.Vec3(force.x, force.y, force.z);
        for (var _i = 0, _a = this.particles; _i < _a.length; _i++) {
            var particle = _a[_i];
            particle.applyForce(cannonForce, particle.position);
        }
    };
    /**
     * 应用冲量到所有粒子
     * @param impulse 冲量向量
     */
    SoftBodyComponent.prototype.applyImpulse = function (impulse) {
        if (!this.initialized)
            return;
        var cannonImpulse = new CANNON.Vec3(impulse.x, impulse.y, impulse.z);
        for (var _i = 0, _a = this.particles; _i < _a.length; _i++) {
            var particle = _a[_i];
            particle.applyImpulse(cannonImpulse, particle.position);
        }
    };
    /**
     * 获取约束数组
     * @returns 约束数组
     */
    SoftBodyComponent.prototype.getConstraints = function () {
        if (!this.initialized)
            return null;
        return this.constraints;
    };
    /**
     * 移除约束
     * @param constraintIndices 约束索引数组
     * @returns 是否成功移除
     */
    SoftBodyComponent.prototype.removeConstraints = function (constraintIndices) {
        if (!this.initialized || !this.world)
            return false;
        // 按索引从大到小排序，以便从后向前移除
        constraintIndices.sort(function (a, b) { return b - a; });
        // 移除物理约束
        for (var _i = 0, constraintIndices_1 = constraintIndices; _i < constraintIndices_1.length; _i++) {
            var index = constraintIndices_1[_i];
            if (index >= 0 && index < this.physicsConstraints.length) {
                var constraint = this.physicsConstraints[index];
                this.world.removeConstraint(constraint);
                // 从数组中移除
                this.physicsConstraints.splice(index, 1);
                this.constraints.splice(index, 1);
            }
        }
        return constraintIndices.length > 0;
    };
    /**
     * 设置参数
     * @param name 参数名
     * @param value 参数值
     */
    SoftBodyComponent.prototype.setParameter = function (name, value) {
        if (!this.initialized)
            return;
        this.params[name] = value;
        // 特殊处理某些参数
        if (name === 'pressure' && this.type === SoftBodyType.BALLOON) {
            // 更新气球压力
            this.params.pressure = value;
        }
    };
    /**
     * 创建布料
     */
    SoftBodyComponent.prototype.createCloth = function () {
        var _a, _b, _c;
        // 从参数中获取网格大小
        var gridSize = this.params.gridSize || { x: 10, y: 10 };
        var width = this.params.width || 10;
        var height = this.params.height || 10;
        var segmentsX = gridSize.x;
        var segmentsY = gridSize.y;
        // 如果没有提供网格，创建一个平面网格
        if (!this.mesh) {
            var geometry = new THREE.PlaneGeometry(width, height, segmentsX, segmentsY);
            var material = new THREE.MeshStandardMaterial({
                color: 0x3366cc,
                side: THREE.DoubleSide,
                wireframe: false
            });
            this.mesh = new THREE.Mesh(geometry, material);
            (_a = this.entity) === null || _a === void 0 ? void 0 : _a.addComponent('MeshComponent', { mesh: this.mesh });
        }
        // 创建粒子
        var stepX = width / segmentsX;
        var stepY = height / segmentsY;
        var particleMass = this.mass / (segmentsX * segmentsY);
        // 获取实体的变换
        var transform = (_b = this.entity) === null || _b === void 0 ? void 0 : _b.getTransform();
        var position = transform ? transform.getPosition() : new THREE.Vector3();
        var rotation = transform ? transform.getRotationQuaternion() : new THREE.Quaternion();
        // 创建粒子网格
        for (var y = 0; y <= segmentsY; y++) {
            for (var x = 0; x <= segmentsX; x++) {
                // 计算粒子位置
                var px = (x / segmentsX - 0.5) * width;
                var py = 0;
                var pz = (y / segmentsY - 0.5) * height;
                // 创建粒子
                var particlePos = new THREE.Vector3(px, py, pz)
                    .applyQuaternion(rotation)
                    .add(position);
                var particle = new CANNON.Body({
                    mass: this.fixedCorners && ((x === 0 && y === 0) ||
                        (x === segmentsX && y === 0) ||
                        (x === 0 && y === segmentsY) ||
                        (x === segmentsX && y === segmentsY)) ? 0 : particleMass,
                    position: new CANNON.Vec3(particlePos.x, particlePos.y, particlePos.z),
                    shape: new CANNON.Particle(),
                    material: this.material || undefined,
                    linearDamping: this.damping
                });
                this.particles.push(particle);
                (_c = this.world) === null || _c === void 0 ? void 0 : _c.addBody(particle);
            }
        }
        // 创建约束
        var particlesPerRow = segmentsX + 1;
        // 水平约束
        for (var y = 0; y <= segmentsY; y++) {
            for (var x = 0; x < segmentsX; x++) {
                var i1 = y * particlesPerRow + x;
                var i2 = y * particlesPerRow + x + 1;
                var p1 = this.particles[i1].position;
                var p2 = this.particles[i2].position;
                var restLength = p1.distanceTo(p2);
                this.constraints.push({
                    particleA: i1,
                    particleB: i2,
                    restLength: restLength,
                    stiffness: this.stiffness
                });
            }
        }
        // 垂直约束
        for (var x = 0; x <= segmentsX; x++) {
            for (var y = 0; y < segmentsY; y++) {
                var i1 = y * particlesPerRow + x;
                var i2 = (y + 1) * particlesPerRow + x;
                var p1 = this.particles[i1].position;
                var p2 = this.particles[i2].position;
                var restLength = p1.distanceTo(p2);
                this.constraints.push({
                    particleA: i1,
                    particleB: i2,
                    restLength: restLength,
                    stiffness: this.stiffness
                });
            }
        }
        // 对角线约束（增加稳定性）
        for (var y = 0; y < segmentsY; y++) {
            for (var x = 0; x < segmentsX; x++) {
                var i1 = y * particlesPerRow + x;
                var i2 = (y + 1) * particlesPerRow + x + 1;
                var p1 = this.particles[i1].position;
                var p2 = this.particles[i2].position;
                var restLength = p1.distanceTo(p2);
                this.constraints.push({
                    particleA: i1,
                    particleB: i2,
                    restLength: restLength,
                    stiffness: this.stiffness * 0.5 // 对角线约束刚度较低
                });
                var i3 = y * particlesPerRow + x + 1;
                var i4 = (y + 1) * particlesPerRow + x;
                var p3 = this.particles[i3].position;
                var p4 = this.particles[i4].position;
                var restLength2 = p3.distanceTo(p4);
                this.constraints.push({
                    particleA: i3,
                    particleB: i4,
                    restLength: restLength2,
                    stiffness: this.stiffness * 0.5
                });
            }
        }
    };
    /**
     * 创建绳索
     */
    SoftBodyComponent.prototype.createRope = function () {
        var _a, _b, _c;
        // 从参数中获取绳索参数
        var segments = this.params.segments || 10;
        var length = this.params.length || 10;
        // 如果没有提供网格，创建一个绳索网格
        if (!this.mesh) {
            var points = [];
            for (var i = 0; i <= segments; i++) {
                points.push(new THREE.Vector3(0, -i * (length / segments), 0));
            }
            var geometry = new THREE.BufferGeometry().setFromPoints(points);
            var material = new THREE.LineBasicMaterial({ color: 0x3366cc });
            this.mesh = new THREE.Line(geometry, material);
            (_a = this.entity) === null || _a === void 0 ? void 0 : _a.addComponent('MeshComponent', { mesh: this.mesh });
        }
        // 获取实体的变换
        var transform = (_b = this.entity) === null || _b === void 0 ? void 0 : _b.getTransform();
        var position = transform ? transform.getPosition() : new THREE.Vector3();
        var rotation = transform ? transform.getRotationQuaternion() : new THREE.Quaternion();
        // 创建粒子
        var particleMass = this.mass / segments;
        var segmentLength = length / segments;
        for (var i = 0; i <= segments; i++) {
            // 计算粒子位置
            var particlePos = new THREE.Vector3(0, -i * segmentLength, 0)
                .applyQuaternion(rotation)
                .add(position);
            // 创建粒子
            var particle = new CANNON.Body({
                mass: (this.fixedEnds && (i === 0 || i === segments)) ? 0 : particleMass,
                position: new CANNON.Vec3(particlePos.x, particlePos.y, particlePos.z),
                shape: new CANNON.Particle(),
                material: this.material || undefined,
                linearDamping: this.damping
            });
            this.particles.push(particle);
            (_c = this.world) === null || _c === void 0 ? void 0 : _c.addBody(particle);
        }
        // 创建约束
        for (var i = 0; i < segments; i++) {
            var p1 = this.particles[i].position;
            var p2 = this.particles[i + 1].position;
            var restLength = p1.distanceTo(p2);
            this.constraints.push({
                particleA: i,
                particleB: i + 1,
                restLength: restLength,
                stiffness: this.stiffness
            });
        }
    };
    /**
     * 创建体积软体
     */
    SoftBodyComponent.prototype.createVolumeSoftBody = function () {
        var _a, _b, _c;
        // 从参数中获取体积软体参数
        var size = this.params.size || 1;
        var segments = this.params.segments || 3;
        // 如果没有提供网格，创建一个立方体网格
        if (!this.mesh) {
            var geometry = new THREE.BoxGeometry(size, size, size, segments, segments, segments);
            var material = new THREE.MeshStandardMaterial({
                color: 0x3366cc,
                wireframe: true
            });
            this.mesh = new THREE.Mesh(geometry, material);
            (_a = this.entity) === null || _a === void 0 ? void 0 : _a.addComponent('MeshComponent', { mesh: this.mesh });
        }
        // 获取实体的变换
        var transform = (_b = this.entity) === null || _b === void 0 ? void 0 : _b.getTransform();
        var position = transform ? transform.getPosition() : new THREE.Vector3();
        var rotation = transform ? transform.getRotationQuaternion() : new THREE.Quaternion();
        // 创建粒子
        var particleMass = this.mass / Math.pow(segments + 1, 3);
        var step = size / segments;
        // 创建3D网格的粒子
        for (var z = 0; z <= segments; z++) {
            for (var y = 0; y <= segments; y++) {
                for (var x = 0; x <= segments; x++) {
                    // 计算粒子位置
                    var px = (x / segments - 0.5) * size;
                    var py = (y / segments - 0.5) * size;
                    var pz = (z / segments - 0.5) * size;
                    var particlePos = new THREE.Vector3(px, py, pz)
                        .applyQuaternion(rotation)
                        .add(position);
                    // 创建粒子
                    var particle = new CANNON.Body({
                        mass: particleMass,
                        position: new CANNON.Vec3(particlePos.x, particlePos.y, particlePos.z),
                        shape: new CANNON.Particle(),
                        material: this.material || undefined,
                        linearDamping: this.damping
                    });
                    this.particles.push(particle);
                    (_c = this.world) === null || _c === void 0 ? void 0 : _c.addBody(particle);
                }
            }
        }
        // 创建约束
        var particlesPerRow = segments + 1;
        var particlesPerSlice = particlesPerRow * particlesPerRow;
        // X方向约束
        for (var z = 0; z <= segments; z++) {
            for (var y = 0; y <= segments; y++) {
                for (var x = 0; x < segments; x++) {
                    var i1 = z * particlesPerSlice + y * particlesPerRow + x;
                    var i2 = z * particlesPerSlice + y * particlesPerRow + x + 1;
                    var p1 = this.particles[i1].position;
                    var p2 = this.particles[i2].position;
                    var restLength = p1.distanceTo(p2);
                    this.constraints.push({
                        particleA: i1,
                        particleB: i2,
                        restLength: restLength,
                        stiffness: this.stiffness
                    });
                }
            }
        }
        // Y方向约束
        for (var z = 0; z <= segments; z++) {
            for (var x = 0; x <= segments; x++) {
                for (var y = 0; y < segments; y++) {
                    var i1 = z * particlesPerSlice + y * particlesPerRow + x;
                    var i2 = z * particlesPerSlice + (y + 1) * particlesPerRow + x;
                    var p1 = this.particles[i1].position;
                    var p2 = this.particles[i2].position;
                    var restLength = p1.distanceTo(p2);
                    this.constraints.push({
                        particleA: i1,
                        particleB: i2,
                        restLength: restLength,
                        stiffness: this.stiffness
                    });
                }
            }
        }
        // Z方向约束
        for (var y = 0; y <= segments; y++) {
            for (var x = 0; x <= segments; x++) {
                for (var z = 0; z < segments; z++) {
                    var i1 = z * particlesPerSlice + y * particlesPerRow + x;
                    var i2 = (z + 1) * particlesPerSlice + y * particlesPerRow + x;
                    var p1 = this.particles[i1].position;
                    var p2 = this.particles[i2].position;
                    var restLength = p1.distanceTo(p2);
                    this.constraints.push({
                        particleA: i1,
                        particleB: i2,
                        restLength: restLength,
                        stiffness: this.stiffness
                    });
                }
            }
        }
        // 对角线约束（可选，增加稳定性）
        if (this.params.addDiagonalConstraints) {
            // 实现对角线约束...
        }
    };
    /**
     * 创建物理约束
     */
    SoftBodyComponent.prototype.createPhysicsConstraints = function () {
        var _a;
        // 为每个约束创建物理约束
        for (var _i = 0, _b = this.constraints; _i < _b.length; _i++) {
            var constraint = _b[_i];
            var particleA = this.particles[constraint.particleA];
            var particleB = this.particles[constraint.particleB];
            // 创建距离约束
            var distanceConstraint = new CANNON.DistanceConstraint(particleA, particleB, constraint.restLength, constraint.stiffness);
            this.physicsConstraints.push(distanceConstraint);
            (_a = this.world) === null || _a === void 0 ? void 0 : _a.addConstraint(distanceConstraint);
        }
    };
    /**
     * 求解约束
     * @param deltaTime 时间步长
     */
    SoftBodyComponent.prototype.solveConstraints = function (deltaTime) {
        if (!this.initialized)
            return;
        // 手动求解约束（可选，如果物理引擎的约束求解不够稳定）
        for (var i = 0; i < this.constraints.length; i++) {
            var constraint = this.constraints[i];
            var particleA = this.particles[constraint.particleA];
            var particleB = this.particles[constraint.particleB];
            // 计算当前长度
            var delta = new CANNON.Vec3();
            delta.copy(particleB.position);
            delta.vsub(particleA.position);
            var currentLength = delta.length();
            // 如果长度接近约束长度，跳过
            if (Math.abs(currentLength - constraint.restLength) < 0.001)
                continue;
            // 计算校正因子
            var correctionFactor = (currentLength - constraint.restLength) / currentLength;
            // 应用校正
            var correction = new CANNON.Vec3();
            correction.copy(delta);
            correction.scale(correctionFactor * 0.5 * constraint.stiffness * deltaTime);
            // 如果粒子不是固定的，应用校正
            if (particleA.mass > 0) {
                particleA.position.vadd(correction);
            }
            if (particleB.mass > 0) {
                particleB.position.vsub(correction);
            }
        }
    };
    /**
     * 更新网格
     */
    SoftBodyComponent.prototype.updateMesh = function () {
        if (!this.initialized || !this.mesh)
            return;
        // 根据软体类型更新网格
        switch (this.type) {
            case SoftBodyType.CLOTH:
                this.updateClothMesh();
                break;
            case SoftBodyType.ROPE:
                this.updateRopeMesh();
                break;
            case SoftBodyType.VOLUME:
                this.updateVolumeMesh();
                break;
            case SoftBodyType.BALLOON:
                this.updateBalloonMesh();
                break;
            case SoftBodyType.JELLY:
                this.updateJellyMesh();
                break;
        }
    };
    /**
     * 更新布料网格
     */
    SoftBodyComponent.prototype.updateClothMesh = function () {
        if (!this.mesh)
            return;
        // 获取网格的顶点位置
        var geometry = this.mesh.geometry;
        var positionAttribute = geometry.getAttribute('position');
        // 更新顶点位置
        for (var i = 0; i < this.particles.length; i++) {
            var particle = this.particles[i];
            positionAttribute.setXYZ(i, particle.getPosition().x, particle.getPosition().y, particle.getPosition().z);
        }
        // 标记位置属性需要更新
        positionAttribute.needsUpdate = true;
        // 更新法线
        geometry.computeVertexNormals();
    };
    /**
     * 更新绳索网格
     */
    SoftBodyComponent.prototype.updateRopeMesh = function () {
        if (!this.mesh)
            return;
        // 获取网格的顶点位置
        var geometry = this.mesh.geometry;
        var positionAttribute = geometry.getAttribute('position');
        // 更新顶点位置
        for (var i = 0; i < this.particles.length; i++) {
            var particle = this.particles[i];
            positionAttribute.setXYZ(i, particle.getPosition().x, particle.getPosition().y, particle.getPosition().z);
        }
        // 标记位置属性需要更新
        positionAttribute.needsUpdate = true;
    };
    /**
     * 更新体积软体网格
     */
    SoftBodyComponent.prototype.updateVolumeMesh = function () {
        if (!this.mesh)
            return;
        // 获取网格的顶点位置
        var geometry = this.mesh.geometry;
        var positionAttribute = geometry.getAttribute('position');
        // 更新顶点位置
        for (var i = 0; i < this.particles.length; i++) {
            var particle = this.particles[i];
            positionAttribute.setXYZ(i, particle.getPosition().x, particle.getPosition().y, particle.getPosition().z);
        }
        // 标记位置属性需要更新
        positionAttribute.needsUpdate = true;
        // 更新法线
        geometry.computeVertexNormals();
    };
    /**
     * 创建气球
     */
    SoftBodyComponent.prototype.createBalloon = function () {
        var _a, _b, _c;
        // 从参数中获取气球参数
        var radius = this.params.radius || 1;
        var segments = this.params.segments || 16;
        var pressure = this.params.pressure || 10;
        // 如果没有提供网格，创建一个球体网格
        if (!this.mesh) {
            var geometry = new THREE.SphereGeometry(radius, segments, segments);
            var material = new THREE.MeshStandardMaterial({
                color: 0xff0000,
                side: THREE.DoubleSide
            });
            this.mesh = new THREE.Mesh(geometry, material);
            (_a = this.entity) === null || _a === void 0 ? void 0 : _a.addComponent('MeshComponent', { mesh: this.mesh });
        }
        // 获取实体的变换
        var transform = (_b = this.entity) === null || _b === void 0 ? void 0 : _b.getTransform();
        var position = transform ? transform.getPosition() : new THREE.Vector3();
        var rotation = transform ? transform.getRotationQuaternion() : new THREE.Quaternion();
        // 创建粒子
        var particleMass = this.mass / (segments * segments);
        // 创建球面上的粒子
        for (var i = 0; i <= segments; i++) {
            var phi = Math.PI * i / segments;
            var sinPhi = Math.sin(phi);
            var cosPhi = Math.cos(phi);
            for (var j = 0; j <= segments; j++) {
                var theta = 2 * Math.PI * j / segments;
                var sinTheta = Math.sin(theta);
                var cosTheta = Math.cos(theta);
                // 计算球面上的点
                var x = radius * sinPhi * cosTheta;
                var y = radius * cosPhi;
                var z = radius * sinPhi * sinTheta;
                // 创建粒子
                var particlePos = new THREE.Vector3(x, y, z)
                    .applyQuaternion(rotation)
                    .add(position);
                var particle = new CANNON.Body({
                    mass: particleMass,
                    position: new CANNON.Vec3(particlePos.x, particlePos.y, particlePos.z),
                    shape: new CANNON.Particle(),
                    material: this.material || undefined,
                    linearDamping: this.damping
                });
                this.particles.push(particle);
                (_c = this.world) === null || _c === void 0 ? void 0 : _c.addBody(particle);
            }
        }
        // 创建约束
        var particlesPerRow = segments + 1;
        // 创建表面约束
        for (var i = 0; i < segments; i++) {
            for (var j = 0; j < segments; j++) {
                var i1 = i * particlesPerRow + j;
                var i2 = i * particlesPerRow + (j + 1);
                var i3 = (i + 1) * particlesPerRow + j;
                var i4 = (i + 1) * particlesPerRow + (j + 1);
                // 水平约束
                this.addConstraint(i1, i2);
                // 垂直约束
                this.addConstraint(i1, i3);
                // 对角线约束
                this.addConstraint(i1, i4);
                this.addConstraint(i2, i3);
            }
        }
        // 存储气球参数
        this.params.pressure = pressure;
    };
    /**
     * 创建果冻
     */
    SoftBodyComponent.prototype.createJelly = function () {
        var _a, _b, _c;
        // 从参数中获取果冻参数
        var size = this.params.size || 1;
        var segments = this.params.segments || 5;
        var stiffness = this.params.stiffness || 300;
        var damping = this.params.damping || 0.5;
        // 如果没有提供网格，创建一个立方体网格
        if (!this.mesh) {
            var geometry = new THREE.BoxGeometry(size, size, size, segments, segments, segments);
            var material = new THREE.MeshStandardMaterial({
                color: 0x00ff00,
                transparent: true,
                opacity: 0.8
            });
            this.mesh = new THREE.Mesh(geometry, material);
            (_a = this.entity) === null || _a === void 0 ? void 0 : _a.addComponent('MeshComponent', { mesh: this.mesh });
        }
        // 获取实体的变换
        var transform = (_b = this.entity) === null || _b === void 0 ? void 0 : _b.getTransform();
        var position = transform ? transform.getPosition() : new THREE.Vector3();
        var rotation = transform ? transform.getRotationQuaternion() : new THREE.Quaternion();
        // 创建粒子
        var particleMass = this.mass / Math.pow(segments + 1, 3);
        var step = size / segments;
        // 创建3D网格的粒子
        for (var z = 0; z <= segments; z++) {
            for (var y = 0; y <= segments; y++) {
                for (var x = 0; x <= segments; x++) {
                    // 计算粒子位置
                    var px = (x / segments - 0.5) * size;
                    var py = (y / segments - 0.5) * size;
                    var pz = (z / segments - 0.5) * size;
                    var particlePos = new THREE.Vector3(px, py, pz)
                        .applyQuaternion(rotation)
                        .add(position);
                    // 创建粒子
                    var particle = new CANNON.Body({
                        mass: particleMass,
                        position: new CANNON.Vec3(particlePos.x, particlePos.y, particlePos.z),
                        shape: new CANNON.Particle(),
                        material: this.material || undefined,
                        linearDamping: damping
                    });
                    this.particles.push(particle);
                    (_c = this.world) === null || _c === void 0 ? void 0 : _c.addBody(particle);
                }
            }
        }
        // 创建约束
        var particlesPerRow = segments + 1;
        var particlesPerSlice = particlesPerRow * particlesPerRow;
        // 创建内部约束
        for (var z = 0; z <= segments; z++) {
            for (var y = 0; y <= segments; y++) {
                for (var x = 0; x <= segments; x++) {
                    var index = z * particlesPerSlice + y * particlesPerRow + x;
                    // 连接到相邻粒子
                    if (x < segments)
                        this.addConstraint(index, index + 1, stiffness);
                    if (y < segments)
                        this.addConstraint(index, index + particlesPerRow, stiffness);
                    if (z < segments)
                        this.addConstraint(index, index + particlesPerSlice, stiffness);
                    // 对角线约束（增加稳定性）
                    if (x < segments && y < segments) {
                        this.addConstraint(index, index + particlesPerRow + 1, stiffness * 0.7);
                        this.addConstraint(index + 1, index + particlesPerRow, stiffness * 0.7);
                    }
                    if (y < segments && z < segments) {
                        this.addConstraint(index, index + particlesPerSlice + particlesPerRow, stiffness * 0.7);
                        this.addConstraint(index + particlesPerRow, index + particlesPerSlice, stiffness * 0.7);
                    }
                    if (x < segments && z < segments) {
                        this.addConstraint(index, index + particlesPerSlice + 1, stiffness * 0.7);
                        this.addConstraint(index + 1, index + particlesPerSlice, stiffness * 0.7);
                    }
                }
            }
        }
    };
    /**
     * 添加约束
     * @param particleA 粒子A索引
     * @param particleB 粒子B索引
     * @param stiffness 刚度（可选）
     */
    SoftBodyComponent.prototype.addConstraint = function (particleA, particleB, stiffness) {
        var p1 = this.particles[particleA].position;
        var p2 = this.particles[particleB].position;
        var restLength = p1.distanceTo(p2);
        this.constraints.push({
            particleA: particleA,
            particleB: particleB,
            restLength: restLength,
            stiffness: stiffness || this.stiffness
        });
    };
    /**
     * 更新气球网格
     */
    SoftBodyComponent.prototype.updateBalloonMesh = function () {
        if (!this.mesh)
            return;
        // 获取网格的顶点位置
        var geometry = this.mesh.geometry;
        var positionAttribute = geometry.getAttribute('position');
        // 更新顶点位置
        for (var i = 0; i < this.particles.length; i++) {
            var particle = this.particles[i];
            positionAttribute.setXYZ(i, particle.getPosition().x, particle.getPosition().y, particle.getPosition().z);
        }
        // 标记位置属性需要更新
        positionAttribute.needsUpdate = true;
        // 更新法线
        geometry.computeVertexNormals();
        // 应用内部压力
        this.applyBalloonPressure();
    };
    /**
     * 应用气球内部压力
     */
    SoftBodyComponent.prototype.applyBalloonPressure = function () {
        if (!this.params.pressure)
            return;
        // 计算气球中心
        var center = new CANNON.Vec3();
        for (var _i = 0, _a = this.particles; _i < _a.length; _i++) {
            var particle = _a[_i];
            center.vadd(particle.position, center);
        }
        center.scale(1 / this.particles.length, center);
        // 对每个粒子应用向外的力
        for (var _b = 0, _c = this.particles; _b < _c.length; _b++) {
            var particle = _c[_b];
            // 计算从中心到粒子的方向
            var direction = new CANNON.Vec3();
            direction.copy(particle.position);
            direction.vsub(center, direction);
            // 归一化方向
            var length_1 = direction.length();
            if (length_1 < 0.0001)
                continue;
            direction.scale(1 / length_1, direction);
            // 应用力
            var force = new CANNON.Vec3();
            force.copy(direction);
            force.scale(this.params.pressure, force);
            particle.applyForce(force, particle.position);
        }
    };
    /**
     * 更新果冻网格
     */
    SoftBodyComponent.prototype.updateJellyMesh = function () {
        if (!this.mesh)
            return;
        // 获取网格的顶点位置
        var geometry = this.mesh.geometry;
        var positionAttribute = geometry.getAttribute('position');
        // 更新顶点位置
        for (var i = 0; i < this.particles.length; i++) {
            var particle = this.particles[i];
            positionAttribute.setXYZ(i, particle.getPosition().x, particle.getPosition().y, particle.getPosition().z);
        }
        // 标记位置属性需要更新
        positionAttribute.needsUpdate = true;
        // 更新法线
        geometry.computeVertexNormals();
    };
    /**
     * 创建调试网格
     * @returns 调试网格
     */
    SoftBodyComponent.prototype.createDebugMesh = function () {
        if (!this.initialized)
            return null;
        var debugObject = new THREE.Object3D();
        // 创建粒子可视化
        var particleGeometry = new THREE.SphereGeometry(0.05, 8, 8);
        var particleMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
        for (var _i = 0, _a = this.particles; _i < _a.length; _i++) {
            var particle = _a[_i];
            var particleMesh = new THREE.Mesh(particleGeometry, particleMaterial);
            particleMesh.setPosition(particle.getPosition().x, particle.getPosition().y, particle.getPosition().z);
            debugObject.add(particleMesh);
        }
        // 创建约束可视化
        var constraintMaterial = new THREE.LineBasicMaterial({ color: 0x00ff00 });
        for (var _b = 0, _c = this.constraints; _b < _c.length; _b++) {
            var constraint = _c[_b];
            var particleA = this.particles[constraint.particleA];
            var particleB = this.particles[constraint.particleB];
            var points = [
                new THREE.Vector3(particleA.getPosition().x, particleA.getPosition().y, particleA.getPosition().z),
                new THREE.Vector3(particleB.getPosition().x, particleB.getPosition().y, particleB.getPosition().z)
            ];
            var geometry = new THREE.BufferGeometry().setFromPoints(points);
            var line = new THREE.Line(geometry, constraintMaterial);
            debugObject.add(line);
        }
        return debugObject;
    };
    /**
     * 是否已初始化
     * @returns 是否已初始化
     */
    SoftBodyComponent.prototype.isInitialized = function () {
        return this.initialized;
    };
    /**
     * 销毁软体
     */
    SoftBodyComponent.prototype.destroy = function () {
        if (!this.initialized || !this.world)
            return;
        // 移除所有约束
        for (var _i = 0, _a = this.physicsConstraints; _i < _a.length; _i++) {
            var constraint = _a[_i];
            this.world.removeConstraint(constraint);
        }
        // 移除所有粒子
        for (var _b = 0, _c = this.particles; _b < _c.length; _b++) {
            var particle = _c[_b];
            this.world.removeBody(particle);
        }
        this.physicsConstraints = [];
        this.particles = [];
        this.constraints = [];
        this.initialized = false;
    };
    /** 组件类型 */
    SoftBodyComponent.type = 'SoftBodyComponent';
    return SoftBodyComponent;
}(Component_1.Component));

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PointToPointConstraint = void 0;
/**
 * 点对点约束
 * 将两个物体通过一个点连接起来
 */
var THREE = require("three");
var CANNON = require("cannon-es");
var PhysicsConstraint_1 = require("./PhysicsConstraint");
/**
 * 点对点约束
 */
var PointToPointConstraint = exports.PointToPointConstraint = /** @class */ (function (_super) {
    __extends(PointToPointConstraint, _super);
    /**
     * 创建点对点约束
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    function PointToPointConstraint(targetEntity, options) {
        if (targetEntity === void 0) { targetEntity = null; }
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, PhysicsConstraint_1.ConstraintType.POINT_TO_POINT, targetEntity, options) || this;
        // 设置源物体上的连接点
        var pivotA = options.pivotA || new THREE.Vector3(0, 0, 0);
        _this.pivotA = new CANNON.Vec3(pivotA.x, pivotA.y, pivotA.z);
        // 设置目标物体上的连接点
        var pivotB = options.pivotB || new THREE.Vector3(0, 0, 0);
        _this.pivotB = new CANNON.Vec3(pivotB.x, pivotB.y, pivotB.z);
        // 设置最大力
        _this.maxForce = options.maxForce !== undefined ? options.maxForce : 1e6;
        return _this;
    }
    /**
     * 创建约束
     */
    PointToPointConstraint.prototype.createConstraint = function () {
        // 获取源物体和目标物体
        var bodyA = this.getSourceBody();
        var bodyB = this.getTargetBody();
        // 如果没有源物体或目标物体，则无法创建约束
        if (!bodyA || !bodyB) {
            console.warn('无法创建点对点约束：缺少源物体或目标物体');
            return;
        }
        // 创建点对点约束
        this.constraint = new CANNON.PointToPointConstraint(bodyA, this.pivotA, bodyB, this.pivotB, this.maxForce);
        // 设置是否允许连接的物体之间碰撞
        this.constraint.collideConnected = this.collideConnected;
    };
    /**
     * 设置源物体上的连接点
     * @param pivot 连接点（局部坐标）
     */
    PointToPointConstraint.prototype.setPivotA = function (pivot) {
        this.pivotA.set(pivot.x, pivot.y, pivot.z);
        // 如果约束已创建，更新约束
        if (this.constraint instanceof CANNON.PointToPointConstraint) {
            this.constraint.pivotA.copy(this.pivotA);
        }
    };
    /**
     * 获取源物体上的连接点
     * @returns 连接点（局部坐标）
     */
    PointToPointConstraint.prototype.getPivotA = function () {
        return new THREE.Vector3(this.pivotA.x, this.pivotA.y, this.pivotA.z);
    };
    /**
     * 设置目标物体上的连接点
     * @param pivot 连接点（局部坐标）
     */
    PointToPointConstraint.prototype.setPivotB = function (pivot) {
        this.pivotB.set(pivot.x, pivot.y, pivot.z);
        // 如果约束已创建，更新约束
        if (this.constraint instanceof CANNON.PointToPointConstraint) {
            this.constraint.pivotB.copy(this.pivotB);
        }
    };
    /**
     * 获取目标物体上的连接点
     * @returns 连接点（局部坐标）
     */
    PointToPointConstraint.prototype.getPivotB = function () {
        return new THREE.Vector3(this.pivotB.x, this.pivotB.y, this.pivotB.z);
    };
    /**
     * 设置最大力
     * @param maxForce 最大力
     */
    PointToPointConstraint.prototype.setMaxForce = function (maxForce) {
        this.maxForce = maxForce;
        // 如果约束已创建，更新约束
        if (this.constraint instanceof CANNON.PointToPointConstraint) {
            this.constraint.maxForce = maxForce;
        }
    };
    /**
     * 获取最大力
     * @returns 最大力
     */
    PointToPointConstraint.prototype.getMaxForce = function () {
        return this.maxForce;
    };
    /** 组件类型 */
    PointToPointConstraint.type = 'PointToPointConstraint';
    return PointToPointConstraint;
}(PhysicsConstraint_1.PhysicsConstraint));

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhysicsCollider = exports.ColliderType = void 0;
/**
 * 物理碰撞器组件
 * 为实体提供碰撞形状
 */
var THREE = require("three");
var CANNON = require("cannon-es");
var Component_1 = require("../core/Component");
var ColliderType;
(function (ColliderType) {
    ColliderType["BOX"] = "box";
    ColliderType["SPHERE"] = "sphere";
    ColliderType["CYLINDER"] = "cylinder";
    ColliderType["CAPSULE"] = "capsule";
    ColliderType["CONVEX"] = "convex";
    ColliderType["TRIMESH"] = "trimesh";
    ColliderType["PLANE"] = "plane";
    ColliderType["COMPOUND"] = "compound";
})(ColliderType || (exports.ColliderType = ColliderType = {}));
var PhysicsCollider = exports.PhysicsCollider = /** @class */ (function (_super) {
    __extends(PhysicsCollider, _super);
    /**
     * 创建物理碰撞器组件
     * @param options 碰撞器选项
     */
    function PhysicsCollider(options) {
        var _this = _super.call(this, PhysicsCollider.type) || this;
        /** 碰撞形状 */
        _this.shapes = [];
        /** 物理世界 */
        _this.world = null;
        /** 是否已初始化 */
        _this.initialized = false;
        _this.colliderType = options.type;
        _this.params = options.params || {};
        // 设置偏移
        var offset = options.offset || { x: 0, y: 0, z: 0 };
        _this.offset = new CANNON.Vec3(offset.x, offset.y, offset.z);
        // 设置旋转
        var rotation = options.rotation || { x: 0, y: 0, z: 0 };
        var quaternion = new THREE.Quaternion().setFromEuler(new THREE.Euler(rotation.x, rotation.y, rotation.z));
        _this.rotation = new CANNON.Quaternion(quaternion.x, quaternion.y, quaternion.z, quaternion.w);
        return _this;
    }
    /**
     * 初始化碰撞器
     * @param world 物理世界
     */
    PhysicsCollider.prototype.initialize = function (world) {
        if (this.initialized)
            return;
        this.world = world;
        // 创建碰撞形状
        this.createShape();
        this.initialized = true;
    };
    /**
     * 创建碰撞形状
     */
    PhysicsCollider.prototype.createShape = function () {
        var shape = null;
        switch (this.colliderType) {
            case ColliderType.BOX:
                shape = this.createBoxShape();
                break;
            case ColliderType.SPHERE:
                shape = this.createSphereShape();
                break;
            case ColliderType.CYLINDER:
                shape = this.createCylinderShape();
                break;
            case ColliderType.CAPSULE:
                shape = this.createCapsuleShape();
                break;
            case ColliderType.CONVEX:
                shape = this.createConvexShape();
                break;
            case ColliderType.TRIMESH:
                shape = this.createTrimeshShape();
                break;
            case ColliderType.PLANE:
                shape = this.createPlaneShape();
                break;
            case ColliderType.COMPOUND:
                this.createCompoundShape();
                return;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u78B0\u649E\u5668\u7C7B\u578B: ".concat(this.colliderType));
                return;
        }
        if (shape) {
            // 设置形状的位置和旋转（使用类型断言）
            shape.position = this.offset;
            shape.quaternion = this.rotation;
            this.shapes.push(shape);
        }
    };
    /**
     * 创建盒体碰撞形状
     * @returns 盒体碰撞形状
     */
    PhysicsCollider.prototype.createBoxShape = function () {
        var halfExtents = this.params.halfExtents || { x: 0.5, y: 0.5, z: 0.5 };
        return new CANNON.Box(new CANNON.Vec3(halfExtents.x, halfExtents.y, halfExtents.z));
    };
    /**
     * 创建球体碰撞形状
     * @returns 球体碰撞形状
     */
    PhysicsCollider.prototype.createSphereShape = function () {
        var radius = this.params.radius || 0.5;
        return new CANNON.Sphere(radius);
    };
    /**
     * 创建圆柱体碰撞形状
     * @returns 圆柱体碰撞形状
     */
    PhysicsCollider.prototype.createCylinderShape = function () {
        var radiusTop = this.params.radiusTop || 0.5;
        var radiusBottom = this.params.radiusBottom || 0.5;
        var height = this.params.height || 1;
        var numSegments = this.params.numSegments || 8;
        return new CANNON.Cylinder(radiusTop, radiusBottom, height, numSegments);
    };
    /**
     * 创建胶囊体碰撞形状
     * @returns 胶囊体碰撞形状（使用复合形状模拟）
     */
    PhysicsCollider.prototype.createCapsuleShape = function () {
        // CANNON.js没有内置的胶囊体，使用一个圆柱体和两个半球组成的复合形状来模拟
        var radius = this.params.radius || 0.5;
        var height = this.params.height || 1;
        var numSegments = this.params.numSegments || 8;
        // 创建圆柱体
        var cylinder = new CANNON.Cylinder(radius, radius, height, numSegments);
        // 创建两个半球
        var sphere1 = new CANNON.Sphere(radius);
        var sphere2 = new CANNON.Sphere(radius);
        // 创建复合形状（使用类型断言）
        var compound = new CANNON.Compound();
        // 添加圆柱体
        compound.addChild(cylinder, new CANNON.Vec3(0, 0, 0));
        // 添加上半球
        compound.addChild(sphere1, new CANNON.Vec3(0, height / 2, 0));
        // 添加下半球
        compound.addChild(sphere2, new CANNON.Vec3(0, -height / 2, 0));
        return compound;
    };
    /**
     * 创建凸包碰撞形状
     * @returns 凸包碰撞形状
     */
    PhysicsCollider.prototype.createConvexShape = function () {
        if (!this.params.vertices || !this.params.faces) {
            console.error('创建凸包碰撞形状需要提供顶点和面');
            // 返回默认盒体（使用类型断言）
            return new CANNON.Box(new CANNON.Vec3(0.5, 0.5, 0.5));
        }
        var vertices = this.params.vertices.map(function (v) { return new CANNON.Vec3(v.x, v.y, v.z); });
        var faces = this.params.faces;
        return new CANNON.ConvexPolyhedron({ vertices: vertices, faces: faces });
    };
    /**
     * 创建三角网格碰撞形状
     * @returns 三角网格碰撞形状
     */
    PhysicsCollider.prototype.createTrimeshShape = function () {
        if (!this.params.vertices || !this.params.indices) {
            console.error('创建三角网格碰撞形状需要提供顶点和索引');
            // 返回默认盒体（使用类型断言）
            return new CANNON.Box(new CANNON.Vec3(0.5, 0.5, 0.5));
        }
        var vertices = this.params.vertices;
        var indices = this.params.indices;
        // 将 TypedArray 转换为普通数组
        var verticesArray = Array.isArray(vertices) ? vertices : Array.from(vertices);
        var indicesArray = Array.isArray(indices) ? indices : Array.from(indices);
        return new CANNON.Trimesh(verticesArray, indicesArray);
    };
    /**
     * 创建平面碰撞形状
     * @returns 平面碰撞形状
     */
    PhysicsCollider.prototype.createPlaneShape = function () {
        return new CANNON.Plane();
    };
    /**
     * 创建复合碰撞形状
     */
    PhysicsCollider.prototype.createCompoundShape = function () {
        var _a;
        if (!this.params.shapes || !Array.isArray(this.params.shapes)) {
            console.error('创建复合碰撞形状需要提供形状数组');
            this.shapes.push(new CANNON.Box(new CANNON.Vec3(0.5, 0.5, 0.5))); // 添加默认盒体
            return;
        }
        for (var _i = 0, _b = this.params.shapes; _i < _b.length; _i++) {
            var shapeInfo = _b[_i];
            var collider = new PhysicsCollider(shapeInfo);
            collider.initialize(this.world);
            var shapes = collider.getShapes();
            (_a = this.shapes).push.apply(_a, shapes);
        }
    };
    /**
     * 从网格创建碰撞形状
     * @param mesh Three.js网格
     * @returns 碰撞形状
     */
    PhysicsCollider.createFromMesh = function (mesh) {
        if (!mesh.geometry) {
            console.error('网格没有几何体');
            return new PhysicsCollider({ type: ColliderType.BOX });
        }
        // 获取包围盒
        var boundingBox = new THREE.Box3().setFromObject(mesh);
        var size = new THREE.Vector3();
        boundingBox.getSize(size);
        // 根据几何体类型选择合适的碰撞器
        if (mesh.geometry instanceof THREE.BoxGeometry) {
            return new PhysicsCollider({
                type: ColliderType.BOX,
                params: {
                    halfExtents: { x: size.x / 2, y: size.y / 2, z: size.z / 2 }
                }
            });
        }
        else if (mesh.geometry instanceof THREE.SphereGeometry) {
            return new PhysicsCollider({
                type: ColliderType.SPHERE,
                params: {
                    radius: Math.max(size.x, size.y, size.z) / 2
                }
            });
        }
        else if (mesh.geometry instanceof THREE.CylinderGeometry) {
            return new PhysicsCollider({
                type: ColliderType.CYLINDER,
                params: {
                    radiusTop: size.x / 2,
                    radiusBottom: size.x / 2,
                    height: size.y
                }
            });
        }
        else {
            // 对于复杂几何体，使用凸包或三角网格
            // 这里简化处理，使用包围盒
            return new PhysicsCollider({
                type: ColliderType.BOX,
                params: {
                    halfExtents: { x: size.x / 2, y: size.y / 2, z: size.z / 2 }
                }
            });
        }
    };
    /**
     * 获取碰撞形状
     * @returns 碰撞形状数组
     */
    PhysicsCollider.prototype.getShapes = function () {
        return this.shapes;
    };
    /**
     * 获取碰撞器类型
     * @returns 碰撞器类型
     */
    PhysicsCollider.prototype.getColliderType = function () {
        return this.colliderType;
    };
    /**
     * 获取碰撞器参数
     * @returns 碰撞器参数
     */
    PhysicsCollider.prototype.getParams = function () {
        return this.params;
    };
    /**
     * 获取碰撞器位置偏移
     * @returns 碰撞器位置偏移
     */
    PhysicsCollider.prototype.getOffset = function () {
        return this.offset;
    };
    /**
     * 获取碰撞器旋转偏移
     * @returns 碰撞器旋转偏移
     */
    PhysicsCollider.prototype.getRotation = function () {
        return this.rotation;
    };
    /**
     * 销毁碰撞器
     */
    PhysicsCollider.prototype.dispose = function () {
        this.shapes = [];
        this.world = null;
        this.initialized = false;
        // 调用基类的dispose方法
        _super.prototype.dispose.call(this);
    };
    /** 组件类型 */
    PhysicsCollider.type = 'PhysicsCollider';
    return PhysicsCollider;
}(Component_1.Component));

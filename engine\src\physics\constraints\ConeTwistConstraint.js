"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConeTwistConstraint = void 0;
/**
 * 圆锥扭转约束
 * 限制两个物体之间的相对旋转，类似于万向节
 */
var THREE = require("three");
var CANNON = require("cannon-es");
var PhysicsConstraint_1 = require("./PhysicsConstraint");
/**
 * 圆锥扭转约束
 */
var ConeTwistConstraint = exports.ConeTwistConstraint = /** @class */ (function (_super) {
    __extends(ConeTwistConstraint, _super);
    /**
     * 创建圆锥扭转约束
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    function ConeTwistConstraint(targetEntity, options) {
        if (targetEntity === void 0) { targetEntity = null; }
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, PhysicsConstraint_1.ConstraintType.CONE_TWIST, targetEntity, options) || this;
        // 设置源物体上的连接点
        var pivotA = options.pivotA || new THREE.Vector3(0, 0, 0);
        _this.pivotA = new CANNON.Vec3(pivotA.x, pivotA.y, pivotA.z);
        // 设置目标物体上的连接点
        var pivotB = options.pivotB || new THREE.Vector3(0, 0, 0);
        _this.pivotB = new CANNON.Vec3(pivotB.x, pivotB.y, pivotB.z);
        // 设置源物体上的轴
        var axisA = options.axisA || new THREE.Vector3(1, 0, 0);
        _this.axisA = new CANNON.Vec3(axisA.x, axisA.y, axisA.z);
        _this.axisA.normalize();
        // 设置目标物体上的轴
        var axisB = options.axisB || new THREE.Vector3(1, 0, 0);
        _this.axisB = new CANNON.Vec3(axisB.x, axisB.y, axisB.z);
        _this.axisB.normalize();
        // 设置最大力
        _this.maxForce = options.maxForce !== undefined ? options.maxForce : 1e6;
        // 设置角度限制
        _this.angle = options.angle !== undefined ? options.angle : Math.PI / 4;
        _this.twistAngle = options.twistAngle !== undefined ? options.twistAngle : Math.PI / 4;
        return _this;
    }
    /**
     * 创建约束
     */
    ConeTwistConstraint.prototype.createConstraint = function () {
        // 获取源物体和目标物体
        var bodyA = this.getSourceBody();
        var bodyB = this.getTargetBody();
        // 如果没有源物体或目标物体，则无法创建约束
        if (!bodyA || !bodyB) {
            console.warn('无法创建圆锥扭转约束：缺少源物体或目标物体');
            return;
        }
        // CANNON.js没有内置的ConeTwistConstraint，使用PointToPointConstraint作为替代
        // 或者使用HingeConstraint来模拟
        this.constraint = new CANNON.PointToPointConstraint(bodyA, this.pivotA, bodyB, this.pivotB, this.maxForce);
        this.constraint.collideConnected = this.collideConnected;
    };
    /**
     * 设置源物体上的连接点
     * @param pivot 连接点（局部坐标）
     */
    ConeTwistConstraint.prototype.setPivotA = function (pivot) {
        this.pivotA.set(pivot.x, pivot.y, pivot.z);
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取源物体上的连接点
     * @returns 连接点（局部坐标）
     */
    ConeTwistConstraint.prototype.getPivotA = function () {
        return new THREE.Vector3(this.pivotA.x, this.pivotA.y, this.pivotA.z);
    };
    /**
     * 设置目标物体上的连接点
     * @param pivot 连接点（局部坐标）
     */
    ConeTwistConstraint.prototype.setPivotB = function (pivot) {
        this.pivotB.set(pivot.x, pivot.y, pivot.z);
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取目标物体上的连接点
     * @returns 连接点（局部坐标）
     */
    ConeTwistConstraint.prototype.getPivotB = function () {
        return new THREE.Vector3(this.pivotB.x, this.pivotB.y, this.pivotB.z);
    };
    /**
     * 设置源物体上的轴
     * @param axis 轴（局部坐标）
     */
    ConeTwistConstraint.prototype.setAxisA = function (axis) {
        this.axisA.set(axis.x, axis.y, axis.z);
        this.axisA.normalize();
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取源物体上的轴
     * @returns 轴（局部坐标）
     */
    ConeTwistConstraint.prototype.getAxisA = function () {
        return new THREE.Vector3(this.axisA.x, this.axisA.y, this.axisA.z);
    };
    /**
     * 设置目标物体上的轴
     * @param axis 轴（局部坐标）
     */
    ConeTwistConstraint.prototype.setAxisB = function (axis) {
        this.axisB.set(axis.x, axis.y, axis.z);
        this.axisB.normalize();
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取目标物体上的轴
     * @returns 轴（局部坐标）
     */
    ConeTwistConstraint.prototype.getAxisB = function () {
        return new THREE.Vector3(this.axisB.x, this.axisB.y, this.axisB.z);
    };
    /**
     * 设置圆锥角度
     * @param angle 角度（弧度）
     */
    ConeTwistConstraint.prototype.setAngle = function (angle) {
        this.angle = angle;
        // 注意：由于使用PointToPointConstraint替代，角度限制功能受限
    };
    /**
     * 获取圆锥角度
     * @returns 角度（弧度）
     */
    ConeTwistConstraint.prototype.getAngle = function () {
        return this.angle;
    };
    /**
     * 设置扭转角度
     * @param angle 角度（弧度）
     */
    ConeTwistConstraint.prototype.setTwistAngle = function (angle) {
        this.twistAngle = angle;
        // 注意：由于使用PointToPointConstraint替代，角度限制功能受限
    };
    /**
     * 获取扭转角度
     * @returns 角度（弧度）
     */
    ConeTwistConstraint.prototype.getTwistAngle = function () {
        return this.twistAngle;
    };
    /**
     * 设置最大力
     * @param maxForce 最大力
     */
    ConeTwistConstraint.prototype.setMaxForce = function (maxForce) {
        this.maxForce = maxForce;
        // 如果约束已创建，需要重新创建
        this.recreateConstraint();
    };
    /**
     * 获取最大力
     * @returns 最大力
     */
    ConeTwistConstraint.prototype.getMaxForce = function () {
        return this.maxForce;
    };
    /**
     * 重新创建约束
     */
    ConeTwistConstraint.prototype.recreateConstraint = function () {
        if (this.initialized && this.constraint && this.world) {
            this.world.removeConstraint(this.constraint);
            this.constraint = null;
            this.initialized = false;
            this.initialize(this.world);
        }
    };
    /** 组件类型 */
    ConeTwistConstraint.type = 'ConeTwistConstraint';
    return ConeTwistConstraint;
}(PhysicsConstraint_1.PhysicsConstraint));

/**
 * 视觉脚本软体物理节点
 * 提供软体物理系统相关的节点
 */
import type { Entity } from '../../core/Entity';
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';
import { Vector3 } from '../../math/Vector3';
import { Quaternion } from '../../math/Quaternion';
import { SoftBodySystem } from '../../physics/softbody/SoftBodySystem';
import { SoftBodyComponent, SoftBodyType } from '../../physics/softbody/SoftBodyComponent';

/**
 * 创建布料节点
 * 创建布料软体
 */
export class CreateClothNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'width',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '宽度',
      defaultValue: 1
    });

    this.addInput({
      name: 'height',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '高度',
      defaultValue: 1
    });

    this.addInput({
      name: 'segments',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '分段数',
      defaultValue: 10
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '位置',
      defaultValue: new Vector3(0, 0, 0)
    });

    this.addInput({
      name: 'fixedCorners',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否固定角落',
      defaultValue: true
    });

    this.addInput({
      name: 'mass',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '质量',
      defaultValue: 1
    });

    this.addInput({
      name: 'stiffness',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '刚度',
      defaultValue: 0.9
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '创建的实体'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const width = this.getInputValue('width') as number;
    const height = this.getInputValue('height') as number;
    const segments = this.getInputValue('segments') as number;
    const position = this.getInputValue('position') as Vector3;
    const fixedCorners = this.getInputValue('fixedCorners') as boolean;
    const mass = this.getInputValue('mass') as number;
    const stiffness = this.getInputValue('stiffness') as number;

    // 获取软体物理系统
    const softBodySystem = this.graph.getWorld().getSystem(SoftBodySystem);
    if (!softBodySystem) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 创建布料实体
    const entity = softBodySystem.createCloth({
      width,
      height,
      segments,
      position,
      fixedCorners,
      mass,
      stiffness
    });

    // 设置输出值
    this.setOutputValue('entity', entity);
    this.setOutputValue('success', !!entity);

    // 触发输出流程
    this.triggerFlow('flow');

    return !!entity;
  }
}

/**
 * 创建绳索节点
 * 创建绳索软体
 */
export class CreateRopeNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'start',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '起点',
      defaultValue: new Vector3(0, 1, 0)
    });

    this.addInput({
      name: 'end',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '终点',
      defaultValue: new Vector3(0, -1, 0)
    });

    this.addInput({
      name: 'segments',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '分段数',
      defaultValue: 10
    });

    this.addInput({
      name: 'fixedEnds',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否固定两端',
      defaultValue: true
    });

    this.addInput({
      name: 'mass',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '质量',
      defaultValue: 1
    });

    this.addInput({
      name: 'stiffness',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '刚度',
      defaultValue: 0.9
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '创建的实体'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const start = this.getInputValue('start') as Vector3;
    const end = this.getInputValue('end') as Vector3;
    const segments = this.getInputValue('segments') as number;
    const fixedEnds = this.getInputValue('fixedEnds') as boolean;
    const mass = this.getInputValue('mass') as number;
    const stiffness = this.getInputValue('stiffness') as number;

    // 获取软体物理系统
    const softBodySystem = this.graph.getWorld().getSystem(SoftBodySystem);
    if (!softBodySystem) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 创建绳索实体
    const entity = softBodySystem.createRope({
      start,
      end,
      segments,
      fixedEnds,
      mass,
      stiffness
    });

    // 设置输出值
    this.setOutputValue('entity', entity);
    this.setOutputValue('success', !!entity);

    // 触发输出流程
    this.triggerFlow('flow');

    return !!entity;
  }
}

/**
 * 创建气球节点
 * 创建气球软体
 */
export class CreateBalloonNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'radius',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '半径',
      defaultValue: 0.5
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '位置',
      defaultValue: new Vector3(0, 0, 0)
    });

    this.addInput({
      name: 'segments',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '分段数',
      defaultValue: 16
    });

    this.addInput({
      name: 'pressure',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '内部压力',
      defaultValue: 100
    });

    this.addInput({
      name: 'mass',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '质量',
      defaultValue: 1
    });

    this.addInput({
      name: 'stiffness',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '刚度',
      defaultValue: 0.9
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '创建的实体'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const radius = this.getInputValue('radius') as number;
    const position = this.getInputValue('position') as Vector3;
    const segments = this.getInputValue('segments') as number;
    const pressure = this.getInputValue('pressure') as number;
    const mass = this.getInputValue('mass') as number;
    const stiffness = this.getInputValue('stiffness') as number;

    // 获取软体物理系统
    const softBodySystem = this.graph.getWorld().getSystem(SoftBodySystem);
    if (!softBodySystem) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 创建气球实体
    const entity = softBodySystem.createBalloon({
      radius,
      position,
      segments,
      pressure,
      mass,
      stiffness
    });

    // 设置输出值
    this.setOutputValue('entity', entity);
    this.setOutputValue('success', !!entity);

    // 触发输出流程
    this.triggerFlow('flow');

    return !!entity;
  }
}

/**
 * 创建果冻节点
 * 创建果冻软体
 */
export class CreateJellyNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '尺寸',
      defaultValue: new Vector3(1, 1, 1)
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '位置',
      defaultValue: new Vector3(0, 0, 0)
    });

    this.addInput({
      name: 'resolution',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '分辨率',
      defaultValue: 8
    });

    this.addInput({
      name: 'mass',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '质量',
      defaultValue: 1
    });

    this.addInput({
      name: 'stiffness',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '刚度',
      defaultValue: 0.8
    });

    this.addInput({
      name: 'damping',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '阻尼',
      defaultValue: 0.3
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '创建的实体'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const size = this.getInputValue('size') as Vector3;
    const position = this.getInputValue('position') as Vector3;
    const resolution = this.getInputValue('resolution') as number;
    const mass = this.getInputValue('mass') as number;
    const stiffness = this.getInputValue('stiffness') as number;
    const damping = this.getInputValue('damping') as number;

    // 获取软体物理系统
    const softBodySystem = this.graph.getWorld().getSystem(SoftBodySystem);
    if (!softBodySystem) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 创建果冻实体
    const entity = softBodySystem.createJelly({
      size,
      position,
      resolution,
      mass,
      stiffness,
      damping
    });

    // 设置输出值
    this.setOutputValue('entity', entity);
    this.setOutputValue('success', !!entity);

    // 触发输出流程
    this.triggerFlow('flow');

    return !!entity;
  }
}

/**
 * 软体切割节点
 * 切割软体
 */
export class CutSoftBodyNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '软体实体'
    });

    this.addInput({
      name: 'cutPoint',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '切割点'
    });

    this.addInput({
      name: 'cutNormal',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '切割面法线'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });

    this.addOutput({
      name: 'newEntity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '切割后新创建的实体'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const cutPoint = this.getInputValue('cutPoint') as Vector3;
    const cutNormal = this.getInputValue('cutNormal') as Vector3;

    // 检查输入值是否有效
    if (!entity || !cutPoint || !cutNormal) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 获取软体物理系统
    const softBodySystem = this.graph.getWorld().getSystem(SoftBodySystem);
    if (!softBodySystem) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 切割软体
    const result = softBodySystem.cutSoftBody(entity, cutPoint, cutNormal);

    // 设置输出值
    this.setOutputValue('success', result.success);
    this.setOutputValue('newEntity', result.newEntity);

    // 触发输出流程
    this.triggerFlow('flow');

    return result.success;
  }
}

/**
 * 注册软体物理节点
 * @param registry 节点注册表
 */
export function registerSoftBodyNodes(registry: NodeRegistry): void {
  // 注册创建布料节点
  registry.registerNodeType({
    type: 'physics/softbody/createCloth',
    category: NodeCategory.PHYSICS,
    constructor: CreateClothNode,
    label: '创建布料',
    description: '创建布料软体',
    icon: 'cloth',
    color: '#9C27B0',
    tags: ['physics', 'softbody', 'cloth']
  });

  // 注册创建绳索节点
  registry.registerNodeType({
    type: 'physics/softbody/createRope',
    category: NodeCategory.PHYSICS,
    constructor: CreateRopeNode,
    label: '创建绳索',
    description: '创建绳索软体',
    icon: 'rope',
    color: '#9C27B0',
    tags: ['physics', 'softbody', 'rope']
  });

  // 注册创建气球节点
  registry.registerNodeType({
    type: 'physics/softbody/createBalloon',
    category: NodeCategory.PHYSICS,
    constructor: CreateBalloonNode,
    label: '创建气球',
    description: '创建气球软体',
    icon: 'balloon',
    color: '#9C27B0',
    tags: ['physics', 'softbody', 'balloon']
  });

  // 注册创建果冻节点
  registry.registerNodeType({
    type: 'physics/softbody/createJelly',
    category: NodeCategory.PHYSICS,
    constructor: CreateJellyNode,
    label: '创建果冻',
    description: '创建果冻软体',
    icon: 'jelly',
    color: '#9C27B0',
    tags: ['physics', 'softbody', 'jelly']
  });

  // 注册软体切割节点
  registry.registerNodeType({
    type: 'physics/softbody/cut',
    category: NodeCategory.PHYSICS,
    constructor: CutSoftBodyNode,
    label: '切割软体',
    description: '切割软体',
    icon: 'cut',
    color: '#9C27B0',
    tags: ['physics', 'softbody', 'cut']
  });
}

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DistanceConstraint = void 0;
var CANNON = require("cannon-es");
var PhysicsConstraint_1 = require("./PhysicsConstraint");
/**
 * 距离约束
 */
var DistanceConstraint = exports.DistanceConstraint = /** @class */ (function (_super) {
    __extends(DistanceConstraint, _super);
    /**
     * 创建距离约束
     * @param targetEntity 目标实体
     * @param options 约束选项
     */
    function DistanceConstraint(targetEntity, options) {
        if (targetEntity === void 0) { targetEntity = null; }
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, PhysicsConstraint_1.ConstraintType.DISTANCE, targetEntity, options) || this;
        // 设置距离
        _this.distance = options.distance !== undefined ? options.distance : 1;
        // 设置最大力
        _this.maxForce = options.maxForce !== undefined ? options.maxForce : 1e6;
        return _this;
    }
    /**
     * 创建约束
     */
    DistanceConstraint.prototype.createConstraint = function () {
        // 获取源物体和目标物体
        var bodyA = this.getSourceBody();
        var bodyB = this.getTargetBody();
        // 如果没有源物体或目标物体，则无法创建约束
        if (!bodyA || !bodyB) {
            console.warn('无法创建距离约束：缺少源物体或目标物体');
            return;
        }
        // 创建距离约束
        this.constraint = new CANNON.DistanceConstraint(bodyA, bodyB, this.distance, this.maxForce);
        // 设置是否允许连接的物体之间碰撞
        this.constraint.collideConnected = this.collideConnected;
    };
    /**
     * 设置距离
     * @param distance 距离
     */
    DistanceConstraint.prototype.setDistance = function (distance) {
        this.distance = distance;
        // 如果约束已创建，更新约束
        if (this.constraint instanceof CANNON.DistanceConstraint) {
            this.constraint.distance = distance;
        }
    };
    /**
     * 获取距离
     * @returns 距离
     */
    DistanceConstraint.prototype.getDistance = function () {
        return this.distance;
    };
    /**
     * 设置最大力
     * @param maxForce 最大力
     */
    DistanceConstraint.prototype.setMaxForce = function (maxForce) {
        this.maxForce = maxForce;
        // 如果约束已创建，更新约束
        if (this.constraint instanceof CANNON.DistanceConstraint) {
            this.constraint.equations[0].maxForce = maxForce;
            this.constraint.equations[0].minForce = -maxForce;
        }
    };
    /**
     * 获取最大力
     * @returns 最大力
     */
    DistanceConstraint.prototype.getMaxForce = function () {
        return this.maxForce;
    };
    /**
     * 获取当前距离
     * @returns 当前距离
     */
    DistanceConstraint.prototype.getCurrentDistance = function () {
        if (!this.initialized || !this.constraint)
            return this.distance;
        var bodyA = this.getSourceBody();
        var bodyB = this.getTargetBody();
        if (!bodyA || !bodyB)
            return this.distance;
        var posA = bodyA.position;
        var posB = bodyB.position;
        return Math.sqrt(Math.pow(posB.x - posA.x, 2) +
            Math.pow(posB.y - posA.y, 2) +
            Math.pow(posB.z - posA.z, 2));
    };
    /** 组件类型 */
    DistanceConstraint.type = 'DistanceConstraint';
    return DistanceConstraint;
}(PhysicsConstraint_1.PhysicsConstraint));

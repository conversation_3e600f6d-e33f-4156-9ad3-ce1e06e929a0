"use strict";
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Debug = exports.LogLevel = void 0;
/**
 * 调试工具类
 * 提供调试相关的功能
 */
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["NONE"] = 0] = "NONE";
    LogLevel[LogLevel["ERROR"] = 1] = "ERROR";
    LogLevel[LogLevel["WARN"] = 2] = "WARN";
    LogLevel[LogLevel["INFO"] = 3] = "INFO";
    LogLevel[LogLevel["DEBUG"] = 4] = "DEBUG";
    LogLevel[LogLevel["ALL"] = 5] = "ALL";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
var Debug = exports.Debug = /** @class */ (function () {
    function Debug() {
    }
    /**
     * 设置日志级别
     * @param level 日志级别
     */
    Debug.setLogLevel = function (level) {
        this.logLevel = level;
    };
    /**
     * 获取日志级别
     * @returns 日志级别
     */
    Debug.getLogLevel = function () {
        return this.logLevel;
    };
    /**
     * 设置调试模式
     * @param enabled 是否启用
     */
    Debug.setDebugMode = function (enabled) {
        this.debugMode = enabled;
    };
    /**
     * 是否处于调试模式
     * @returns 是否处于调试模式
     */
    Debug.isDebugMode = function () {
        return this.debugMode;
    };
    /**
     * 设置日志回调函数
     * @param callback 回调函数
     */
    Debug.setLogCallback = function (callback) {
        this.logCallback = callback;
    };
    /**
     * 清除日志回调函数
     */
    Debug.clearLogCallback = function () {
        this.logCallback = null;
    };
    /**
     * 记录调试日志
     * @param module 模块名或日志消息
     * @param message 日志消息（可选）
     * @param args 附加参数
     */
    Debug.log = function (module, message) {
        var args = [];
        for (var _i = 2; _i < arguments.length; _i++) {
            args[_i - 2] = arguments[_i];
        }
        if (this.logLevel >= LogLevel.DEBUG) {
            if (message !== undefined) {
                // 两个参数：模块名和消息
                console.log.apply(console, __spreadArray(["[DEBUG] [".concat(module, "] ").concat(message)], args, false));
                if (this.logCallback) {
                    this.logCallback.apply(this, __spreadArray([LogLevel.DEBUG, "[".concat(module, "] ").concat(message)], args, false));
                }
            }
            else {
                // 一个参数：只有消息
                console.log.apply(console, __spreadArray(["[DEBUG] ".concat(module)], args, false));
                if (this.logCallback) {
                    this.logCallback.apply(this, __spreadArray([LogLevel.DEBUG, module], args, false));
                }
            }
        }
    };
    /**
     * 记录信息日志
     * @param module 模块名或日志消息
     * @param message 日志消息（可选）
     * @param args 附加参数
     */
    Debug.info = function (module, message) {
        var args = [];
        for (var _i = 2; _i < arguments.length; _i++) {
            args[_i - 2] = arguments[_i];
        }
        if (this.logLevel >= LogLevel.INFO) {
            if (message !== undefined) {
                // 两个参数：模块名和消息
                console.info.apply(console, __spreadArray(["[INFO] [".concat(module, "] ").concat(message)], args, false));
                if (this.logCallback) {
                    this.logCallback.apply(this, __spreadArray([LogLevel.INFO, "[".concat(module, "] ").concat(message)], args, false));
                }
            }
            else {
                // 一个参数：只有消息
                console.info.apply(console, __spreadArray(["[INFO] ".concat(module)], args, false));
                if (this.logCallback) {
                    this.logCallback.apply(this, __spreadArray([LogLevel.INFO, module], args, false));
                }
            }
        }
    };
    /**
     * 记录警告日志
     * @param module 模块名或日志消息
     * @param message 日志消息（可选）
     * @param args 附加参数
     */
    Debug.warn = function (module, message) {
        var args = [];
        for (var _i = 2; _i < arguments.length; _i++) {
            args[_i - 2] = arguments[_i];
        }
        if (this.logLevel >= LogLevel.WARN) {
            if (message !== undefined) {
                // 两个参数：模块名和消息
                console.warn.apply(console, __spreadArray(["[WARN] [".concat(module, "] ").concat(message)], args, false));
                if (this.logCallback) {
                    this.logCallback.apply(this, __spreadArray([LogLevel.WARN, "[".concat(module, "] ").concat(message)], args, false));
                }
            }
            else {
                // 一个参数：只有消息
                console.warn.apply(console, __spreadArray(["[WARN] ".concat(module)], args, false));
                if (this.logCallback) {
                    this.logCallback.apply(this, __spreadArray([LogLevel.WARN, module], args, false));
                }
            }
        }
    };
    /**
     * 记录错误日志
     * @param module 模块名或日志消息
     * @param message 日志消息（可选）
     * @param args 附加参数
     */
    Debug.error = function (module, message) {
        var args = [];
        for (var _i = 2; _i < arguments.length; _i++) {
            args[_i - 2] = arguments[_i];
        }
        if (this.logLevel >= LogLevel.ERROR) {
            if (message !== undefined) {
                // 两个参数：模块名和消息
                console.error.apply(console, __spreadArray(["[ERROR] [".concat(module, "] ").concat(message)], args, false));
                if (this.logCallback) {
                    this.logCallback.apply(this, __spreadArray([LogLevel.ERROR, "[".concat(module, "] ").concat(message)], args, false));
                }
            }
            else {
                // 一个参数：只有消息
                console.error.apply(console, __spreadArray(["[ERROR] ".concat(module)], args, false));
                if (this.logCallback) {
                    this.logCallback.apply(this, __spreadArray([LogLevel.ERROR, module], args, false));
                }
            }
        }
    };
    /**
     * 断言
     * @param condition 条件
     * @param message 断言失败时的消息
     * @throws 如果条件为假，则抛出错误
     */
    Debug.assert = function (condition, message) {
        if (!condition) {
            var assertMessage = "\u65AD\u8A00\u5931\u8D25: ".concat(message);
            this.error(assertMessage);
            if (this.debugMode) {
                throw new Error(assertMessage);
            }
        }
    };
    /**
     * 开始计时
     * @param label 计时标签
     */
    Debug.time = function (label) {
        if (this.logLevel >= LogLevel.DEBUG) {
            console.time(label);
        }
    };
    /**
     * 结束计时并输出结果
     * @param label 计时标签
     */
    Debug.timeEnd = function (label) {
        if (this.logLevel >= LogLevel.DEBUG) {
            console.timeEnd(label);
        }
    };
    /**
     * 输出对象的详细信息
     * @param obj 要检查的对象
     * @param label 标签
     */
    Debug.inspect = function (obj, label) {
        if (label === void 0) { label = 'Object'; }
        if (this.logLevel >= LogLevel.DEBUG) {
            console.log("[INSPECT] ".concat(label, ":"), obj);
            if (typeof obj === 'object' && obj !== null) {
                console.dir(obj);
            }
        }
    };
    /** 日志级别 */
    Debug.logLevel = LogLevel.INFO;
    /** 是否启用调试模式 */
    Debug.debugMode = false;
    /** 日志回调函数 */
    Debug.logCallback = null;
    return Debug;
}());

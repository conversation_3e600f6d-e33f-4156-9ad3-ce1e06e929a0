"use strict";
/**
 * 输入绑定
 * 用于将输入映射绑定到输入动作
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompositeBindingType = exports.CompositeInputBinding = exports.InputBinding = void 0;
/**
 * 输入绑定
 */
var InputBinding = /** @class */ (function () {
    /**
     * 创建输入绑定
     * @param name 绑定名称
     * @param mappingName 映射名称
     */
    function InputBinding(name, mappingName) {
        this.name = name;
        this.mappingName = mappingName;
    }
    /**
     * 获取绑定名称
     * @returns 绑定名称
     */
    InputBinding.prototype.getName = function () {
        return this.name;
    };
    /**
     * 获取映射名称
     * @returns 映射名称
     */
    InputBinding.prototype.getMappingName = function () {
        return this.mappingName;
    };
    return InputBinding;
}());
exports.InputBinding = InputBinding;
/**
 * 组合输入绑定
 * 用于将多个输入映射绑定到一个输入动作
 */
var CompositeInputBinding = /** @class */ (function () {
    /**
     * 创建组合输入绑定
     * @param name 绑定名称
     * @param mappingNames 映射名称列表
     * @param compositeType 组合类型
     */
    function CompositeInputBinding(name, mappingNames, compositeType) {
        if (compositeType === void 0) { compositeType = CompositeBindingType.ANY; }
        this.name = name;
        this.mappingNames = mappingNames;
        this.compositeType = compositeType;
    }
    /**
     * 获取绑定名称
     * @returns 绑定名称
     */
    CompositeInputBinding.prototype.getName = function () {
        return this.name;
    };
    /**
     * 获取映射名称
     * @returns 映射名称
     */
    CompositeInputBinding.prototype.getMappingName = function () {
        return this.mappingNames[0];
    };
    /**
     * 获取所有映射名称
     * @returns 映射名称列表
     */
    CompositeInputBinding.prototype.getMappingNames = function () {
        return this.mappingNames;
    };
    /**
     * 获取组合类型
     * @returns 组合类型
     */
    CompositeInputBinding.prototype.getCompositeType = function () {
        return this.compositeType;
    };
    return CompositeInputBinding;
}());
exports.CompositeInputBinding = CompositeInputBinding;
/**
 * 组合绑定类型
 */
var CompositeBindingType;
(function (CompositeBindingType) {
    /** 任意一个映射满足条件即可 */
    CompositeBindingType["ANY"] = "any";
    /** 所有映射都必须满足条件 */
    CompositeBindingType["ALL"] = "all";
    /** 按顺序优先使用第一个满足条件的映射 */
    CompositeBindingType["PRIORITY"] = "priority";
})(CompositeBindingType || (exports.CompositeBindingType = CompositeBindingType = {}));

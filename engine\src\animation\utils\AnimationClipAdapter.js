"use strict";
/**
 * 动画片段适配器
 * 用于在自定义 AnimationClip 和 Three.js AnimationClip 之间进行转换
 */
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnimationClipAdapter = void 0;
var THREE = require("three");
var AnimationClip_1 = require("../AnimationClip");
var AnimationClipAdapter = /** @class */ (function () {
    function AnimationClipAdapter() {
    }
    /**
     * 将自定义 AnimationClip 转换为 Three.js AnimationClip
     */
    AnimationClipAdapter.toThreeClip = function (customClip) {
        // 如果已经是 Three.js 的 AnimationClip，直接返回
        if (customClip instanceof THREE.AnimationClip) {
            return customClip;
        }
        // 获取 Three.js 格式的动画片段
        var threeClip = customClip.toThreeAnimationClip();
        return threeClip;
    };
    /**
     * 将 Three.js AnimationClip 转换为自定义 AnimationClip
     */
    AnimationClipAdapter.fromThreeClip = function (threeClip) {
        // 使用静态方法从Three.js动画片段创建自定义AnimationClip
        return AnimationClip_1.AnimationClip.fromThreeAnimationClip(threeClip);
    };
    /**
     * 安全地将任意类型转换为 Three.js AnimationClip
     */
    AnimationClipAdapter.ensureThreeClip = function (clip) {
        if (!clip) {
            throw new Error('AnimationClip is null or undefined');
        }
        // 如果已经是 Three.js AnimationClip
        if (clip instanceof THREE.AnimationClip) {
            return clip;
        }
        // 如果是自定义 AnimationClip
        if (clip.toThreeAnimationClip && typeof clip.toThreeAnimationClip === 'function') {
            return clip.toThreeAnimationClip();
        }
        // 如果有必要的属性，尝试创建 Three.js AnimationClip
        if (clip.name && clip.duration && clip.tracks) {
            return new THREE.AnimationClip(clip.name, clip.duration, clip.tracks, clip.blendMode);
        }
        throw new Error('Cannot convert to THREE.AnimationClip: invalid format');
    };
    /**
     * 安全地将任意类型转换为自定义 AnimationClip
     */
    AnimationClipAdapter.ensureCustomClip = function (clip) {
        if (!clip) {
            throw new Error('AnimationClip is null or undefined');
        }
        // 如果已经是自定义 AnimationClip
        if (clip instanceof AnimationClip_1.AnimationClip) {
            return clip;
        }
        // 如果是 Three.js AnimationClip
        if (clip instanceof THREE.AnimationClip) {
            return this.fromThreeClip(clip);
        }
        // 尝试从对象创建
        if (clip.name && clip.duration) {
            return new AnimationClip_1.AnimationClip({
                name: clip.name,
                duration: clip.duration,
                tracks: clip.tracks || []
            });
        }
        throw new Error('Cannot convert to custom AnimationClip: invalid format');
    };
    /**
     * 批量转换为 Three.js AnimationClip
     */
    AnimationClipAdapter.toThreeClips = function (clips) {
        var _this = this;
        return clips.map(function (clip) { return _this.ensureThreeClip(clip); });
    };
    /**
     * 批量转换为自定义 AnimationClip
     */
    AnimationClipAdapter.toCustomClips = function (clips) {
        var _this = this;
        return clips.map(function (clip) { return _this.ensureCustomClip(clip); });
    };
    /**
     * 检查是否为有效的动画片段
     */
    AnimationClipAdapter.isValidClip = function (clip) {
        if (!clip)
            return false;
        // 检查基本属性
        if (!clip.name || typeof clip.name !== 'string')
            return false;
        if (typeof clip.duration !== 'number' || clip.duration < 0)
            return false;
        // 检查轨道
        if (!Array.isArray(clip.tracks))
            return false;
        return true;
    };
    /**
     * 复制动画片段
     */
    AnimationClipAdapter.cloneClip = function (clip) {
        if (clip instanceof THREE.AnimationClip) {
            return clip.clone();
        }
        if (clip instanceof AnimationClip_1.AnimationClip) {
            // 创建一个新的AnimationClip实例作为克隆
            return new AnimationClip_1.AnimationClip({
                name: clip.name,
                duration: clip.duration,
                tracks: clip.tracks ? __spreadArray([], clip.tracks, true) : []
            });
        }
        // 通用复制
        return JSON.parse(JSON.stringify(clip));
    };
    return AnimationClipAdapter;
}());
exports.AnimationClipAdapter = AnimationClipAdapter;

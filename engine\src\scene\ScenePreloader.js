"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScenePreloader = exports.SceneResourceType = void 0;
/**
 * 场景预加载器
 * 负责场景资源的预加载和管理
 */
var AssetManager_1 = require("../assets/AssetManager");
var ResourcePreloader_1 = require("../assets/ResourcePreloader");
var EventEmitter_1 = require("../utils/EventEmitter");
var THREE = require("three");
/**
 * 场景资源类型
 */
var SceneResourceType;
(function (SceneResourceType) {
    /** 纹理 */
    SceneResourceType["TEXTURE"] = "texture";
    /** 模型 */
    SceneResourceType["MODEL"] = "model";
    /** 音频 */
    SceneResourceType["AUDIO"] = "audio";
    /** 字体 */
    SceneResourceType["FONT"] = "font";
    /** 着色器 */
    SceneResourceType["SHADER"] = "shader";
    /** 材质 */
    SceneResourceType["MATERIAL"] = "material";
    /** 其他 */
    SceneResourceType["OTHER"] = "other";
})(SceneResourceType || (exports.SceneResourceType = SceneResourceType = {}));
/**
 * 场景预加载器
 */
var ScenePreloader = /** @class */ (function (_super) {
    __extends(ScenePreloader, _super);
    /**
     * 创建场景预加载器实例
     * @param options 预加载器选项
     */
    function ScenePreloader(options) {
        var _this = _super.call(this) || this;
        /** 场景资源映射（场景ID -> 资源信息数组） */
        _this.sceneResources = new Map();
        /** 是否已初始化 */
        _this.initialized = false;
        _this.assetManager = options.assetManager;
        _this.autoAnalyzeResources = options.autoAnalyzeResources !== undefined ? options.autoAnalyzeResources : true;
        _this.autoRegisterResources = options.autoRegisterResources !== undefined ? options.autoRegisterResources : true;
        _this.debug = options.debug !== undefined ? options.debug : false;
        // 创建资源预加载器
        _this.resourcePreloader = new ResourcePreloader_1.ResourcePreloader({
            assetManager: _this.assetManager,
            autoRegisterAssets: false,
            autoLoadDependencies: true,
            maxConcurrentLoads: options.maxConcurrentLoads || 4,
            retryCount: 2
        });
        return _this;
    }
    /**
     * 初始化预加载器
     */
    ScenePreloader.prototype.initialize = function () {
        var _this = this;
        if (this.initialized) {
            return;
        }
        // 监听资源预加载器事件
        this.resourcePreloader.on('loadStart', function (data) {
            _this.emit('loadStart', data);
        });
        this.resourcePreloader.on('loadProgress', function (data) {
            _this.emit('loadProgress', data);
        });
        this.resourcePreloader.on('loadComplete', function (data) {
            _this.emit('loadComplete', data);
        });
        this.resourcePreloader.on('loadError', function (data) {
            _this.emit('loadError', data);
        });
        this.initialized = true;
        this.emit('initialized');
    };
    /**
     * 分析场景资源
     * @param scene 场景实例
     * @returns 场景资源信息数组
     */
    ScenePreloader.prototype.analyzeSceneResources = function (scene) {
        if (!scene.id) {
            throw new Error('场景没有ID');
        }
        if (this.debug) {
            console.log("[ScenePreloader] \u5206\u6790\u573A\u666F\u8D44\u6E90: ".concat(scene.name, " (").concat(scene.id, ")"));
        }
        // 获取场景中的所有实体
        var entities = scene.getEntities();
        // 收集所有资源
        var resources = [];
        // 分析场景天空盒资源
        var skybox = scene.getSkybox();
        if (skybox) {
            var skyboxResources = this.analyzeSkyboxResources(skybox);
            resources.push.apply(resources, skyboxResources);
        }
        // 分析实体资源
        for (var _i = 0, entities_1 = entities; _i < entities_1.length; _i++) {
            var entity = entities_1[_i];
            var entityResources = this.analyzeEntityResources(entity);
            resources.push.apply(resources, entityResources);
        }
        // 存储场景资源
        this.sceneResources.set(scene.id, resources);
        // 发出资源分析完成事件
        this.emit('resourcesAnalyzed', { sceneId: scene.id, resources: resources });
        return resources;
    };
    /**
     * 分析天空盒资源
     * @param skybox 天空盒实例
     * @returns 资源信息数组
     */
    ScenePreloader.prototype.analyzeSkyboxResources = function (skybox) {
        var resources = [];
        // 获取天空盒类型
        var skyboxType = skybox.getType();
        if (skyboxType === 'cubemap') {
            // 获取立方体贴图
            var cubeTexture = skybox.getCubeTexture();
            if (cubeTexture && cubeTexture.images) {
                // 添加立方体贴图资源
                for (var i = 0; i < cubeTexture.images.length; i++) {
                    var image = cubeTexture.images[i];
                    if (image && image.src) {
                        resources.push({
                            id: "skybox_cubemap_".concat(i),
                            type: SceneResourceType.TEXTURE,
                            url: image.src,
                            priority: 90,
                            required: true
                        });
                    }
                }
            }
        }
        else if (skyboxType === 'equirectangular') {
            // 获取全景贴图
            var texture = skybox.getTexture();
            if (texture && texture.image && texture.image.src) {
                resources.push({
                    id: 'skybox_equirectangular',
                    type: SceneResourceType.TEXTURE,
                    url: texture.image.src,
                    priority: 90,
                    required: true
                });
            }
        }
        return resources;
    };
    /**
     * 分析实体资源
     * @param entity 实体实例
     * @returns 资源信息数组
     */
    ScenePreloader.prototype.analyzeEntityResources = function (entity) {
        var resources = [];
        // 获取实体的所有组件
        var components = entity.getAllComponents();
        // 分析每个组件的资源
        for (var _i = 0, components_1 = components; _i < components_1.length; _i++) {
            var component = components_1[_i];
            var componentType = component.getType();
            // 根据组件类型分析资源
            switch (componentType) {
                case 'MeshComponent':
                    this.analyzeMeshComponentResources(entity, component, resources);
                    break;
                case 'AudioComponent':
                    this.analyzeAudioComponentResources(entity, component, resources);
                    break;
                case 'LightComponent':
                    this.analyzeLightComponentResources(entity, component, resources);
                    break;
                // 可以添加更多组件类型的分析
            }
        }
        // 递归分析子实体
        var children = entity.getChildren();
        for (var _a = 0, children_1 = children; _a < children_1.length; _a++) {
            var child = children_1[_a];
            var childResources = this.analyzeEntityResources(child);
            resources.push.apply(resources, childResources);
        }
        return resources;
    };
    /**
     * 分析网格组件资源
     * @param entity 实体实例
     * @param component 组件实例
     * @param resources 资源信息数组
     */
    ScenePreloader.prototype.analyzeMeshComponentResources = function (entity, component, resources) {
        // 获取网格
        var mesh = component.getMesh();
        if (!mesh) {
            return;
        }
        // 分析材质
        if (mesh.material) {
            this.analyzeMaterialResources(entity, mesh.material, resources);
        }
        // 如果是模型，可能需要分析几何体
        // 这里简化处理，实际应用中可能需要更复杂的分析
    };
    /**
     * 分析材质资源
     * @param entity 实体实例
     * @param material 材质实例
     * @param resources 资源信息数组
     */
    ScenePreloader.prototype.analyzeMaterialResources = function (entity, material, resources) {
        // 处理材质数组
        if (Array.isArray(material)) {
            for (var _i = 0, material_1 = material; _i < material_1.length; _i++) {
                var mat = material_1[_i];
                this.analyzeMaterialResources(entity, mat, resources);
            }
            return;
        }
        // 处理标准材质
        if (material instanceof THREE.MeshStandardMaterial) {
            // 分析贴图
            this.analyzeTextureResource(entity, 'map', material.map, resources);
            this.analyzeTextureResource(entity, 'normalMap', material.normalMap, resources);
            this.analyzeTextureResource(entity, 'roughnessMap', material.roughnessMap, resources);
            this.analyzeTextureResource(entity, 'metalnessMap', material.metalnessMap, resources);
            this.analyzeTextureResource(entity, 'aoMap', material.aoMap, resources);
            this.analyzeTextureResource(entity, 'emissiveMap', material.emissiveMap, resources);
            this.analyzeTextureResource(entity, 'displacementMap', material.displacementMap, resources);
        }
        // 可以添加更多材质类型的分析
    };
    /**
     * 分析贴图资源
     * @param entity 实体实例
     * @param mapType 贴图类型
     * @param texture 贴图实例
     * @param resources 资源信息数组
     */
    ScenePreloader.prototype.analyzeTextureResource = function (entity, mapType, texture, resources) {
        if (!texture || !texture.image || !texture.image.src) {
            return;
        }
        // 创建资源ID
        var resourceId = "texture_".concat(entity.id, "_").concat(mapType, "_").concat(texture.uuid);
        // 添加贴图资源
        resources.push({
            id: resourceId,
            type: SceneResourceType.TEXTURE,
            url: texture.image.src,
            priority: mapType === 'map' ? 80 : 70,
            entityId: entity.id,
            componentType: 'MeshComponent',
            required: mapType === 'map' // 漫反射贴图是必需的
        });
    };
    /**
     * 分析音频组件资源
     * @param entity 实体实例
     * @param component 组件实例
     * @param resources 资源信息数组
     */
    ScenePreloader.prototype.analyzeAudioComponentResources = function (entity, component, resources) {
        // 获取音频URL
        var audioUrl = component.getAudioUrl();
        if (!audioUrl) {
            return;
        }
        // 创建资源ID
        var resourceId = "audio_".concat(entity.id);
        // 添加音频资源
        resources.push({
            id: resourceId,
            type: SceneResourceType.AUDIO,
            url: audioUrl,
            priority: 60,
            entityId: entity.id,
            componentType: 'AudioComponent',
            required: false // 音频通常不是必需的
        });
    };
    /**
     * 分析光照组件资源
     * @param entity 实体实例
     * @param component 组件实例
     * @param resources 资源信息数组
     */
    ScenePreloader.prototype.analyzeLightComponentResources = function (entity, component, resources) {
        // 获取光照类型
        var lightType = component.getLightType();
        // 目前只有IES光照需要加载外部资源
        if (lightType === 'IES') {
            // 获取IES文件URL
            var iesUrl = component.getIESUrl();
            if (!iesUrl) {
                return;
            }
            // 创建资源ID
            var resourceId = "ies_".concat(entity.id);
            // 添加IES资源
            resources.push({
                id: resourceId,
                type: SceneResourceType.OTHER,
                url: iesUrl,
                priority: 50,
                entityId: entity.id,
                componentType: 'LightComponent',
                required: false // IES通常不是必需的
            });
        }
    };
    /**
     * 注册场景资源
     * @param sceneId 场景ID
     * @returns 是否成功注册
     */
    ScenePreloader.prototype.registerSceneResources = function (sceneId) {
        // 检查场景资源是否存在
        if (!this.sceneResources.has(sceneId)) {
            return false;
        }
        var resources = this.sceneResources.get(sceneId);
        if (this.debug) {
            console.log("[ScenePreloader] \u6CE8\u518C\u573A\u666F\u8D44\u6E90: ".concat(sceneId, ", \u8D44\u6E90\u6570: ").concat(resources.length));
        }
        // 注册每个资源
        for (var _i = 0, resources_1 = resources; _i < resources_1.length; _i++) {
            var resource = resources_1[_i];
            this.registerResource(resource);
        }
        // 创建预加载组
        this.createPreloadGroup(sceneId, resources);
        return true;
    };
    /**
     * 注册资源
     * @param resource 资源信息
     * @returns 是否成功注册
     */
    ScenePreloader.prototype.registerResource = function (resource) {
        // 将场景资源类型转换为资产类型
        var assetType = this.convertResourceTypeToAssetType(resource.type);
        // 注册资产
        try {
            this.assetManager.registerAsset(resource.id, resource.id, assetType, resource.url, {
                entityId: resource.entityId,
                componentType: resource.componentType,
                required: resource.required,
                priority: resource.priority
            });
            return true;
        }
        catch (error) {
            console.error("\u6CE8\u518C\u8D44\u6E90\u5931\u8D25: ".concat(resource.id), error);
            return false;
        }
    };
    /**
     * 将场景资源类型转换为资产类型
     * @param resourceType 场景资源类型
     * @returns 资产类型
     */
    ScenePreloader.prototype.convertResourceTypeToAssetType = function (resourceType) {
        switch (resourceType) {
            case SceneResourceType.TEXTURE:
                return AssetManager_1.AssetType.TEXTURE;
            case SceneResourceType.MODEL:
                return AssetManager_1.AssetType.MODEL;
            case SceneResourceType.AUDIO:
                return AssetManager_1.AssetType.AUDIO;
            case SceneResourceType.FONT:
                return AssetManager_1.AssetType.FONT;
            case SceneResourceType.SHADER:
                return AssetManager_1.AssetType.TEXT;
            case SceneResourceType.MATERIAL:
                return AssetManager_1.AssetType.JSON;
            default:
                return AssetManager_1.AssetType.BINARY;
        }
    };
    /**
     * 创建预加载组
     * @param sceneId 场景ID
     * @param resources 资源信息数组
     */
    ScenePreloader.prototype.createPreloadGroup = function (sceneId, resources) {
        var _this = this;
        // 将资源分为必需和非必需两组
        var requiredResources = resources.filter(function (resource) { return resource.required; });
        var optionalResources = resources.filter(function (resource) { return !resource.required; });
        // 创建必需资源组
        var requiredGroupInfo = {
            name: "".concat(sceneId, "_required"),
            priority: 100,
            resources: requiredResources.map(function (resource) { return ({
                id: resource.id,
                type: _this.convertResourceTypeToAssetType(resource.type),
                url: resource.url,
                priority: resource.priority || 0
            }); })
        };
        // 创建非必需资源组
        var optionalGroupInfo = {
            name: "".concat(sceneId, "_optional"),
            priority: 50,
            dependencies: ["".concat(sceneId, "_required")],
            resources: optionalResources.map(function (resource) { return ({
                id: resource.id,
                type: _this.convertResourceTypeToAssetType(resource.type),
                url: resource.url,
                priority: resource.priority || 0
            }); })
        };
        // 添加预加载组
        if (requiredGroupInfo.resources.length > 0) {
            this.resourcePreloader.addGroup(requiredGroupInfo);
        }
        if (optionalGroupInfo.resources.length > 0) {
            this.resourcePreloader.addGroup(optionalGroupInfo);
        }
    };
    /**
     * 加载资源
     * @param resourceIds 资源ID数组
     * @param options 加载选项
     */
    ScenePreloader.prototype.loadResources = function (resourceIds, options) {
        var _this = this;
        if (options === void 0) { options = {}; }
        if (this.debug) {
            console.log("[ScenePreloader] \u52A0\u8F7D\u8D44\u6E90: ".concat(resourceIds.length, " \u4E2A\u8D44\u6E90"));
        }
        // 创建加载进度信息
        var progressInfo = {
            total: resourceIds.length,
            loaded: 0,
            failed: 0,
            progress: 0,
            resources: []
        };
        // 如果没有资源需要加载，直接返回
        if (resourceIds.length === 0) {
            if (options.onComplete) {
                options.onComplete(progressInfo);
            }
            return;
        }
        // 发出加载开始事件
        this.emit('loadStart', progressInfo);
        // 调用进度回调
        if (options.onProgress) {
            options.onProgress(progressInfo);
        }
        // 设置最大并发加载数
        var maxConcurrentLoads = options.maxConcurrentLoads || 5;
        // 创建加载队列
        var queue = __spreadArray([], resourceIds, true);
        // 当前正在加载的资源数
        var activeLoads = 0;
        // 处理下一个资源
        var processNext = function () {
            // 如果队列为空或已达到最大并发加载数，则返回
            if (queue.length === 0 || activeLoads >= maxConcurrentLoads) {
                return;
            }
            // 获取下一个资源ID
            var resourceId = queue.shift();
            // 增加活动加载数
            activeLoads++;
            // 获取资源信息
            var resource = _this.assetManager.getAssetInfo(resourceId);
            if (!resource) {
                console.warn("\u627E\u4E0D\u5230\u8D44\u6E90: ".concat(resourceId));
                // 更新进度信息
                progressInfo.failed++;
                progressInfo.progress = progressInfo.loaded / progressInfo.total;
                // 减少活动加载数
                activeLoads--;
                // 处理下一个资源
                processNext();
                return;
            }
            // 应用优化选项
            var loadOptions = {
                priority: options.priority || resource.priority || 1
            };
            // 应用LOD
            if (options.useLOD && options.lodLevel !== undefined) {
                loadOptions.lodLevel = options.lodLevel;
            }
            // 应用纹理压缩
            if (options.useTextureCompression) {
                loadOptions.useTextureCompression = true;
            }
            // 应用几何体简化
            if (options.useGeometrySimplification) {
                loadOptions.useGeometrySimplification = true;
            }
            // 应用实例化
            if (options.useInstancing) {
                loadOptions.useInstancing = true;
            }
            // 加载资源
            _this.assetManager.loadAsset(resource.url, resource.type, loadOptions)
                .then(function () {
                // 更新进度信息
                progressInfo.loaded++;
                progressInfo.progress = progressInfo.loaded / progressInfo.total;
                progressInfo.resources.push({
                    id: resourceId,
                    url: resource.url,
                    type: resource.type,
                    status: 'loaded'
                });
                // 发出加载进度事件
                _this.emit('loadProgress', progressInfo);
                // 调用进度回调
                if (options.onProgress) {
                    options.onProgress(progressInfo);
                }
                // 减少活动加载数
                activeLoads--;
                // 处理下一个资源
                processNext();
                // 检查是否所有资源都已加载
                if (progressInfo.loaded + progressInfo.failed === progressInfo.total) {
                    // 发出加载完成事件
                    _this.emit('loadComplete', progressInfo);
                    // 调用完成回调
                    if (options.onComplete) {
                        options.onComplete(progressInfo);
                    }
                }
            })
                .catch(function (error) {
                console.error("\u52A0\u8F7D\u8D44\u6E90\u5931\u8D25: ".concat(resourceId), error);
                // 更新进度信息
                progressInfo.failed++;
                progressInfo.progress = progressInfo.loaded / progressInfo.total;
                progressInfo.resources.push({
                    id: resourceId,
                    url: resource.url,
                    type: resource.type,
                    status: 'failed',
                    error: error
                });
                // 发出加载错误事件
                _this.emit('loadError', {
                    id: resourceId,
                    url: resource.url,
                    type: resource.type,
                    error: error
                });
                // 调用错误回调
                if (options.onError) {
                    options.onError(error);
                }
                // 减少活动加载数
                activeLoads--;
                // 处理下一个资源
                processNext();
                // 检查是否所有资源都已加载
                if (progressInfo.loaded + progressInfo.failed === progressInfo.total) {
                    // 发出加载完成事件
                    _this.emit('loadComplete', progressInfo);
                    // 调用完成回调
                    if (options.onComplete) {
                        options.onComplete(progressInfo);
                    }
                }
            });
            // 处理下一个资源
            processNext();
        };
        // 开始处理队列
        for (var i = 0; i < Math.min(maxConcurrentLoads, resourceIds.length); i++) {
            processNext();
        }
    };
    /**
     * 预加载场景资源
     * @param sceneId 场景ID
     * @param onProgress 进度回调
     * @returns Promise，解析为加载结果
     */
    ScenePreloader.prototype.preloadSceneResources = function (sceneId, onProgress) {
        return __awaiter(this, void 0, void 0, function () {
            var progressInfo, resources, _i, resources_2, resource, requiredResult_1, error_1, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // 检查场景资源是否存在
                        if (!this.sceneResources.has(sceneId)) {
                            throw new Error("\u627E\u4E0D\u5230\u573A\u666F\u8D44\u6E90: ".concat(sceneId));
                        }
                        if (this.debug) {
                            console.log("[ScenePreloader] \u9884\u52A0\u8F7D\u573A\u666F\u8D44\u6E90: ".concat(sceneId));
                        }
                        progressInfo = {
                            sceneId: sceneId,
                            loaded: 0,
                            total: 0,
                            progress: 0,
                            loadedResources: [],
                            failedResources: [],
                            loadedBytes: 0,
                            totalBytes: 0
                        };
                        resources = this.sceneResources.get(sceneId);
                        progressInfo.total = resources.length;
                        // 计算总字节数
                        for (_i = 0, resources_2 = resources; _i < resources_2.length; _i++) {
                            resource = resources_2[_i];
                            progressInfo.totalBytes += resource.size || 0;
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 7, , 8]);
                        return [4 /*yield*/, this.resourcePreloader.loadGroup("".concat(sceneId, "_required"), function (groupProgress) {
                                // 更新进度信息
                                progressInfo.loaded = groupProgress.loaded;
                                progressInfo.loadedResources = groupProgress.loadedResources;
                                progressInfo.failedResources = groupProgress.failedResources;
                                progressInfo.currentResource = groupProgress.currentResource;
                                progressInfo.progress = groupProgress.progress;
                                // 调用进度回调
                                if (onProgress) {
                                    onProgress(progressInfo);
                                }
                            })];
                    case 2:
                        requiredResult_1 = _a.sent();
                        _a.label = 3;
                    case 3:
                        _a.trys.push([3, 5, , 6]);
                        return [4 /*yield*/, this.resourcePreloader.loadGroup("".concat(sceneId, "_optional"), function (groupProgress) {
                                // 更新进度信息
                                progressInfo.loaded = requiredResult_1.loaded + groupProgress.loaded;
                                progressInfo.loadedResources = __spreadArray(__spreadArray([], requiredResult_1.loadedResources, true), groupProgress.loadedResources, true);
                                progressInfo.failedResources = __spreadArray(__spreadArray([], requiredResult_1.failedResources, true), groupProgress.failedResources, true);
                                progressInfo.currentResource = groupProgress.currentResource;
                                progressInfo.progress = (progressInfo.loaded / progressInfo.total) || 0;
                                // 调用进度回调
                                if (onProgress) {
                                    onProgress(progressInfo);
                                }
                            })];
                    case 4:
                        _a.sent();
                        return [3 /*break*/, 6];
                    case 5:
                        error_1 = _a.sent();
                        console.warn("\u52A0\u8F7D\u975E\u5FC5\u9700\u8D44\u6E90\u5931\u8D25: ".concat(sceneId), error_1);
                        return [3 /*break*/, 6];
                    case 6: return [2 /*return*/, progressInfo];
                    case 7:
                        error_2 = _a.sent();
                        throw new Error("\u52A0\u8F7D\u5FC5\u9700\u8D44\u6E90\u5931\u8D25: ".concat(sceneId, ", ").concat(error_2.message));
                    case 8: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 预加载场景
     * @param scene 场景实例
     * @param onProgress 进度回调
     * @returns Promise，解析为加载结果
     */
    ScenePreloader.prototype.preloadScene = function (scene, onProgress) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (!scene.id) {
                    throw new Error('场景没有ID');
                }
                // 如果启用自动分析资源，则分析场景资源
                if (this.autoAnalyzeResources) {
                    this.analyzeSceneResources(scene);
                }
                // 如果启用自动注册资源，则注册场景资源
                if (this.autoRegisterResources) {
                    this.registerSceneResources(scene.id);
                }
                // 预加载场景资源
                return [2 /*return*/, this.preloadSceneResources(scene.id, onProgress)];
            });
        });
    };
    /**
     * 卸载场景资源
     * @param sceneId 场景ID
     * @returns 是否成功卸载
     */
    ScenePreloader.prototype.unloadSceneResources = function (sceneId) {
        // 检查场景资源是否存在
        if (!this.sceneResources.has(sceneId)) {
            return false;
        }
        if (this.debug) {
            console.log("[ScenePreloader] \u5378\u8F7D\u573A\u666F\u8D44\u6E90: ".concat(sceneId));
        }
        // 获取场景资源
        var resources = this.sceneResources.get(sceneId);
        // 卸载每个资源
        for (var _i = 0, resources_3 = resources; _i < resources_3.length; _i++) {
            var resource = resources_3[_i];
            this.assetManager.unloadAsset(resource.id);
        }
        return true;
    };
    /**
     * 获取场景资源
     * @param sceneId 场景ID
     * @returns 场景资源信息数组
     */
    ScenePreloader.prototype.getSceneResources = function (sceneId) {
        return this.sceneResources.get(sceneId) || [];
    };
    /**
     * 清除场景资源
     * @param sceneId 场景ID
     * @returns 是否成功清除
     */
    ScenePreloader.prototype.clearSceneResources = function (sceneId) {
        // 检查场景资源是否存在
        if (!this.sceneResources.has(sceneId)) {
            return false;
        }
        // 卸载场景资源
        this.unloadSceneResources(sceneId);
        // 移除场景资源
        this.sceneResources.delete(sceneId);
        return true;
    };
    /**
     * 销毁预加载器
     */
    ScenePreloader.prototype.dispose = function () {
        // 清空所有场景资源
        for (var _i = 0, _a = this.sceneResources.keys(); _i < _a.length; _i++) {
            var sceneId = _a[_i];
            this.clearSceneResources(sceneId);
        }
        // 移除所有事件监听器
        this.removeAllListeners();
        this.initialized = false;
    };
    return ScenePreloader;
}(EventEmitter_1.EventEmitter));
exports.ScenePreloader = ScenePreloader;

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkSimulator = void 0;
/**
 * 网络模拟器
 * 用于模拟各种网络条件，如延迟、丢包、抖动等
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var Debug_1 = require("../utils/Debug");
/**
 * 网络模拟器
 * 用于模拟各种网络条件，如延迟、丢包、抖动等
 */
var NetworkSimulator = /** @class */ (function (_super) {
    __extends(NetworkSimulator, _super);
    /**
     * 创建网络模拟器
     * @param config 配置
     */
    function NetworkSimulator(config) {
        if (config === void 0) { config = {}; }
        var _this = _super.call(this) || this;
        /** 待处理的消息队列 */
        _this.messageQueue = [];
        /** 处理队列的定时器ID */
        _this.processTimerId = null;
        /** 当前是否断线 */
        _this.disconnected = false;
        /** 重连定时器ID */
        _this.reconnectTimerId = null;
        /** 发送的字节数 */
        _this.sentBytes = 0;
        /** 上次重置带宽计数的时间 */
        _this.lastBandwidthResetTime = 0;
        // 默认配置
        _this.config = __assign({ enabled: true, latency: 0, latencyJitter: 0, packetLoss: 0, bandwidthLimit: 0, enableRandomDisconnect: false, disconnectProbability: 0.01, reconnectTime: 3000, detailedLogging: false }, config);
        // 初始化
        _this.initialize();
        return _this;
    }
    /**
     * 初始化
     */
    NetworkSimulator.prototype.initialize = function () {
        // 启动处理队列的定时器
        this.startProcessing();
        // 初始化带宽重置时间
        this.lastBandwidthResetTime = Date.now();
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkSimulator', "\u7F51\u7EDC\u6A21\u62DF\u5668\u5DF2\u521D\u59CB\u5316: \u5EF6\u8FDF=".concat(this.config.latency, "ms, \u6296\u52A8=").concat(this.config.latencyJitter, "ms, \u4E22\u5305\u7387=").concat(this.config.packetLoss * 100, "%, \u5E26\u5BBD\u9650\u5236=").concat(this.config.bandwidthLimit, "B/s"));
        }
    };
    /**
     * 启动处理队列
     */
    NetworkSimulator.prototype.startProcessing = function () {
        var _this = this;
        if (this.processTimerId !== null) {
            return;
        }
        this.processTimerId = window.setInterval(function () {
            _this.processQueue();
        }, 10); // 每10毫秒处理一次队列
    };
    /**
     * 停止处理队列
     */
    NetworkSimulator.prototype.stopProcessing = function () {
        if (this.processTimerId !== null) {
            clearInterval(this.processTimerId);
            this.processTimerId = null;
        }
    };
    /**
     * 处理消息队列
     */
    NetworkSimulator.prototype.processQueue = function () {
        if (!this.config.enabled || this.disconnected) {
            return;
        }
        var now = Date.now();
        var toRemove = [];
        // 检查带宽限制
        if (this.config.bandwidthLimit > 0) {
            // 每秒重置带宽计数
            if (now - this.lastBandwidthResetTime > 1000) {
                this.sentBytes = 0;
                this.lastBandwidthResetTime = now;
            }
            // 如果已经超过带宽限制，则不处理
            if (this.sentBytes >= this.config.bandwidthLimit) {
                return;
            }
        }
        // 处理队列中的消息
        for (var i = 0; i < this.messageQueue.length; i++) {
            var message = this.messageQueue[i];
            // 如果消息延迟时间已到，则发送
            if (now - message.sendTime >= message.delay) {
                // 检查带宽限制
                if (this.config.bandwidthLimit > 0) {
                    // 如果发送这条消息会超过带宽限制，则跳过
                    if (this.sentBytes + message.size > this.config.bandwidthLimit) {
                        continue;
                    }
                    // 更新已发送字节数
                    this.sentBytes += message.size;
                }
                // 触发消息接收事件
                this.emit('receive', message.data);
                // 标记为待移除
                toRemove.push(i);
            }
        }
        // 从队列中移除已处理的消息
        for (var i = toRemove.length - 1; i >= 0; i--) {
            this.messageQueue.splice(toRemove[i], 1);
        }
        // 检查是否需要随机断线
        if (this.config.enableRandomDisconnect && Math.random() < this.config.disconnectProbability) {
            this.simulateDisconnect();
        }
    };
    /**
     * 模拟发送消息
     * @param data 消息数据
     * @param size 消息大小（字节）
     */
    NetworkSimulator.prototype.send = function (data, size) {
        if (size === void 0) { size = 0; }
        if (!this.config.enabled) {
            // 如果未启用模拟，则直接触发接收事件
            this.emit('receive', data);
            return;
        }
        if (this.disconnected) {
            // 如果已断线，则丢弃消息
            if (this.config.detailedLogging) {
                Debug_1.Debug.log('NetworkSimulator', '已断线，消息被丢弃');
            }
            return;
        }
        // 检查丢包
        if (Math.random() < this.config.packetLoss) {
            if (this.config.detailedLogging) {
                Debug_1.Debug.log('NetworkSimulator', '模拟丢包，消息被丢弃');
            }
            return;
        }
        // 计算延迟
        var delay = this.config.latency;
        // 添加抖动
        if (this.config.latencyJitter > 0) {
            // 生成-jitter到+jitter之间的随机数
            var jitter = (Math.random() * 2 - 1) * this.config.latencyJitter;
            delay += jitter;
        }
        // 确保延迟不为负数
        delay = Math.max(0, delay);
        // 添加到队列
        this.messageQueue.push({
            data: data,
            delay: delay,
            sendTime: Date.now(),
            size: size > 0 ? size : (typeof data === 'string' ? data.length : 0),
        });
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkSimulator', "\u6D88\u606F\u5DF2\u52A0\u5165\u961F\u5217\uFF0C\u5EF6\u8FDF=".concat(delay.toFixed(2), "ms, \u5927\u5C0F=").concat(size, "\u5B57\u8282"));
        }
    };
    /**
     * 模拟断线
     */
    NetworkSimulator.prototype.simulateDisconnect = function () {
        var _this = this;
        if (this.disconnected) {
            return;
        }
        this.disconnected = true;
        // 触发断线事件
        this.emit('disconnect');
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkSimulator', '模拟断线');
        }
        // 设置重连定时器
        this.reconnectTimerId = window.setTimeout(function () {
            _this.simulateReconnect();
        }, this.config.reconnectTime);
    };
    /**
     * 模拟重连
     */
    NetworkSimulator.prototype.simulateReconnect = function () {
        if (!this.disconnected) {
            return;
        }
        this.disconnected = false;
        // 清除重连定时器
        if (this.reconnectTimerId !== null) {
            clearTimeout(this.reconnectTimerId);
            this.reconnectTimerId = null;
        }
        // 触发重连事件
        this.emit('reconnect');
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkSimulator', '模拟重连');
        }
    };
    /**
     * 更新配置
     * @param config 新配置
     */
    NetworkSimulator.prototype.updateConfig = function (config) {
        this.config = __assign(__assign({}, this.config), config);
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkSimulator', "\u914D\u7F6E\u5DF2\u66F4\u65B0: \u5EF6\u8FDF=".concat(this.config.latency, "ms, \u6296\u52A8=").concat(this.config.latencyJitter, "ms, \u4E22\u5305\u7387=").concat(this.config.packetLoss * 100, "%, \u5E26\u5BBD\u9650\u5236=").concat(this.config.bandwidthLimit, "B/s"));
        }
    };
    /**
     * 清空消息队列
     */
    NetworkSimulator.prototype.clearQueue = function () {
        this.messageQueue = [];
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkSimulator', '消息队列已清空');
        }
    };
    /**
     * 销毁模拟器
     */
    NetworkSimulator.prototype.dispose = function () {
        this.stopProcessing();
        if (this.reconnectTimerId !== null) {
            clearTimeout(this.reconnectTimerId);
            this.reconnectTimerId = null;
        }
        this.clearQueue();
        this.removeAllListeners();
        if (this.config.detailedLogging) {
            Debug_1.Debug.log('NetworkSimulator', '网络模拟器已销毁');
        }
    };
    return NetworkSimulator;
}(EventEmitter_1.EventEmitter));
exports.NetworkSimulator = NetworkSimulator;

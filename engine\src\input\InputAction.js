"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorInputAction = exports.ValueInputAction = exports.ButtonInputAction = exports.BaseInputAction = exports.InputActionType = void 0;
/**
 * 输入动作
 * 表示一个可以被触发的输入动作
 */
var EventEmitter_1 = require("../utils/EventEmitter");
/**
 * 输入动作类型
 */
var InputActionType;
(function (InputActionType) {
    /** 按钮动作 */
    InputActionType["BUTTON"] = "button";
    /** 值动作 */
    InputActionType["VALUE"] = "value";
    /** 向量动作 */
    InputActionType["VECTOR"] = "vector";
})(InputActionType || (exports.InputActionType = InputActionType = {}));
/**
 * 输入动作基类
 */
var BaseInputAction = /** @class */ (function () {
    /**
     * 创建输入动作
     * @param name 动作名称
     * @param type 动作类型
     */
    function BaseInputAction(name, type) {
        /** 是否发生变化 */
        this.changed = false;
        /** 事件发射器 */
        this.eventEmitter = new EventEmitter_1.EventEmitter();
        this.name = name;
        this.type = type;
    }
    /**
     * 获取动作名称
     * @returns 动作名称
     */
    BaseInputAction.prototype.getName = function () {
        return this.name;
    };
    /**
     * 获取动作类型
     * @returns 动作类型
     */
    BaseInputAction.prototype.getType = function () {
        return this.type;
    };
    /**
     * 更新动作状态
     * @param value 动作值
     */
    BaseInputAction.prototype.update = function (value) {
        this.previousValue = this.value;
        this.value = value;
        this.changed = this.previousValue !== this.value;
        // 触发事件
        if (this.changed) {
            this.eventEmitter.emit('changed', { value: this.value, previousValue: this.previousValue });
        }
    };
    /**
     * 检查动作状态是否发生变化
     * @returns 是否发生变化
     */
    BaseInputAction.prototype.hasChanged = function () {
        return this.changed;
    };
    /**
     * 获取动作值
     * @returns 动作值
     */
    BaseInputAction.prototype.getValue = function () {
        return this.value;
    };
    /**
     * 重置动作状态
     */
    BaseInputAction.prototype.reset = function () {
        this.previousValue = this.value;
        this.changed = false;
    };
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    BaseInputAction.prototype.addEventListener = function (event, callback) {
        this.eventEmitter.on(event, callback);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    BaseInputAction.prototype.removeEventListener = function (event, callback) {
        this.eventEmitter.off(event, callback);
    };
    return BaseInputAction;
}());
exports.BaseInputAction = BaseInputAction;
/**
 * 按钮动作
 */
var ButtonInputAction = /** @class */ (function (_super) {
    __extends(ButtonInputAction, _super);
    /**
     * 创建按钮动作
     * @param name 动作名称
     */
    function ButtonInputAction(name) {
        var _this = _super.call(this, name, InputActionType.BUTTON) || this;
        /** 是否按下 */
        _this.pressed = false;
        /** 是否刚按下 */
        _this.justPressed = false;
        /** 是否刚释放 */
        _this.justReleased = false;
        _this.value = false;
        _this.previousValue = false;
        return _this;
    }
    /**
     * 更新动作状态
     * @param value 动作值
     */
    ButtonInputAction.prototype.update = function (value) {
        this.previousValue = this.value;
        this.value = value;
        this.changed = this.previousValue !== this.value;
        this.pressed = this.value;
        this.justPressed = this.value && !this.previousValue;
        this.justReleased = !this.value && this.previousValue;
        // 触发事件
        if (this.changed) {
            this.eventEmitter.emit('changed', { value: this.value, previousValue: this.previousValue });
        }
        if (this.justPressed) {
            this.eventEmitter.emit('started', { value: this.value });
        }
        if (this.justReleased) {
            this.eventEmitter.emit('ended', { value: this.value });
        }
    };
    /**
     * 检查按钮是否按下
     * @returns 是否按下
     */
    ButtonInputAction.prototype.isPressed = function () {
        return this.pressed;
    };
    /**
     * 检查按钮是否刚按下
     * @returns 是否刚按下
     */
    ButtonInputAction.prototype.isJustPressed = function () {
        return this.justPressed;
    };
    /**
     * 检查按钮是否刚释放
     * @returns 是否刚释放
     */
    ButtonInputAction.prototype.isJustReleased = function () {
        return this.justReleased;
    };
    /**
     * 重置动作状态
     */
    ButtonInputAction.prototype.reset = function () {
        _super.prototype.reset.call(this);
        this.justPressed = false;
        this.justReleased = false;
    };
    return ButtonInputAction;
}(BaseInputAction));
exports.ButtonInputAction = ButtonInputAction;
/**
 * 值动作
 */
var ValueInputAction = /** @class */ (function (_super) {
    __extends(ValueInputAction, _super);
    /**
     * 创建值动作
     * @param name 动作名称
     */
    function ValueInputAction(name) {
        var _this = _super.call(this, name, InputActionType.VALUE) || this;
        _this.value = 0;
        _this.previousValue = 0;
        return _this;
    }
    return ValueInputAction;
}(BaseInputAction));
exports.ValueInputAction = ValueInputAction;
/**
 * 向量动作
 */
var VectorInputAction = /** @class */ (function (_super) {
    __extends(VectorInputAction, _super);
    /**
     * 创建向量动作
     * @param name 动作名称
     */
    function VectorInputAction(name) {
        var _this = _super.call(this, name, InputActionType.VECTOR) || this;
        _this.value = [0, 0];
        _this.previousValue = [0, 0];
        return _this;
    }
    /**
     * 更新动作状态
     * @param value 动作值
     */
    VectorInputAction.prototype.update = function (value) {
        this.previousValue = this.value;
        this.value = value;
        this.changed = this.previousValue[0] !== this.value[0] || this.previousValue[1] !== this.value[1];
    };
    /**
     * 获取X轴值
     * @returns X轴值
     */
    VectorInputAction.prototype.getX = function () {
        return this.value[0];
    };
    /**
     * 获取Y轴值
     * @returns Y轴值
     */
    VectorInputAction.prototype.getY = function () {
        return this.value[1];
    };
    /**
     * 获取向量长度
     * @returns 向量长度
     */
    VectorInputAction.prototype.getLength = function () {
        return Math.sqrt(this.value[0] * this.value[0] + this.value[1] * this.value[1]);
    };
    /**
     * 获取向量方向
     * @returns 向量方向（弧度）
     */
    VectorInputAction.prototype.getDirection = function () {
        return Math.atan2(this.value[1], this.value[0]);
    };
    return VectorInputAction;
}(BaseInputAction));
exports.VectorInputAction = VectorInputAction;
